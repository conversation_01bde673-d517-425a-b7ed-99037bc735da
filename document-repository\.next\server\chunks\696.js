"use strict";exports.id=696,exports.ids=[696],exports.modules={1696:(a,b,c)=>{c.d(b,{toFormData:()=>l});var d=c(14334),e=c(70451);let f=0,g={START_BOUNDARY:f++,HEADER_FIELD_START:f++,HEADER_FIELD:f++,HEADER_VALUE_START:f++,HEADER_VALUE:f++,HEADER_VALUE_ALMOST_DONE:f++,HEADERS_ALMOST_DONE:f++,PART_DATA_START:f++,PART_DATA:f++,END:f++},h={PART_BOUNDARY:1,LAST_BOUNDARY:2},i=a=>32|a,j=()=>{};class k{constructor(a){this.index=0,this.flags=0,this.onHeaderEnd=j,this.onHeaderField=j,this.onHeadersEnd=j,this.onHeaderValue=j,this.onPartBegin=j,this.onPartData=j,this.onPartEnd=j,this.boundaryChars={};let b=new Uint8Array((a="\r\n--"+a).length);for(let c=0;c<a.length;c++)b[c]=a.charCodeAt(c),this.boundaryChars[b[c]]=!0;this.boundary=b,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=g.START_BOUNDARY}write(a){let b,c,d=0,e=a.length,f=this.index,{lookbehind:j,boundary:k,boundaryChars:l,index:m,state:n,flags:o}=this,p=this.boundary.length,q=p-1,r=a.length,s=a=>{this[a+"Mark"]=d},t=a=>{delete this[a+"Mark"]},u=(a,b,c,d)=>{(void 0===b||b!==c)&&this[a](d&&d.subarray(b,c))},v=(b,c)=>{let e=b+"Mark";e in this&&(c?(u(b,this[e],d,a),delete this[e]):(u(b,this[e],a.length,a),this[e]=0))};for(d=0;d<e;d++)switch(b=a[d],n){case g.START_BOUNDARY:if(m===k.length-2){if(45===b)o|=h.LAST_BOUNDARY;else if(13!==b)return;m++;break}if(m-1==k.length-2){if(o&h.LAST_BOUNDARY&&45===b)n=g.END,o=0;else{if(o&h.LAST_BOUNDARY||10!==b)return;m=0,u("onPartBegin"),n=g.HEADER_FIELD_START}break}b!==k[m+2]&&(m=-2),b===k[m+2]&&m++;break;case g.HEADER_FIELD_START:n=g.HEADER_FIELD,s("onHeaderField"),m=0;case g.HEADER_FIELD:if(13===b){t("onHeaderField"),n=g.HEADERS_ALMOST_DONE;break}if(m++,45===b)break;if(58===b){if(1===m)return;v("onHeaderField",!0),n=g.HEADER_VALUE_START;break}if((c=i(b))<97||c>122)return;break;case g.HEADER_VALUE_START:if(32===b)break;s("onHeaderValue"),n=g.HEADER_VALUE;case g.HEADER_VALUE:13===b&&(v("onHeaderValue",!0),u("onHeaderEnd"),n=g.HEADER_VALUE_ALMOST_DONE);break;case g.HEADER_VALUE_ALMOST_DONE:if(10!==b)return;n=g.HEADER_FIELD_START;break;case g.HEADERS_ALMOST_DONE:if(10!==b)return;u("onHeadersEnd"),n=g.PART_DATA_START;break;case g.PART_DATA_START:n=g.PART_DATA,s("onPartData");case g.PART_DATA:if(f=m,0===m){for(d+=q;d<r&&!(a[d]in l);)d+=p;d-=q,b=a[d]}if(m<k.length)k[m]===b?(0===m&&v("onPartData",!0),m++):m=0;else if(m===k.length)m++,13===b?o|=h.PART_BOUNDARY:45===b?o|=h.LAST_BOUNDARY:m=0;else if(m-1===k.length)if(o&h.PART_BOUNDARY){if(m=0,10===b){o&=~h.PART_BOUNDARY,u("onPartEnd"),u("onPartBegin"),n=g.HEADER_FIELD_START;break}}else o&h.LAST_BOUNDARY&&45===b?(u("onPartEnd"),n=g.END,o=0):m=0;m>0?j[m-1]=b:f>0&&(u("onPartData",0,f,new Uint8Array(j.buffer,j.byteOffset,j.byteLength)),f=0,s("onPartData"),d--);break;case g.END:break;default:throw Error(`Unexpected state entered: ${n}`)}v("onHeaderField"),v("onHeaderValue"),v("onPartData"),this.index=m,this.state=n,this.flags=o}end(){if(this.state===g.HEADER_FIELD_START&&0===this.index||this.state===g.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==g.END)throw Error("MultipartParser.end(): stream ended unexpectedly")}}async function l(a,b){let c,f,g,h,i,j;if(!/multipart/i.test(b))throw TypeError("Failed to fetch");let l=b.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!l)throw TypeError("no or bad content-type header, no multipart boundary");let m=new k(l[1]||l[2]),n=[],o=new e.fS,p=a=>{g+=t.decode(a,{stream:!0})},q=a=>{n.push(a)},r=()=>{let a=new d.ZH(n,j,{type:i});o.append(h,a)},s=()=>{o.append(h,g)},t=new TextDecoder("utf-8");for await(let b of(t.decode(),m.onPartBegin=function(){m.onPartData=p,m.onPartEnd=s,c="",f="",g="",h="",i="",j=null,n.length=0},m.onHeaderField=function(a){c+=t.decode(a,{stream:!0})},m.onHeaderValue=function(a){f+=t.decode(a,{stream:!0})},m.onHeaderEnd=function(){if(f+=t.decode(),"content-disposition"===(c=c.toLowerCase())){let a=f.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);a&&(h=a[2]||a[3]||""),(j=function(a){let b=a.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!b)return;let c=b[2]||b[3]||"",d=c.slice(c.lastIndexOf("\\")+1);return(d=d.replace(/%22/g,'"')).replace(/&#(\d{4});/g,(a,b)=>String.fromCharCode(b))}(f))&&(m.onPartData=q,m.onPartEnd=r)}else"content-type"===c&&(i=f);f="",c=""},a))m.write(b);return m.end(),o}}};