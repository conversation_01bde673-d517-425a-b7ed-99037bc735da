# Deployment Guide

This guide covers deploying the Document Repository application to various platforms.

## Prerequisites

Before deploying, ensure you have:

1. **Supabase Project** - Set up and configured with the database schema
2. **Google Drive API** - Configured with proper credentials
3. **Environment Variables** - All required variables ready for production
4. **Domain Name** - (Optional) For custom domain deployment

## Vercel Deployment (Recommended)

Vercel provides the easiest deployment experience for Next.js applications.

### Step 1: Prepare Your Repository

1. Push your code to GitHub, GitLab, or Bitbucket
2. Ensure your `package.json` has the correct build scripts
3. Test your build locally: `npm run build`

### Step 2: Connect to Vercel

1. Go to [vercel.com](https://vercel.com) and sign up/login
2. Click "New Project"
3. Import your repository
4. Vercel will automatically detect it's a Next.js project

### Step 3: Configure Environment Variables

In the Vercel dashboard, add these environment variables:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Google Drive API
GOOGLE_DRIVE_CLIENT_ID=your_client_id
GOOGLE_DRIVE_CLIENT_SECRET=your_client_secret
GOOGLE_DRIVE_REDIRECT_URI=https://your-domain.vercel.app/api/auth/callback
GOOGLE_DRIVE_REFRESH_TOKEN=your_refresh_token
GOOGLE_DRIVE_FOLDER_ID=your_folder_id

# Application
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your_production_secret

# File Upload
MAX_FILE_SIZE=52428800  # 50MB for production
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif,zip,rar

# Security
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000
```

### Step 4: Deploy

1. Click "Deploy"
2. Vercel will build and deploy your application
3. You'll get a URL like `https://your-app.vercel.app`

### Step 5: Update OAuth Settings

Update your Google OAuth settings:
1. Go to Google Cloud Console
2. Update authorized redirect URIs to include your Vercel URL
3. Update Supabase Auth settings if needed

## Netlify Deployment

### Step 1: Build Settings

Create a `netlify.toml` file in your project root:

```toml
[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### Step 2: Deploy

1. Connect your repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `.next`
4. Add environment variables in Netlify dashboard
5. Deploy

## Docker Deployment

### Step 1: Create Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Step 2: Build and Run

```bash
# Build the image
docker build -t document-repository .

# Run the container
docker run -p 3000:3000 --env-file .env.local document-repository
```

## AWS Deployment

### Using AWS Amplify

1. Connect your repository to AWS Amplify
2. Configure build settings:
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```
3. Add environment variables in Amplify console
4. Deploy

### Using EC2

1. Launch an EC2 instance with Node.js
2. Clone your repository
3. Install dependencies: `npm install`
4. Build the application: `npm run build`
5. Use PM2 for process management:
   ```bash
   npm install -g pm2
   pm2 start npm --name "document-repo" -- start
   pm2 startup
   pm2 save
   ```

## Database Migration

### Production Database Setup

1. **Supabase Production Project**:
   - Create a new Supabase project for production
   - Run the schema from `supabase-schema.sql`
   - Configure Row Level Security policies
   - Set up proper backup schedules

2. **Environment-Specific Configurations**:
   ```sql
   -- Enable additional security for production
   ALTER DATABASE postgres SET log_statement = 'all';
   ALTER DATABASE postgres SET log_min_duration_statement = 1000;
   ```

## Security Considerations

### Production Security Checklist

- [ ] Use strong, unique secrets for all environment variables
- [ ] Enable HTTPS (handled automatically by Vercel/Netlify)
- [ ] Configure proper CORS settings
- [ ] Set up monitoring and alerting
- [ ] Enable database backups
- [ ] Configure rate limiting
- [ ] Set up error tracking (Sentry recommended)
- [ ] Enable audit logging
- [ ] Configure proper file upload limits
- [ ] Set up security headers (already implemented)

### Environment Variables Security

- Never commit `.env` files to version control
- Use different secrets for each environment
- Rotate secrets regularly
- Use secret management services for sensitive data

## Monitoring and Maintenance

### Recommended Monitoring Tools

1. **Application Monitoring**:
   - Vercel Analytics (if using Vercel)
   - Google Analytics
   - Sentry for error tracking

2. **Database Monitoring**:
   - Supabase built-in monitoring
   - Custom alerts for query performance

3. **File Storage Monitoring**:
   - Google Drive API quota monitoring
   - Storage usage tracking

### Backup Strategy

1. **Database Backups**:
   - Supabase automatic backups
   - Custom backup scripts for critical data

2. **File Backups**:
   - Google Drive provides redundancy
   - Consider additional backup to another cloud provider

### Performance Optimization

1. **Caching**:
   - Enable Next.js caching
   - Use CDN for static assets
   - Implement Redis for session storage (optional)

2. **Database Optimization**:
   - Monitor query performance
   - Add indexes as needed
   - Optimize large file queries

## Troubleshooting

### Common Deployment Issues

1. **Build Failures**:
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check for TypeScript errors

2. **Environment Variable Issues**:
   - Verify all required variables are set
   - Check variable names for typos
   - Ensure secrets are properly encoded

3. **Database Connection Issues**:
   - Verify Supabase URL and keys
   - Check network connectivity
   - Verify database schema is applied

4. **Google Drive API Issues**:
   - Check API quotas and limits
   - Verify OAuth configuration
   - Check redirect URI settings

### Getting Help

- Check the application logs in your deployment platform
- Review Supabase logs for database issues
- Check Google Cloud Console for API issues
- Open an issue on the project repository

## Rollback Strategy

### Quick Rollback Steps

1. **Vercel**: Use the deployments tab to rollback to previous version
2. **Docker**: Keep previous image tags and redeploy
3. **Database**: Use Supabase point-in-time recovery if needed

### Rollback Checklist

- [ ] Identify the last known good deployment
- [ ] Check for database schema changes
- [ ] Verify environment variable compatibility
- [ ] Test critical functionality after rollback
- [ ] Notify users if necessary

## Post-Deployment

### Initial Setup

1. Create the first admin user
2. Test file upload functionality
3. Verify Google Drive integration
4. Test authentication flows
5. Check admin dashboard functionality

### Ongoing Maintenance

- Monitor application performance
- Review security logs regularly
- Update dependencies monthly
- Test backup and recovery procedures
- Review and rotate secrets quarterly
