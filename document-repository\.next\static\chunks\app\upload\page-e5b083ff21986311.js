(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[994],{1349:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5155),r=t(2115),l=t(5695),i=t(4107),n=t(7338),o=t(24),d=t(9434);function c(e){let{onUpload:s,maxFiles:t=5,maxFileSize:l=0xa00000,allowedFileTypes:c=["pdf","doc","docx","txt","jpg","jpeg","png","gif","zip","rar"],disabled:m=!1}=e,[p,u]=(0,r.useState)([]),[x,g]=(0,r.useState)([]),h=(0,r.useCallback)((e,s)=>{g([]);let a=[];s.forEach(e=>{let{file:s,errors:t}=e;t.forEach(e=>{"file-too-large"===e.code?a.push("".concat(s.name,": File is too large (max ").concat((0,d.v7)(l),")")):"file-invalid-type"===e.code?a.push("".concat(s.name,": File type not allowed")):a.push("".concat(s.name,": ").concat(e.message))})});let r=[];if(e.forEach(e=>(0,d.zf)(e,c)?(0,d.Gg)(e,l)?void r.push(e):void a.push("".concat(e.name,": File is too large (max ").concat((0,d.v7)(l),")")):void a.push("".concat(e.name,": File type not allowed. Allowed types: ").concat(c.join(", ")))),p.length+r.length>t){a.push("Cannot upload more than ".concat(t," files at once")),g(a);return}let i=r.map(e=>({file:e,metadata:{description:"",tags:[],isPublic:!1}}));u(e=>[...e,...i]),g(a)},[p,t,l,c]),{getRootProps:f,getInputProps:j,isDragActive:b}=(0,i.VB)({onDrop:h,disabled:m,maxFiles:t,maxSize:l,accept:{"application/pdf":[".pdf"],"application/msword":[".doc"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"image/jpeg":[".jpg",".jpeg"],"image/png":[".png"],"image/gif":[".gif"],"application/zip":[".zip"],"application/x-rar-compressed":[".rar"]}}),y=(e,s)=>{u(t=>t.map((t,a)=>a===e?{...t,metadata:{...t.metadata,...s}}:t))};return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{...f(),className:"\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n          ".concat(b?"border-indigo-500 bg-indigo-50":"border-gray-300 hover:border-gray-400","\n          ").concat(m?"opacity-50 cursor-not-allowed":"","\n        "),children:[(0,a.jsx)("input",{...j()}),(0,a.jsx)(n.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:b?"Drop the files here...":"Drag and drop files here, or click to select files"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Max ",t," files, up to ",(0,d.v7)(l)," each"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Supported: ",c.join(", ")]})]}),x.length>0&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-md",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-red-800",children:"Upload Errors:"}),(0,a.jsx)("ul",{className:"mt-2 text-sm text-red-700 list-disc list-inside",children:x.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]}),p.length>0&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-900 mb-4",children:["Selected Files (",p.length,")"]}),(0,a.jsx)("div",{className:"space-y-4",children:p.map((e,s)=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.file.name}),(0,a.jsxs)("span",{className:"ml-2 text-xs text-gray-500",children:["(",(0,d.v7)(e.file.size),")"]})]}),(0,a.jsxs)("div",{className:"mt-3 space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-700",children:"Description (optional)"}),(0,a.jsx)("input",{type:"text",className:"mt-1 block w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500",placeholder:"Enter file description",value:e.metadata.description,onChange:e=>y(s,{description:e.target.value})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-700",children:"Tags (comma-separated)"}),(0,a.jsx)("input",{type:"text",className:"mt-1 block w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500",placeholder:"e.g., document, important, project",value:e.metadata.tags.join(", "),onChange:e=>y(s,{tags:e.target.value.split(",").map(e=>e.trim()).filter(Boolean)})})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"public-".concat(s),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",checked:e.metadata.isPublic,onChange:e=>y(s,{isPublic:e.target.checked})}),(0,a.jsx)("label",{htmlFor:"public-".concat(s),className:"ml-2 block text-xs text-gray-700",children:"Make this file publicly accessible"})]})]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>{u(e=>e.filter((e,t)=>t!==s))},className:"ml-4 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(o.A,{className:"h-5 w-5"})})]})},s))}),(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsxs)("button",{type:"button",onClick:()=>{if(0!==p.length)s(p.map(e=>e.file),p.map(e=>e.metadata)),u([])},disabled:m||0===p.length,className:"px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",children:["Upload ",p.length," File",1!==p.length?"s":""]})})]})]})}function m(){let[e,s]=(0,r.useState)(!1),[t,i]=(0,r.useState)([]),[n,o]=(0,r.useState)(""),d=(0,l.useRouter)(),m=async(e,a)=>{s(!0),o(""),i(e.map((e,s)=>({id:"".concat(Date.now(),"-").concat(s),filename:e.name,progress:0,status:"pending"})));try{for(let s=0;s<e.length;s++){let t=e[s],r=a[s];i(e=>e.map((e,t)=>t===s?{...e,status:"uploading",progress:0}:e));let l=new FormData;l.append("file",t),l.append("description",r.description),l.append("tags",r.tags.join(",")),l.append("isPublic",r.isPublic.toString());try{let e=await fetch("/api/files/upload",{method:"POST",body:l}),t=await e.json();if(!e.ok)throw Error(t.error||"Upload failed");i(e=>e.map((e,t)=>t===s?{...e,status:"completed",progress:100}:e))}catch(e){console.error("Error uploading ".concat(t.name,":"),e),i(t=>t.map((t,a)=>a===s?{...t,status:"error",error:e instanceof Error?e.message:"Upload failed"}:t))}}t.some(e=>"error"===e.status)||setTimeout(()=>{d.push("/dashboard")},2e3)}catch(e){console.error("Upload error:",e),o(e instanceof Error?e.message:"Upload failed")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Upload Files"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Upload your documents to the repository. Files will be stored securely in Google Drive."})]}),(0,a.jsxs)("div",{className:"p-6",children:[n&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-md",children:(0,a.jsx)("div",{className:"text-sm text-red-700",children:n})}),(0,a.jsx)(c,{onUpload:m,disabled:e,maxFiles:5,maxFileSize:0xa00000,allowedFileTypes:["pdf","doc","docx","txt","jpg","jpeg","png","gif","zip","rar"]}),t.length>0&&(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Upload Progress"}),(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.filename}),(0,a.jsxs)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"error"===e.status?"bg-red-100 text-red-800":"uploading"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:["completed"===e.status&&"Completed","error"===e.status&&"Error","uploading"===e.status&&"Uploading...","pending"===e.status&&"Pending"]})]}),"uploading"===e.status&&(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})}),"completed"===e.status&&(0,a.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{className:"text-sm",children:"Upload completed successfully"})]}),"error"===e.status&&e.error&&(0,a.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),(0,a.jsx)("span",{className:"text-sm",children:e.error})]})]},e.id))}),t.every(e=>"completed"===e.status)&&(0,a.jsx)("div",{className:"mt-6 p-4 bg-green-50 border border-green-200 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{className:"text-sm text-green-700",children:"All files uploaded successfully! Redirecting to dashboard..."})]})})]})]})]})})})}},2595:(e,s,t)=>{Promise.resolve().then(t.bind(t,1349))},9434:(e,s,t)=>{"use strict";function a(e){if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][s]}function r(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(e){return e.startsWith("image/")?"\uD83D\uDDBC️":e.startsWith("video/")?"\uD83C\uDFA5":e.startsWith("audio/")?"\uD83C\uDFB5":e.includes("pdf")?"\uD83D\uDCC4":e.includes("word")||e.includes("document")?"\uD83D\uDCDD":e.includes("excel")||e.includes("spreadsheet")?"\uD83D\uDCCA":e.includes("powerpoint")||e.includes("presentation")?"\uD83D\uDCC8":e.includes("zip")||e.includes("rar")||e.includes("archive")?"\uD83D\uDDDC️":e.includes("text/")?"\uD83D\uDCC4":"\uD83D\uDCC1"}function i(e,s){var t;let a=null==(t=e.name.split(".").pop())?void 0:t.toLowerCase();return s.includes(a||"")}function n(e,s){return e.size<=s}function o(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}t.d(s,{B9:()=>o,Gg:()=>n,I3:()=>l,Yq:()=>r,v7:()=>a,zf:()=>i})}},e=>{e.O(0,[453,441,964,358],()=>e(e.s=2595)),_N_E=e.O()}]);