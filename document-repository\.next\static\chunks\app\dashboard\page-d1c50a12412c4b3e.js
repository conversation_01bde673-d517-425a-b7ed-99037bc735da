(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{72:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(5155),l=s(2115),r=s(6874),i=s.n(r),n=s(7338),d=s(9064);let o=l.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?l.createElement("title",{id:a},s):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))});var c=s(8346),m=s(9413);let u=l.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?l.createElement("title",{id:a},s):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});var x=s(9434),h=s(2099);function g(){let[e,t]=(0,l.useState)(null),[s,r]=(0,l.useState)([]),[g,f]=(0,l.useState)(!0),[p,v]=(0,l.useState)(""),[j,w]=(0,l.useState)("all"),[b,y]=(0,l.useState)("uploaded_at"),[N,E]=(0,l.useState)("desc"),[k,_]=(0,l.useState)(1),[L,A]=(0,l.useState)(1),C=(0,h.i7)();(0,l.useEffect)(()=>{S(),B()},[k,b,N,p,j]);let S=async()=>{try{let{data:{user:e}}=await C.auth.getUser();if(e){let s=await fetch("/api/users/".concat(e.id));if(s.ok){let e=await s.json();t(e.data)}}}catch(e){console.error("Error loading user data:",e)}},B=async()=>{try{f(!0);let t=new URLSearchParams({page:k.toString(),limit:"10",sortBy:b,sortOrder:N,...p&&{search:p},..."all"!==j&&{fileType:j}}),s=await fetch("/api/files/upload?".concat(t));if(s.ok){var e;let t=await s.json();r(t.data),A((null==(e=t.pagination)?void 0:e.totalPages)||1)}}catch(e){console.error("Error loading files:",e)}finally{f(!1)}},F=async(e,t)=>{try{let t=await fetch("/api/files/".concat(e,"/download"));if(t.ok){let e=await t.json();e.success&&e.data.downloadUrl&&window.open(e.data.downloadUrl,"_blank")}else alert("Failed to download file")}catch(e){console.error("Error downloading file:",e),alert("Failed to download file")}},M=async e=>{if(confirm("Are you sure you want to delete this file?"))try{(await fetch("/api/files/".concat(e),{method:"DELETE"})).ok?r(t=>t.filter(t=>t.id!==e)):alert("Failed to delete file")}catch(e){console.error("Error deleting file:",e),alert("Failed to delete file")}},P=s.filter(e=>{var t;let s=e.original_filename.toLowerCase().includes(p.toLowerCase())||(null==(t=e.description)?void 0:t.toLowerCase().includes(p.toLowerCase())),a="all"===j||e.file_type===j;return s&&a});return g&&0===s.length?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading your files..."})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Welcome back",(null==e?void 0:e.full_name)?", ".concat(e.full_name):"","!"]}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Manage your documents and files"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)(i(),{href:"/upload",className:"inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 mr-2"}),"Upload Files"]}),(null==e?void 0:e.role)==="admin"&&(0,a.jsx)(i(),{href:"/admin",className:"inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:"Admin Panel"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Your Files"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s.length})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"w-8 h-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Storage Used"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,x.v7)(s.reduce((e,t)=>e+t.file_size,0))})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o,{className:"w-8 h-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Downloads"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s.reduce((e,t)=>e+t.download_count,0)})]})]})})]}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search your files...",className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500",value:p,onChange:e=>v(e.target.value)})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500",value:j,onChange:e=>w(e.target.value),children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"pdf",children:"PDF"}),(0,a.jsx)("option",{value:"doc",children:"Documents"}),(0,a.jsx)("option",{value:"jpg",children:"Images"}),(0,a.jsx)("option",{value:"zip",children:"Archives"})]}),(0,a.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500",value:"".concat(b,"-").concat(N),onChange:e=>{let[t,s]=e.target.value.split("-");y(t),E(s)},children:[(0,a.jsx)("option",{value:"uploaded_at-desc",children:"Newest First"}),(0,a.jsx)("option",{value:"uploaded_at-asc",children:"Oldest First"}),(0,a.jsx)("option",{value:"original_filename-asc",children:"Name A-Z"}),(0,a.jsx)("option",{value:"original_filename-desc",children:"Name Z-A"}),(0,a.jsx)("option",{value:"file_size-desc",children:"Largest First"}),(0,a.jsx)("option",{value:"file_size-asc",children:"Smallest First"})]})]})]})}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Your Files (",P.length,")"]})}),0===P.length?(0,a.jsxs)("div",{className:"px-6 py-12 text-center",children:[(0,a.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No files found"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:p||"all"!==j?"Try adjusting your search or filter criteria.":"Get started by uploading your first file."}),!p&&"all"===j&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)(i(),{href:"/upload",className:"inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 mr-2"}),"Upload Files"]})})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:P.map(e=>(0,a.jsxs)("div",{className:"px-6 py-4 flex items-center justify-between hover:bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center flex-1",children:[(0,a.jsx)("span",{className:"text-2xl mr-4",children:(0,x.I3)(e.mime_type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.original_filename}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[(0,x.v7)(e.file_size)," • ",(0,x.Yq)(e.uploaded_at)]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),e.tags&&e.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:e.tags.map((e,t)=>(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full",children:e},t))})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.is_public?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.is_public?"Public":"Private"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.download_count," downloads"]}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)("button",{onClick:()=>F(e.id,e.original_filename),className:"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded",title:"Download",children:(0,a.jsx)(o,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>M(e.id),className:"p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded",title:"Delete",children:(0,a.jsx)(u,{className:"w-4 h-4"})})]})]})]},e.id))}),L>1&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",k," of ",L]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>_(e=>Math.max(1,e-1)),disabled:1===k,className:"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>_(e=>Math.min(L,e+1)),disabled:k===L,className:"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})]})})}},2099:(e,t,s)=>{"use strict";s.d(t,{i7:()=>n});var a=s(822),l=s(9509);let r=l.env.NEXT_PUBLIC_SUPABASE_URL,i=l.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;l.env.SUPABASE_SERVICE_ROLE_KEY;let n=()=>(0,a.createBrowserClient)(r,i)},5300:(e,t,s)=>{Promise.resolve().then(s.bind(s,72))},7338:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(2115);let l=a.forwardRef(function(e,t){let{title:s,titleId:l,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},r),s?a.createElement("title",{id:l},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})},8346:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(2115);let l=a.forwardRef(function(e,t){let{title:s,titleId:l,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},r),s?a.createElement("title",{id:l},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},9064:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(2115);let l=a.forwardRef(function(e,t){let{title:s,titleId:l,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},r),s?a.createElement("title",{id:l},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},9413:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(2115);let l=a.forwardRef(function(e,t){let{title:s,titleId:l,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},r),s?a.createElement("title",{id:l},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},9434:(e,t,s)=>{"use strict";function a(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]}function l(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}function r(e){return e.startsWith("image/")?"\uD83D\uDDBC️":e.startsWith("video/")?"\uD83C\uDFA5":e.startsWith("audio/")?"\uD83C\uDFB5":e.includes("pdf")?"\uD83D\uDCC4":e.includes("word")||e.includes("document")?"\uD83D\uDCDD":e.includes("excel")||e.includes("spreadsheet")?"\uD83D\uDCCA":e.includes("powerpoint")||e.includes("presentation")?"\uD83D\uDCC8":e.includes("zip")||e.includes("rar")||e.includes("archive")?"\uD83D\uDDDC️":e.includes("text/")?"\uD83D\uDCC4":"\uD83D\uDCC1"}function i(e,t){var s;let a=null==(s=e.name.split(".").pop())?void 0:s.toLowerCase();return t.includes(a||"")}function n(e,t){return e.size<=t}function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}s.d(t,{B9:()=>d,Gg:()=>n,I3:()=>r,Yq:()=>l,v7:()=>a,zf:()=>i})}},e=>{e.O(0,[874,822,441,964,358],()=>e(e.s=5300)),_N_E=e.O()}]);