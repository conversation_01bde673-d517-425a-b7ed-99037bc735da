import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  const response = new NextResponse();
  const supabase = createRouteHandlerClient(request, response);

  try {
    await supabase.auth.signOut();
    
    return NextResponse.json({
      success: true,
      message: 'Signed out successfully'
    });
  } catch (error) {
    console.error('Sign out error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to sign out' 
      },
      { status: 500 }
    );
  }
}
