const { google } = require('googleapis');

// Replace these with your actual credentials
const CLIENT_ID = '881878955396-uf6u5s4qhuaffap1ba19qpa26iedve0l.apps.googleusercontent.com';
const CLIENT_SECRET = 'YOUR_CLIENT_SECRET_HERE'; // Replace with your actual client secret
const REDIRECT_URI = 'http://localhost:3000/oauth/callback';

const oauth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);

const scopes = ['https://www.googleapis.com/auth/drive.file'];

if (process.argv.length < 3) {
  // Generate authorization URL
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    prompt: 'consent'
  });
  
  console.log('\n🔗 Step 1: Visit this URL to authorize the application:');
  console.log(authUrl);
  console.log('\n📋 Step 2: After authorization, copy the code from the redirect URL');
  console.log('💻 Step 3: Run: node get-refresh-token.js YOUR_CODE_HERE');
  console.log('\n⚠️  Make sure your development server is running on localhost:3000');
} else {
  // Exchange code for tokens
  const code = process.argv[2];
  
  oauth2Client.getToken(code, (err, token) => {
    if (err) {
      console.error('❌ Error retrieving access token:', err);
      return;
    }
    
    console.log('\n✅ Success! Your refresh token is:');
    console.log('🔑', token.refresh_token);
    console.log('\n📝 Add this to your .env.local file as:');
    console.log(`GOOGLE_DRIVE_REFRESH_TOKEN=${token.refresh_token}`);
  });
}
