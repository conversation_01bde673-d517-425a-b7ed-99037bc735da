# Document Repository

A comprehensive, secure document repository system with Google Drive integration, built with Next.js, TypeScript, Supabase, and Tailwind CSS.

## Features

- 🔐 **Secure Authentication** - User registration/login with Supabase Auth
- 📁 **File Management** - Upload, organize, and manage documents
- ☁️ **Google Drive Integration** - Files stored securely in Google Drive
- 👥 **Role-Based Access Control** - Admin and user roles with different permissions
- 🔍 **Advanced Search & Filtering** - Find files quickly with powerful search
- 📱 **Responsive Design** - Works seamlessly on desktop and mobile
- 🛡️ **Security Features** - File validation, rate limiting, CSRF protection
- 📊 **Admin Dashboard** - Comprehensive admin panel for system management
- 🏷️ **File Tagging** - Organize files with custom tags
- 📈 **Analytics** - Track file downloads and usage statistics

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **File Storage**: Google Drive API
- **Testing**: Jest, React Testing Library
- **Deployment**: Vercel (recommended)

## Prerequisites

Before you begin, ensure you have:

- Node.js 18+ installed
- A Supabase account and project
- A Google Cloud Platform account with Drive API enabled
- Git installed

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd document-repository
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   Fill in your environment variables (see [Environment Setup](#environment-setup))

4. **Set up the database**
   - Run the SQL schema in your Supabase project (see `supabase-schema.sql`)

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Environment Setup

### Supabase Configuration

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your keys
3. Run the SQL schema from `supabase-schema.sql` in the SQL Editor

### Google Drive API Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable the Google Drive API
4. Create credentials (OAuth 2.0 Client ID)
5. Set up OAuth consent screen
6. Add authorized redirect URIs: `http://localhost:3000/api/auth/callback`

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google Drive API Configuration
GOOGLE_DRIVE_CLIENT_ID=your_google_drive_client_id
GOOGLE_DRIVE_CLIENT_SECRET=your_google_drive_client_secret
GOOGLE_DRIVE_REDIRECT_URI=your_google_drive_redirect_uri
GOOGLE_DRIVE_REFRESH_TOKEN=your_google_drive_refresh_token
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id

# Application Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
NODE_ENV=development

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif,zip,rar

# Security Configuration
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
```

## Project Structure

```
document-repository/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── admin/             # Admin dashboard pages
│   │   ├── api/               # API routes
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # User dashboard
│   │   └── upload/            # File upload page
│   ├── components/            # Reusable React components
│   ├── lib/                   # Utility libraries
│   │   ├── supabase.ts       # Supabase client configuration
│   │   ├── google-drive.ts   # Google Drive API integration
│   │   ├── database.ts       # Database service layer
│   │   ├── security.ts       # Security utilities
│   │   └── utils.ts          # General utilities
│   ├── types/                 # TypeScript type definitions
│   └── __tests__/            # Test files
├── public/                    # Static assets
├── supabase-schema.sql       # Database schema
└── README.md                 # This file
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

## API Documentation

### Authentication Endpoints

- `POST /api/auth/signout` - Sign out user
- `GET /api/auth/callback` - OAuth callback handler

### File Management Endpoints

- `POST /api/files/upload` - Upload new file
- `GET /api/files/upload` - Get user's files (with pagination)
- `GET /api/files/[id]` - Get file details
- `PUT /api/files/[id]` - Update file metadata
- `DELETE /api/files/[id]` - Delete file
- `GET /api/files/[id]/download` - Download file

### Admin Endpoints

- `GET /api/admin/stats` - Get dashboard statistics
- `GET /api/admin/users` - Get all users (admin only)

## Security Features

- **File Validation**: Comprehensive file type and content validation
- **Rate Limiting**: API rate limiting to prevent abuse
- **CSRF Protection**: Cross-site request forgery protection
- **Input Sanitization**: All user inputs are sanitized
- **Secure Headers**: Security headers added to all responses
- **Role-Based Access**: Admin and user roles with proper permissions
- **Audit Logging**: All actions are logged for security monitoring

## Testing

The project includes comprehensive tests:

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

Test coverage includes:
- Unit tests for utilities and security functions
- Component tests for React components
- Integration tests for API endpoints
- Security validation tests

## Deployment

### Vercel Deployment (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on push

### Manual Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Start the production server:
   ```bash
   npm start
   ```

### Environment Variables for Production

Make sure to set all environment variables in your production environment, updating URLs and secrets as needed.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run tests: `npm run test`
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue on GitHub or contact the development team.
