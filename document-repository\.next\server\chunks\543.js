exports.id=543,exports.ids=[543],exports.modules={14334:(a,b,c)=>{"use strict";c.d(b,{F8:()=>j,NX:()=>l,YQ:()=>h.A,ZH:()=>g.A,_M:()=>m,k4:()=>k});var d=c(73024),e=c(76760),f=c(77680),g=c(86596),h=c(26204);let{stat:i}=d.promises,j=(a,b)=>n((0,d.statSync)(a),a,b),k=(a,b)=>i(a).then(c=>n(c,a,b)),l=(a,b)=>i(a).then(c=>o(c,a,b)),m=(a,b)=>o((0,d.statSync)(a),a,b),n=(a,b,c="")=>new h.A([new p({path:b,size:a.size,lastModified:a.mtimeMs,start:0})],{type:c}),o=(a,b,c="")=>new g.A([new p({path:b,size:a.size,lastModified:a.mtimeMs,start:0})],(0,e.basename)(b),{type:c,lastModified:a.mtimeMs});class p{#a;#b;constructor(a){this.#a=a.path,this.#b=a.start,this.size=a.size,this.lastModified=a.lastModified}slice(a,b){return new p({path:this.#a,lastModified:this.lastModified,size:b-a,start:this.#b+a})}async *stream(){let{mtimeMs:a}=await i(this.#a);if(a>this.lastModified)throw new f("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,d.createReadStream)(this.#a,{start:this.#b,end:this.#b+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}},26204:(a,b,c)=>{"use strict";async function*d(a,b=!0){for(let c of a)if("stream"in c)yield*c.stream();else if(ArrayBuffer.isView(c))if(b){let a=c.byteOffset,b=c.byteOffset+c.byteLength;for(;a!==b;){let d=Math.min(b-a,65536),e=c.buffer.slice(a,a+d);a+=e.byteLength,yield new Uint8Array(e)}}else yield c;else{let a=0;for(;a!==c.size;){let b=c.slice(a,Math.min(c.size,a+65536)),d=await b.arrayBuffer();a+=d.byteLength,yield new Uint8Array(d)}}}c.d(b,{A:()=>f}),c(75484);let e=class a{#c=[];#d="";#e=0;#f="transparent";constructor(b=[],c={}){if("object"!=typeof b||null===b)throw TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if("function"!=typeof b[Symbol.iterator])throw TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if("object"!=typeof c&&"function"!=typeof c)throw TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");null===c&&(c={});let d=new TextEncoder;for(let c of b){let b;b=ArrayBuffer.isView(c)?new Uint8Array(c.buffer.slice(c.byteOffset,c.byteOffset+c.byteLength)):c instanceof ArrayBuffer?new Uint8Array(c.slice(0)):c instanceof a?c:d.encode(`${c}`),this.#e+=ArrayBuffer.isView(b)?b.byteLength:b.size,this.#c.push(b)}this.#f=`${void 0===c.endings?"transparent":c.endings}`;let e=void 0===c.type?"":String(c.type);this.#d=/^[\x20-\x7E]*$/.test(e)?e:""}get size(){return this.#e}get type(){return this.#d}async text(){let a=new TextDecoder,b="";for await(let c of d(this.#c,!1))b+=a.decode(c,{stream:!0});return b+a.decode()}async arrayBuffer(){let a=new Uint8Array(this.size),b=0;for await(let c of d(this.#c,!1))a.set(c,b),b+=c.length;return a.buffer}stream(){let a=d(this.#c,!0);return new globalThis.ReadableStream({type:"bytes",async pull(b){let c=await a.next();c.done?b.close():b.enqueue(c.value)},async cancel(){await a.return()}})}slice(b=0,c=this.size,d=""){let{size:e}=this,f=b<0?Math.max(e+b,0):Math.min(b,e),g=c<0?Math.max(e+c,0):Math.min(c,e),h=Math.max(g-f,0),i=this.#c,j=[],k=0;for(let a of i){if(k>=h)break;let b=ArrayBuffer.isView(a)?a.byteLength:a.size;if(f&&b<=f)f-=b,g-=b;else{let c;ArrayBuffer.isView(a)?k+=(c=a.subarray(f,Math.min(b,g))).byteLength:k+=(c=a.slice(f,Math.min(b,g))).size,g-=b,j.push(c),f=0}}let l=new a([],{type:String(d).toLowerCase()});return l.#e=h,l.#c=j,l}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](a){return a&&"object"==typeof a&&"function"==typeof a.constructor&&("function"==typeof a.stream||"function"==typeof a.arrayBuffer)&&/^(Blob|File)$/.test(a[Symbol.toStringTag])}};Object.defineProperties(e.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});let f=e},63707:function(a,b){(function(a){"use strict";var b,c,d;function e(){}function f(a){return"object"==typeof a&&null!==a||"function"==typeof a}function g(a,b){try{Object.defineProperty(a,"name",{value:b,configurable:!0})}catch(a){}}let h=Promise,i=Promise.prototype.then,j=Promise.reject.bind(h);function k(a){return new h(a)}function l(a){return k(b=>b(a))}function m(a,b,c){return i.call(a,b,c)}function n(a,b,c){m(m(a,b,c),void 0,e)}function o(a,b){n(a,void 0,b)}function p(a){m(a,void 0,e)}let q=a=>{if("function"==typeof queueMicrotask)q=queueMicrotask;else{let a=l(void 0);q=b=>m(a,b)}return q(a)};function r(a,b,c){if("function"!=typeof a)throw TypeError("Argument is not a function");return Function.prototype.apply.call(a,b,c)}function s(a,b,c){try{return l(r(a,b,c))}catch(a){return j(a)}}class t{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(a){let b=this._back,c=b;16383===b._elements.length&&(c={_elements:[],_next:void 0}),b._elements.push(a),c!==b&&(this._back=c,b._next=c),++this._size}shift(){let a=this._front,b=a,c=this._cursor,d=c+1,e=a._elements,f=e[c];return 16384===d&&(b=a._next,d=0),--this._size,this._cursor=d,a!==b&&(this._front=b),e[c]=void 0,f}forEach(a){let b=this._cursor,c=this._front,d=c._elements;for(;(b!==d.length||void 0!==c._next)&&(b!==d.length||(d=(c=c._next)._elements,b=0,0!==d.length));)a(d[b]),++b}peek(){let a=this._front,b=this._cursor;return a._elements[b]}}let u=Symbol("[[AbortSteps]]"),v=Symbol("[[ErrorSteps]]"),w=Symbol("[[CancelSteps]]"),x=Symbol("[[PullSteps]]"),y=Symbol("[[ReleaseSteps]]");function z(a,b){var c,d,e;a._ownerReadableStream=b,b._reader=a,"readable"===b._state?D(a):"closed"===b._state?(D(c=a),F(c)):(d=a,e=b._storedError,D(d),E(d,e))}function A(a,b){return b1(a._ownerReadableStream,b)}function B(a){let b=a._ownerReadableStream;"readable"===b._state?E(a,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(a,b){D(a),E(a,b)}(a,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),b._readableStreamController[y](),b._reader=void 0,a._ownerReadableStream=void 0}function C(a){return TypeError("Cannot "+a+" a stream using a released reader")}function D(a){a._closedPromise=k((b,c)=>{a._closedPromise_resolve=b,a._closedPromise_reject=c})}function E(a,b){void 0!==a._closedPromise_reject&&(p(a._closedPromise),a._closedPromise_reject(b),a._closedPromise_resolve=void 0,a._closedPromise_reject=void 0)}function F(a){void 0!==a._closedPromise_resolve&&(a._closedPromise_resolve(void 0),a._closedPromise_resolve=void 0,a._closedPromise_reject=void 0)}let G=Number.isFinite||function(a){return"number"==typeof a&&isFinite(a)},H=Math.trunc||function(a){return a<0?Math.ceil(a):Math.floor(a)};function I(a,b){if(void 0!==a&&"object"!=typeof a&&"function"!=typeof a)throw TypeError(`${b} is not an object.`)}function J(a,b){if("function"!=typeof a)throw TypeError(`${b} is not a function.`)}function K(a,b){if(("object"!=typeof a||null===a)&&"function"!=typeof a)throw TypeError(`${b} is not an object.`)}function L(a,b,c){if(void 0===a)throw TypeError(`Parameter ${b} is required in '${c}'.`)}function M(a,b,c){if(void 0===a)throw TypeError(`${b} is required in '${c}'.`)}function N(a){return Number(a)}function O(a,b){var c,d;let e=Number.MAX_SAFE_INTEGER,f=Number(a);if(!G(f=0===(c=f)?0:c))throw TypeError(`${b} is not a finite number`);if((f=0===(d=H(f))?0:d)<0||f>e)throw TypeError(`${b} is outside the accepted range of 0 to ${e}, inclusive`);return G(f)&&0!==f?f:0}function P(a,b){if(!b_(a))throw TypeError(`${b} is not a ReadableStream.`)}function Q(a){return new V(a)}function R(a,b){a._reader._readRequests.push(b)}function S(a,b,c){let d=a._reader._readRequests.shift();c?d._closeSteps():d._chunkSteps(b)}function T(a){return a._reader._readRequests.length}function U(a){let b=a._reader;return void 0!==b&&!!W(b)}class V{constructor(a){if(L(a,1,"ReadableStreamDefaultReader"),P(a,"First parameter"),b0(a))throw TypeError("This stream has already been locked for exclusive reading by another reader");z(this,a),this._readRequests=new t}get closed(){return W(this)?this._closedPromise:j(Z("closed"))}cancel(a){return W(this)?void 0===this._ownerReadableStream?j(C("cancel")):A(this,a):j(Z("cancel"))}read(){let a,b;if(!W(this))return j(Z("read"));if(void 0===this._ownerReadableStream)return j(C("read from"));let c=k((c,d)=>{a=c,b=d});return X(this,{_chunkSteps:b=>a({value:b,done:!1}),_closeSteps:()=>a({value:void 0,done:!0}),_errorSteps:a=>b(a)}),c}releaseLock(){var a;if(!W(this))throw Z("releaseLock");void 0!==this._ownerReadableStream&&(a=this,B(a),Y(a,TypeError("Reader was released")))}}function W(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_readRequests")&&a instanceof V}function X(a,b){let c=a._ownerReadableStream;c._disturbed=!0,"closed"===c._state?b._closeSteps():"errored"===c._state?b._errorSteps(c._storedError):c._readableStreamController[x](b)}function Y(a,b){let c=a._readRequests;a._readRequests=new t,c.forEach(a=>{a._errorSteps(b)})}function Z(a){return TypeError(`ReadableStreamDefaultReader.prototype.${a} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(V.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),g(V.prototype.cancel,"cancel"),g(V.prototype.read,"read"),g(V.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(V.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let $=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class _{constructor(a,b){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=a,this._preventCancel=b}next(){let a=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?m(this._ongoingPromise,a,a):a(),this._ongoingPromise}return(a){let b=()=>this._returnSteps(a);return this._ongoingPromise?m(this._ongoingPromise,b,b):b()}_nextSteps(){let a,b;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let c=this._reader,d=k((c,d)=>{a=c,b=d});return X(c,{_chunkSteps:b=>{this._ongoingPromise=void 0,q(()=>a({value:b,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,B(c),a({value:void 0,done:!0})},_errorSteps:a=>{this._ongoingPromise=void 0,this._isFinished=!0,B(c),b(a)}}),d}_returnSteps(a){if(this._isFinished)return Promise.resolve({value:a,done:!0});this._isFinished=!0;let b=this._reader;if(!this._preventCancel){let c=A(b,a);return B(b),m(c,()=>({value:a,done:!0}),void 0)}return B(b),l({value:a,done:!0})}}let aa={next(){return ab(this)?this._asyncIteratorImpl.next():j(ac("next"))},return(a){return ab(this)?this._asyncIteratorImpl.return(a):j(ac("return"))}};function ab(a){if(!f(a)||!Object.prototype.hasOwnProperty.call(a,"_asyncIteratorImpl"))return!1;try{return a._asyncIteratorImpl instanceof _}catch(a){return!1}}function ac(a){return TypeError(`ReadableStreamAsyncIterator.${a} can only be used on a ReadableSteamAsyncIterator`)}Object.setPrototypeOf(aa,$);let ad=Number.isNaN||function(a){return a!=a};function ae(a){return a.slice()}function af(a,b,c,d,e){new Uint8Array(a).set(new Uint8Array(c,d,e),b)}let ag=a=>(ag="function"==typeof a.transfer?a=>a.transfer():"function"==typeof structuredClone?a=>structuredClone(a,{transfer:[a]}):a=>a)(a),ah=a=>(ah="boolean"==typeof a.detached?a=>a.detached:a=>0===a.byteLength)(a);function ai(a,b,c){if(a.slice)return a.slice(b,c);let d=c-b,e=new ArrayBuffer(d);return af(e,0,a,b,d),e}function aj(a,b){let c=a[b];if(null!=c){if("function"!=typeof c)throw TypeError(`${String(b)} is not a function`);return c}}let ak=null!=(d=null!=(b=Symbol.asyncIterator)?b:null==(c=Symbol.for)?void 0:c.call(Symbol,"Symbol.asyncIterator"))?d:"@@asyncIterator";function al(a){return new Uint8Array(ai(a.buffer,a.byteOffset,a.byteOffset+a.byteLength))}function am(a){let b=a._queue.shift();return a._queueTotalSize-=b.size,a._queueTotalSize<0&&(a._queueTotalSize=0),b.value}function an(a,b,c){if(!(!("number"!=typeof c||ad(c))&&!(c<0)&&1)||c===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");a._queue.push({value:b,size:c}),a._queueTotalSize+=c}function ao(a){a._queue=new t,a._queueTotalSize=0}function ap(a){return a===DataView}class aq{constructor(){throw TypeError("Illegal constructor")}get view(){if(!at(this))throw aS("view");return this._view}respond(a){if(!at(this))throw aS("respond");if(L(a,1,"respond"),a=O(a,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(ah(this._view.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");aP(this._associatedReadableByteStreamController,a)}respondWithNewView(a){if(!at(this))throw aS("respondWithNewView");if(L(a,1,"respondWithNewView"),!ArrayBuffer.isView(a))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(ah(a.buffer))throw TypeError("The given view's buffer has been detached and so cannot be used as a response");aQ(this._associatedReadableByteStreamController,a)}}Object.defineProperties(aq.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),g(aq.prototype.respond,"respond"),g(aq.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(aq.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ar{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!as(this))throw aT("byobRequest");return aN(this)}get desiredSize(){if(!as(this))throw aT("desiredSize");return aO(this)}close(){if(!as(this))throw aT("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let a=this._controlledReadableByteStream._state;if("readable"!==a)throw TypeError(`The stream (in ${a} state) is not in the readable state and cannot be closed`);aJ(this)}enqueue(a){if(!as(this))throw aT("enqueue");if(L(a,1,"enqueue"),!ArrayBuffer.isView(a))throw TypeError("chunk must be an array buffer view");if(0===a.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===a.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let b=this._controlledReadableByteStream._state;if("readable"!==b)throw TypeError(`The stream (in ${b} state) is not in the readable state and cannot be enqueued to`);aK(this,a)}error(a){if(!as(this))throw aT("error");aL(this,a)}[w](a){av(this),ao(this);let b=this._cancelAlgorithm(a);return aI(this),b}[x](a){let b=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void aM(this,a);let c=this._autoAllocateChunkSize;if(void 0!==c){let b;try{b=new ArrayBuffer(c)}catch(b){a._errorSteps(b);return}let d={buffer:b,bufferByteLength:c,byteOffset:0,byteLength:c,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(d)}R(b,a),au(this)}[y](){if(this._pendingPullIntos.length>0){let a=this._pendingPullIntos.peek();a.readerType="none",this._pendingPullIntos=new t,this._pendingPullIntos.push(a)}}}function as(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_controlledReadableByteStream")&&a instanceof ar}function at(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_associatedReadableByteStreamController")&&a instanceof aq}function au(a){if(function(a){let b=a._controlledReadableByteStream;return"readable"===b._state&&!a._closeRequested&&!!a._started&&!!(U(b)&&T(b)>0||aW(b)&&aV(b)>0||aO(a)>0)}(a)){if(a._pulling){a._pullAgain=!0;return}a._pulling=!0,n(a._pullAlgorithm(),()=>(a._pulling=!1,a._pullAgain&&(a._pullAgain=!1,au(a)),null),b=>(aL(a,b),null))}}function av(a){aE(a),a._pendingPullIntos=new t}function aw(a,b){let c=!1;"closed"===a._state&&(c=!0);let d=ax(b);"default"===b.readerType?S(a,d,c):function(a,b,c){let d=a._reader._readIntoRequests.shift();c?d._closeSteps(b):d._chunkSteps(b)}(a,d,c)}function ax(a){let b=a.bytesFilled,c=a.elementSize;return new a.viewConstructor(a.buffer,a.byteOffset,b/c)}function ay(a,b,c,d){a._queue.push({buffer:b,byteOffset:c,byteLength:d}),a._queueTotalSize+=d}function az(a,b,c,d){let e;try{e=ai(b,c,c+d)}catch(b){throw aL(a,b),b}ay(a,e,0,d)}function aA(a,b){b.bytesFilled>0&&az(a,b.buffer,b.byteOffset,b.bytesFilled),aH(a)}function aB(a,b){let c=Math.min(a._queueTotalSize,b.byteLength-b.bytesFilled),d=b.bytesFilled+c,e=c,f=!1,g=d%b.elementSize,h=d-g;h>=b.minimumFill&&(e=h-b.bytesFilled,f=!0);let i=a._queue;for(;e>0;){let c=i.peek(),d=Math.min(e,c.byteLength),f=b.byteOffset+b.bytesFilled;af(b.buffer,f,c.buffer,c.byteOffset,d),c.byteLength===d?i.shift():(c.byteOffset+=d,c.byteLength-=d),a._queueTotalSize-=d,aC(a,d,b),e-=d}return f}function aC(a,b,c){c.bytesFilled+=b}function aD(a){0===a._queueTotalSize&&a._closeRequested?(aI(a),b2(a._controlledReadableByteStream)):au(a)}function aE(a){null!==a._byobRequest&&(a._byobRequest._associatedReadableByteStreamController=void 0,a._byobRequest._view=null,a._byobRequest=null)}function aF(a){for(;a._pendingPullIntos.length>0;){if(0===a._queueTotalSize)return;let b=a._pendingPullIntos.peek();aB(a,b)&&(aH(a),aw(a._controlledReadableByteStream,b))}}function aG(a,b){let c=a._pendingPullIntos.peek();if(aE(a),"closed"===a._controlledReadableByteStream._state){"none"===c.readerType&&aH(a);let b=a._controlledReadableByteStream;if(aW(b))for(;aV(b)>0;)aw(b,aH(a))}else!function(a,b,c){if(aC(a,b,c),"none"===c.readerType){aA(a,c),aF(a);return}if(c.bytesFilled<c.minimumFill)return;aH(a);let d=c.bytesFilled%c.elementSize;if(d>0){let b=c.byteOffset+c.bytesFilled;az(a,c.buffer,b-d,d)}c.bytesFilled-=d,aw(a._controlledReadableByteStream,c),aF(a)}(a,b,c);au(a)}function aH(a){return a._pendingPullIntos.shift()}function aI(a){a._pullAlgorithm=void 0,a._cancelAlgorithm=void 0}function aJ(a){let b=a._controlledReadableByteStream;if(!a._closeRequested&&"readable"===b._state){if(a._queueTotalSize>0){a._closeRequested=!0;return}if(a._pendingPullIntos.length>0){let b=a._pendingPullIntos.peek();if(b.bytesFilled%b.elementSize!=0){let b=TypeError("Insufficient bytes to fill elements in the given buffer");throw aL(a,b),b}}aI(a),b2(b)}}function aK(a,b){let c=a._controlledReadableByteStream;if(a._closeRequested||"readable"!==c._state)return;let{buffer:d,byteOffset:e,byteLength:f}=b;if(ah(d))throw TypeError("chunk's buffer is detached and so cannot be enqueued");let g=ag(d);if(a._pendingPullIntos.length>0){let b=a._pendingPullIntos.peek();if(ah(b.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");aE(a),b.buffer=ag(b.buffer),"none"===b.readerType&&aA(a,b)}U(c)?(!function(a){let b=a._controlledReadableByteStream._reader;for(;b._readRequests.length>0;){if(0===a._queueTotalSize)return;aM(a,b._readRequests.shift())}}(a),0===T(c)?ay(a,g,e,f):(a._pendingPullIntos.length>0&&aH(a),S(c,new Uint8Array(g,e,f),!1))):aW(c)?(ay(a,g,e,f),aF(a)):ay(a,g,e,f),au(a)}function aL(a,b){let c=a._controlledReadableByteStream;"readable"===c._state&&(av(a),ao(a),aI(a),b3(c,b))}function aM(a,b){let c=a._queue.shift();a._queueTotalSize-=c.byteLength,aD(a);let d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength);b._chunkSteps(d)}function aN(a){if(null===a._byobRequest&&a._pendingPullIntos.length>0){var b,c,d;let e=a._pendingPullIntos.peek(),f=new Uint8Array(e.buffer,e.byteOffset+e.bytesFilled,e.byteLength-e.bytesFilled),g=Object.create(aq.prototype);b=g,c=a,d=f,b._associatedReadableByteStreamController=c,b._view=d,a._byobRequest=g}return a._byobRequest}function aO(a){let b=a._controlledReadableByteStream._state;return"errored"===b?null:"closed"===b?0:a._strategyHWM-a._queueTotalSize}function aP(a,b){let c=a._pendingPullIntos.peek();if("closed"===a._controlledReadableByteStream._state){if(0!==b)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===b)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(c.bytesFilled+b>c.byteLength)throw RangeError("bytesWritten out of range")}c.buffer=ag(c.buffer),aG(a,b)}function aQ(a,b){let c=a._pendingPullIntos.peek();if("closed"===a._controlledReadableByteStream._state){if(0!==b.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===b.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(c.byteOffset+c.bytesFilled!==b.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(c.bufferByteLength!==b.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(c.bytesFilled+b.byteLength>c.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let d=b.byteLength;c.buffer=ag(b.buffer),aG(a,d)}function aR(a,b,c,d,e,f,g){b._controlledReadableByteStream=a,b._pullAgain=!1,b._pulling=!1,b._byobRequest=null,b._queue=b._queueTotalSize=void 0,ao(b),b._closeRequested=!1,b._started=!1,b._strategyHWM=f,b._pullAlgorithm=d,b._cancelAlgorithm=e,b._autoAllocateChunkSize=g,b._pendingPullIntos=new t,a._readableStreamController=b,n(l(c()),()=>(b._started=!0,au(b),null),a=>(aL(b,a),null))}function aS(a){return TypeError(`ReadableStreamBYOBRequest.prototype.${a} can only be used on a ReadableStreamBYOBRequest`)}function aT(a){return TypeError(`ReadableByteStreamController.prototype.${a} can only be used on a ReadableByteStreamController`)}Object.defineProperties(ar.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),g(ar.prototype.close,"close"),g(ar.prototype.enqueue,"enqueue"),g(ar.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ar.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function aU(a,b){a._reader._readIntoRequests.push(b)}function aV(a){return a._reader._readIntoRequests.length}function aW(a){let b=a._reader;return void 0!==b&&!!aY(b)}class aX{constructor(a){if(L(a,1,"ReadableStreamBYOBReader"),P(a,"First parameter"),b0(a))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!as(a._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");z(this,a),this._readIntoRequests=new t}get closed(){return aY(this)?this._closedPromise:j(a_("closed"))}cancel(a){return aY(this)?void 0===this._ownerReadableStream?j(C("cancel")):A(this,a):j(a_("cancel"))}read(a,b={}){let c,d,e;if(!aY(this))return j(a_("read"));if(!ArrayBuffer.isView(a))return j(TypeError("view must be an array buffer view"));if(0===a.byteLength)return j(TypeError("view must have non-zero byteLength"));if(0===a.buffer.byteLength)return j(TypeError("view's buffer must have non-zero byteLength"));if(ah(a.buffer))return j(TypeError("view's buffer has been detached"));try{var f;I(b,"options"),c={min:O(null!=(f=null==b?void 0:b.min)?f:1,"options has member 'min' that")}}catch(a){return j(a)}let g=c.min;if(0===g)return j(TypeError("options.min must be greater than 0"));if(ap(a.constructor)){if(g>a.byteLength)return j(RangeError("options.min must be less than or equal to view's byteLength"))}else if(g>a.length)return j(RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return j(C("read from"));let h=k((a,b)=>{d=a,e=b});return aZ(this,a,g,{_chunkSteps:a=>d({value:a,done:!1}),_closeSteps:a=>d({value:a,done:!0}),_errorSteps:a=>e(a)}),h}releaseLock(){var a;if(!aY(this))throw a_("releaseLock");void 0!==this._ownerReadableStream&&(a=this,B(a),a$(a,TypeError("Reader was released")))}}function aY(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_readIntoRequests")&&a instanceof aX}function aZ(a,b,c,d){let e=a._ownerReadableStream;e._disturbed=!0,"errored"===e._state?d._errorSteps(e._storedError):function(a,b,c,d){let e,f=a._controlledReadableByteStream,g=b.constructor,h=ap(g)?1:g.BYTES_PER_ELEMENT,{byteOffset:i,byteLength:j}=b;try{e=ag(b.buffer)}catch(a){d._errorSteps(a);return}let k={buffer:e,bufferByteLength:e.byteLength,byteOffset:i,byteLength:j,bytesFilled:0,minimumFill:c*h,elementSize:h,viewConstructor:g,readerType:"byob"};if(a._pendingPullIntos.length>0){a._pendingPullIntos.push(k),aU(f,d);return}if("closed"===f._state){let a=new g(k.buffer,k.byteOffset,0);d._closeSteps(a);return}if(a._queueTotalSize>0){if(aB(a,k)){let b=ax(k);aD(a),d._chunkSteps(b);return}if(a._closeRequested){let b=TypeError("Insufficient bytes to fill elements in the given buffer");aL(a,b),d._errorSteps(b);return}}a._pendingPullIntos.push(k),aU(f,d),au(a)}(e._readableStreamController,b,c,d)}function a$(a,b){let c=a._readIntoRequests;a._readIntoRequests=new t,c.forEach(a=>{a._errorSteps(b)})}function a_(a){return TypeError(`ReadableStreamBYOBReader.prototype.${a} can only be used on a ReadableStreamBYOBReader`)}function a0(a,b){let{highWaterMark:c}=a;if(void 0===c)return b;if(ad(c)||c<0)throw RangeError("Invalid highWaterMark");return c}function a1(a){let{size:b}=a;return b||(()=>1)}function a2(a,b){var c;I(a,b);let d=null==a?void 0:a.highWaterMark,e=null==a?void 0:a.size;return{highWaterMark:void 0===d?void 0:N(d),size:void 0===e?void 0:(J(c=e,`${b} has member 'size' that`),a=>N(c(a)))}}function a3(a,b){if(!a7(a))throw TypeError(`${b} is not a WritableStream.`)}Object.defineProperties(aX.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),g(aX.prototype.cancel,"cancel"),g(aX.prototype.read,"read"),g(aX.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(aX.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let a4="function"==typeof AbortController;class a5{constructor(a={},b={}){void 0===a?a=null:K(a,"First parameter");let c=a2(b,"Second parameter"),d=function(a,b){var c,d,e,f,g,h,i,j;I(a,b);let k=null==a?void 0:a.abort,l=null==a?void 0:a.close,m=null==a?void 0:a.start,n=null==a?void 0:a.type,o=null==a?void 0:a.write;return{abort:void 0===k?void 0:(c=k,d=a,J(c,`${b} has member 'abort' that`),a=>s(c,d,[a])),close:void 0===l?void 0:(e=l,f=a,J(e,`${b} has member 'close' that`),()=>s(e,f,[])),start:void 0===m?void 0:(g=m,h=a,J(g,`${b} has member 'start' that`),a=>r(g,h,[a])),write:void 0===o?void 0:(i=o,j=a,J(i,`${b} has member 'write' that`),(a,b)=>s(i,j,[a,b])),type:n}}(a,"First parameter");if(a6(this),void 0!==d.type)throw RangeError("Invalid type is specified");let e=a1(c);!function(a,b,c,d){let e,f,g=Object.create(bo.prototype);e=void 0!==b.start?()=>b.start(g):()=>void 0,f=void 0!==b.write?a=>b.write(a,g):()=>l(void 0),bq(a,g,e,f,void 0!==b.close?()=>b.close():()=>l(void 0),void 0!==b.abort?a=>b.abort(a):()=>l(void 0),c,d)}(this,d,a0(c,1),e)}get locked(){if(!a7(this))throw bw("locked");return a8(this)}abort(a){return a7(this)?a8(this)?j(TypeError("Cannot abort a stream that already has a writer")):a9(this,a):j(bw("abort"))}close(){return a7(this)?a8(this)?j(TypeError("Cannot close a stream that already has a writer")):be(this)?j(TypeError("Cannot close an already-closing stream")):ba(this):j(bw("close"))}getWriter(){var a;if(!a7(this))throw bw("getWriter");return a=this,new bh(a)}}function a6(a){a._state="writable",a._storedError=void 0,a._writer=void 0,a._writableStreamController=void 0,a._writeRequests=new t,a._inFlightWriteRequest=void 0,a._closeRequest=void 0,a._inFlightCloseRequest=void 0,a._pendingAbortRequest=void 0,a._backpressure=!1}function a7(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_writableStreamController")&&a instanceof a5}function a8(a){return void 0!==a._writer}function a9(a,b){var c;if("closed"===a._state||"errored"===a._state)return l(void 0);a._writableStreamController._abortReason=b,null==(c=a._writableStreamController._abortController)||c.abort(b);let d=a._state;if("closed"===d||"errored"===d)return l(void 0);if(void 0!==a._pendingAbortRequest)return a._pendingAbortRequest._promise;let e=!1;"erroring"===d&&(e=!0,b=void 0);let f=k((c,d)=>{a._pendingAbortRequest={_promise:void 0,_resolve:c,_reject:d,_reason:b,_wasAlreadyErroring:e}});return a._pendingAbortRequest._promise=f,e||bc(a,b),f}function ba(a){var b;let c=a._state;if("closed"===c||"errored"===c)return j(TypeError(`The stream (in ${c} state) is not in the writable state and cannot be closed`));let d=k((b,c)=>{a._closeRequest={_resolve:b,_reject:c}}),e=a._writer;return void 0!==e&&a._backpressure&&"writable"===c&&bG(e),an(b=a._writableStreamController,bn,0),bt(b),d}function bb(a,b){if("writable"===a._state)return void bc(a,b);bd(a)}function bc(a,b){var c;let d=a._writableStreamController;a._state="erroring",a._storedError=b;let e=a._writer;void 0!==e&&bk(e,b),void 0===(c=a)._inFlightWriteRequest&&void 0===c._inFlightCloseRequest&&d._started&&bd(a)}function bd(a){a._state="errored",a._writableStreamController[v]();let b=a._storedError;if(a._writeRequests.forEach(a=>{a._reject(b)}),a._writeRequests=new t,void 0===a._pendingAbortRequest)return void bf(a);let c=a._pendingAbortRequest;if(a._pendingAbortRequest=void 0,c._wasAlreadyErroring){c._reject(b),bf(a);return}n(a._writableStreamController[u](c._reason),()=>(c._resolve(),bf(a),null),b=>(c._reject(b),bf(a),null))}function be(a){return void 0!==a._closeRequest||void 0!==a._inFlightCloseRequest}function bf(a){void 0!==a._closeRequest&&(a._closeRequest._reject(a._storedError),a._closeRequest=void 0);let b=a._writer;void 0!==b&&bB(b,a._storedError)}function bg(a,b){let c=a._writer;void 0!==c&&b!==a._backpressure&&(b?bD(c):bG(c)),a._backpressure=b}Object.defineProperties(a5.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),g(a5.prototype.abort,"abort"),g(a5.prototype.close,"close"),g(a5.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(a5.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class bh{constructor(a){var b,c,d,e,f;if(L(a,1,"WritableStreamDefaultWriter"),a3(a,"First parameter"),a8(a))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=a,a._writer=this;let g=a._state;if("writable"===g){!be(a)&&a._backpressure?bD(this):(b=this,bD(b),bG(b)),bA(this)}else if("erroring"===g)bE(this,a._storedError),bA(this);else if("closed"===g){c=this,bD(c),bG(c),d=this,bA(d),bC(d)}else{let b=a._storedError;bE(this,b),e=this,f=b,bA(e),bB(e,f)}}get closed(){return bi(this)?this._closedPromise:j(by("closed"))}get desiredSize(){if(!bi(this))throw by("desiredSize");if(void 0===this._ownerWritableStream)throw bz("desiredSize");var a=this;let b=a._ownerWritableStream,c=b._state;return"errored"===c||"erroring"===c?null:"closed"===c?0:bs(b._writableStreamController)}get ready(){return bi(this)?this._readyPromise:j(by("ready"))}abort(a){var b,c;if(!bi(this))return j(by("abort"));if(void 0===this._ownerWritableStream)return j(bz("abort"));return b=this,c=a,a9(b._ownerWritableStream,c)}close(){if(!bi(this))return j(by("close"));let a=this._ownerWritableStream;return void 0===a?j(bz("close")):be(a)?j(TypeError("Cannot close an already-closing stream")):bj(this)}releaseLock(){if(!bi(this))throw by("releaseLock");void 0!==this._ownerWritableStream&&bl(this)}write(a){return bi(this)?void 0===this._ownerWritableStream?j(bz("write to")):bm(this,a):j(by("write"))}}function bi(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_ownerWritableStream")&&a instanceof bh}function bj(a){return ba(a._ownerWritableStream)}function bk(a,b){"pending"===a._readyPromiseState?bF(a,b):bE(a,b)}function bl(a){let b=a._ownerWritableStream,c=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");bk(a,c),"pending"===a._closedPromiseState?bB(a,c):function(a,b){bA(a),bB(a,b)}(a,c),b._writer=void 0,a._ownerWritableStream=void 0}function bm(a,b){let c=a._ownerWritableStream,d=c._writableStreamController,e=function(a,b){try{return a._strategySizeAlgorithm(b)}catch(b){return bu(a,b),1}}(d,b);if(c!==a._ownerWritableStream)return j(bz("write to"));let f=c._state;if("errored"===f)return j(c._storedError);if(be(c)||"closed"===f)return j(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===f)return j(c._storedError);let g=k((a,b)=>{c._writeRequests.push({_resolve:a,_reject:b})});return function(a,b,c){try{an(a,b,c)}catch(b){bu(a,b);return}let d=a._controlledWritableStream;be(d)||"writable"!==d._state||bg(d,function(a){return 0>=bs(a)}(a)),bt(a)}(d,b,e),g}Object.defineProperties(bh.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),g(bh.prototype.abort,"abort"),g(bh.prototype.close,"close"),g(bh.prototype.releaseLock,"releaseLock"),g(bh.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(bh.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let bn={};class bo{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!bp(this))throw bx("abortReason");return this._abortReason}get signal(){if(!bp(this))throw bx("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(a){if(!bp(this))throw bx("error");"writable"===this._controlledWritableStream._state&&bv(this,a)}[u](a){let b=this._abortAlgorithm(a);return br(this),b}[v](){ao(this)}}function bp(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_controlledWritableStream")&&a instanceof bo}function bq(a,b,c,d,e,f,g,h){b._controlledWritableStream=a,a._writableStreamController=b,b._queue=void 0,b._queueTotalSize=void 0,ao(b),b._abortReason=void 0,b._abortController=function(){if(a4)return new AbortController}(),b._started=!1,b._strategySizeAlgorithm=h,b._strategyHWM=g,b._writeAlgorithm=d,b._closeAlgorithm=e,b._abortAlgorithm=f,bg(a,0>=bs(b)),n(l(c()),()=>(b._started=!0,bt(b),null),c=>(b._started=!0,bb(a,c),null))}function br(a){a._writeAlgorithm=void 0,a._closeAlgorithm=void 0,a._abortAlgorithm=void 0,a._strategySizeAlgorithm=void 0}function bs(a){return a._strategyHWM-a._queueTotalSize}function bt(a){let b=a._controlledWritableStream;if(!a._started||void 0!==b._inFlightWriteRequest)return;if("erroring"===b._state)return void bd(b);if(0===a._queue.length)return;let c=a._queue.peek().value;c===bn?function(a){let b=a._controlledWritableStream;b._inFlightCloseRequest=b._closeRequest,b._closeRequest=void 0,am(a);let c=a._closeAlgorithm();br(a),n(c,()=>{b._inFlightCloseRequest._resolve(void 0),b._inFlightCloseRequest=void 0,"erroring"===b._state&&(b._storedError=void 0,void 0!==b._pendingAbortRequest&&(b._pendingAbortRequest._resolve(),b._pendingAbortRequest=void 0)),b._state="closed";let a=b._writer;return void 0!==a&&bC(a),null},a=>(b._inFlightCloseRequest._reject(a),b._inFlightCloseRequest=void 0,void 0!==b._pendingAbortRequest&&(b._pendingAbortRequest._reject(a),b._pendingAbortRequest=void 0),bb(b,a),null))}(a):function(a,b){let c=a._controlledWritableStream;c._inFlightWriteRequest=c._writeRequests.shift(),n(a._writeAlgorithm(b),()=>{c._inFlightWriteRequest._resolve(void 0),c._inFlightWriteRequest=void 0;let b=c._state;return am(a),be(c)||"writable"!==b||bg(c,function(a){return 0>=bs(a)}(a)),bt(a),null},b=>("writable"===c._state&&br(a),c._inFlightWriteRequest._reject(b),c._inFlightWriteRequest=void 0,bb(c,b),null))}(a,c)}function bu(a,b){"writable"===a._controlledWritableStream._state&&bv(a,b)}function bv(a,b){let c=a._controlledWritableStream;br(a),bc(c,b)}function bw(a){return TypeError(`WritableStream.prototype.${a} can only be used on a WritableStream`)}function bx(a){return TypeError(`WritableStreamDefaultController.prototype.${a} can only be used on a WritableStreamDefaultController`)}function by(a){return TypeError(`WritableStreamDefaultWriter.prototype.${a} can only be used on a WritableStreamDefaultWriter`)}function bz(a){return TypeError("Cannot "+a+" a stream using a released writer")}function bA(a){a._closedPromise=k((b,c)=>{a._closedPromise_resolve=b,a._closedPromise_reject=c,a._closedPromiseState="pending"})}Object.defineProperties(bo.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(bo.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function bB(a,b){void 0!==a._closedPromise_reject&&(p(a._closedPromise),a._closedPromise_reject(b),a._closedPromise_resolve=void 0,a._closedPromise_reject=void 0,a._closedPromiseState="rejected")}function bC(a){void 0!==a._closedPromise_resolve&&(a._closedPromise_resolve(void 0),a._closedPromise_resolve=void 0,a._closedPromise_reject=void 0,a._closedPromiseState="resolved")}function bD(a){a._readyPromise=k((b,c)=>{a._readyPromise_resolve=b,a._readyPromise_reject=c}),a._readyPromiseState="pending"}function bE(a,b){bD(a),bF(a,b)}function bF(a,b){void 0!==a._readyPromise_reject&&(p(a._readyPromise),a._readyPromise_reject(b),a._readyPromise_resolve=void 0,a._readyPromise_reject=void 0,a._readyPromiseState="rejected")}function bG(a){void 0!==a._readyPromise_resolve&&(a._readyPromise_resolve(void 0),a._readyPromise_resolve=void 0,a._readyPromise_reject=void 0,a._readyPromiseState="fulfilled")}let bH="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,bI=function(){let a=null==bH?void 0:bH.DOMException;return!function(a){if("function"!=typeof a&&"object"!=typeof a||"DOMException"!==a.name)return!1;try{return new a,!0}catch(a){return!1}}(a)?void 0:a}()||function(){let a=function(a,b){this.message=a||"",this.name=b||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return g(a,"DOMException"),a.prototype=Object.create(Error.prototype),Object.defineProperty(a.prototype,"constructor",{value:a,writable:!0,configurable:!0}),a}();function bJ(a,b,c,d,f,g){let h=Q(a),i=new bh(b);a._disturbed=!0;let q=!1,r=l(void 0);return k((s,t)=>{var u,v,w;let x;if(void 0!==g){if(x=()=>{let c=void 0!==g.reason?g.reason:new bI("Aborted","AbortError"),e=[];d||e.push(()=>"writable"===b._state?a9(b,c):l(void 0)),f||e.push(()=>"readable"===a._state?b1(a,c):l(void 0)),A(()=>Promise.all(e.map(a=>a())),!0,c)},g.aborted)return void x();g.addEventListener("abort",x)}if(z(a,h._closedPromise,a=>(d?C(!0,a):A(()=>a9(b,a),!0,a),null)),z(b,i._closedPromise,b=>(f?C(!0,b):A(()=>b1(a,b),!0,b),null)),u=a,v=h._closedPromise,w=()=>(c?C():A(()=>(function(a){let b=a._ownerWritableStream,c=b._state;return be(b)||"closed"===c?l(void 0):"errored"===c?j(b._storedError):bj(a)})(i)),null),"closed"===u._state?w():n(v,w),be(b)||"closed"===b._state){let b=TypeError("the destination writable stream closed before all data could be piped to it");f?C(!0,b):A(()=>b1(a,b),!0,b)}function y(){let a=r;return m(r,()=>a!==r?y():void 0)}function z(a,b,c){"errored"===a._state?c(a._storedError):o(b,c)}function A(a,c,d){if(!q)if(q=!0,"writable"!==b._state||be(b))e();else n(y(),e);function e(){return n(a(),()=>D(c,d),a=>D(!0,a)),null}}function C(a,c){if(!q)if(q=!0,"writable"!==b._state||be(b))D(a,c);else n(y(),()=>D(a,c))}function D(a,b){return bl(i),B(h),void 0!==g&&g.removeEventListener("abort",x),a?t(b):s(void 0),null}p(k((a,b)=>{!function c(d){d?a():m(q?l(!0):m(i._readyPromise,()=>k((a,b)=>{X(h,{_chunkSteps:b=>{r=m(bm(i,b),void 0,e),a(!1)},_closeSteps:()=>a(!0),_errorSteps:b})})),c,b)}(!1)}))})}class bK{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!bL(this))throw bV("desiredSize");return bS(this)}close(){if(!bL(this))throw bV("close");if(!bT(this))throw TypeError("The stream is not in a state that permits close");bP(this)}enqueue(a){if(!bL(this))throw bV("enqueue");if(!bT(this))throw TypeError("The stream is not in a state that permits enqueue");return bQ(this,a)}error(a){if(!bL(this))throw bV("error");bR(this,a)}[w](a){ao(this);let b=this._cancelAlgorithm(a);return bO(this),b}[x](a){let b=this._controlledReadableStream;if(this._queue.length>0){let c=am(this);this._closeRequested&&0===this._queue.length?(bO(this),b2(b)):bM(this),a._chunkSteps(c)}else R(b,a),bM(this)}[y](){}}function bL(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_controlledReadableStream")&&a instanceof bK}function bM(a){if(bN(a)){if(a._pulling){a._pullAgain=!0;return}a._pulling=!0,n(a._pullAlgorithm(),()=>(a._pulling=!1,a._pullAgain&&(a._pullAgain=!1,bM(a)),null),b=>(bR(a,b),null))}}function bN(a){let b=a._controlledReadableStream;return!!bT(a)&&!!a._started&&!!(b0(b)&&T(b)>0||bS(a)>0)}function bO(a){a._pullAlgorithm=void 0,a._cancelAlgorithm=void 0,a._strategySizeAlgorithm=void 0}function bP(a){if(!bT(a))return;let b=a._controlledReadableStream;a._closeRequested=!0,0===a._queue.length&&(bO(a),b2(b))}function bQ(a,b){if(!bT(a))return;let c=a._controlledReadableStream;if(b0(c)&&T(c)>0)S(c,b,!1);else{let c;try{c=a._strategySizeAlgorithm(b)}catch(b){throw bR(a,b),b}try{an(a,b,c)}catch(b){throw bR(a,b),b}}bM(a)}function bR(a,b){let c=a._controlledReadableStream;"readable"===c._state&&(ao(a),bO(a),b3(c,b))}function bS(a){let b=a._controlledReadableStream._state;return"errored"===b?null:"closed"===b?0:a._strategyHWM-a._queueTotalSize}function bT(a){let b=a._controlledReadableStream._state;return!a._closeRequested&&"readable"===b}function bU(a,b,c,d,e,f,g){b._controlledReadableStream=a,b._queue=void 0,b._queueTotalSize=void 0,ao(b),b._started=!1,b._closeRequested=!1,b._pullAgain=!1,b._pulling=!1,b._strategySizeAlgorithm=g,b._strategyHWM=f,b._pullAlgorithm=d,b._cancelAlgorithm=e,a._readableStreamController=b,n(l(c()),()=>(b._started=!0,bM(b),null),a=>(bR(b,a),null))}function bV(a){return TypeError(`ReadableStreamDefaultController.prototype.${a} can only be used on a ReadableStreamDefaultController`)}function bW(a,b){I(a,b);let c=null==a?void 0:a.preventAbort,d=null==a?void 0:a.preventCancel,e=null==a?void 0:a.preventClose,f=null==a?void 0:a.signal;return void 0!==f&&function(a,b){if(!function(a){if("object"!=typeof a||null===a)return!1;try{return"boolean"==typeof a.aborted}catch(a){return!1}}(a))throw TypeError(`${b} is not an AbortSignal.`)}(f,`${b} has member 'signal' that`),{preventAbort:!!c,preventCancel:!!d,preventClose:!!e,signal:f}}Object.defineProperties(bK.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),g(bK.prototype.close,"close"),g(bK.prototype.enqueue,"enqueue"),g(bK.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(bK.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class bX{constructor(a={},b={}){void 0===a?a=null:K(a,"First parameter");let c=a2(b,"Second parameter"),d=function(a,b){var c,d,e,f,g,h;I(a,b);let i=null==a?void 0:a.autoAllocateChunkSize,j=null==a?void 0:a.cancel,k=null==a?void 0:a.pull,l=null==a?void 0:a.start,m=null==a?void 0:a.type;return{autoAllocateChunkSize:void 0===i?void 0:O(i,`${b} has member 'autoAllocateChunkSize' that`),cancel:void 0===j?void 0:(c=j,d=a,J(c,`${b} has member 'cancel' that`),a=>s(c,d,[a])),pull:void 0===k?void 0:(e=k,f=a,J(e,`${b} has member 'pull' that`),a=>s(e,f,[a])),start:void 0===l?void 0:(g=l,h=a,J(g,`${b} has member 'start' that`),a=>r(g,h,[a])),type:void 0===m?void 0:function(a,b){if("bytes"!=(a=`${a}`))throw TypeError(`${b} '${a}' is not a valid enumeration value for ReadableStreamType`);return a}(m,`${b} has member 'type' that`)}}(a,"First parameter");if(b$(this),"bytes"===d.type){if(void 0!==c.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(a,b,c){let d,e,f,g=Object.create(ar.prototype);d=void 0!==b.start?()=>b.start(g):()=>void 0,e=void 0!==b.pull?()=>b.pull(g):()=>l(void 0),f=void 0!==b.cancel?a=>b.cancel(a):()=>l(void 0);let h=b.autoAllocateChunkSize;if(0===h)throw TypeError("autoAllocateChunkSize must be greater than 0");aR(a,g,d,e,f,c,h)}(this,d,a0(c,0))}else{let a=a1(c);!function(a,b,c,d){let e,f,g=Object.create(bK.prototype);e=void 0!==b.start?()=>b.start(g):()=>void 0,f=void 0!==b.pull?()=>b.pull(g):()=>l(void 0),bU(a,g,e,f,void 0!==b.cancel?a=>b.cancel(a):()=>l(void 0),c,d)}(this,d,a0(c,1),a)}}get locked(){if(!b_(this))throw b4("locked");return b0(this)}cancel(a){return b_(this)?b0(this)?j(TypeError("Cannot cancel a stream that already has a reader")):b1(this,a):j(b4("cancel"))}getReader(a){if(!b_(this))throw b4("getReader");return void 0===function(a,b){I(a,b);let c=null==a?void 0:a.mode;return{mode:void 0===c?void 0:function(a,b){if("byob"!=(a=`${a}`))throw TypeError(`${b} '${a}' is not a valid enumeration value for ReadableStreamReaderMode`);return a}(c,`${b} has member 'mode' that`)}}(a,"First parameter").mode?Q(this):new aX(this)}pipeThrough(a,b={}){if(!b_(this))throw b4("pipeThrough");L(a,1,"pipeThrough");let c=function(a,b){I(a,b);let c=null==a?void 0:a.readable;M(c,"readable","ReadableWritablePair"),P(c,`${b} has member 'readable' that`);let d=null==a?void 0:a.writable;return M(d,"writable","ReadableWritablePair"),a3(d,`${b} has member 'writable' that`),{readable:c,writable:d}}(a,"First parameter"),d=bW(b,"Second parameter");if(b0(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(a8(c.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return p(bJ(this,c.writable,d.preventClose,d.preventAbort,d.preventCancel,d.signal)),c.readable}pipeTo(a,b={}){let c;if(!b_(this))return j(b4("pipeTo"));if(void 0===a)return j("Parameter 1 is required in 'pipeTo'.");if(!a7(a))return j(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{c=bW(b,"Second parameter")}catch(a){return j(a)}return b0(this)?j(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):a8(a)?j(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):bJ(this,a,c.preventClose,c.preventAbort,c.preventCancel,c.signal)}tee(){if(!b_(this))throw b4("tee");let a=as(this._readableStreamController)?function(a){let b,c,d,e,f,g=Q(a),h=!1,i=!1,j=!1,m=!1,n=!1,p=k(a=>{f=a});function r(a){o(a._closedPromise,b=>(a!==g||(aL(d._readableStreamController,b),aL(e._readableStreamController,b),m&&n||f(void 0)),null))}function s(){aY(g)&&(B(g),r(g=Q(a))),X(g,{_chunkSteps:b=>{q(()=>{i=!1,j=!1;let c=b;if(!m&&!n)try{c=al(b)}catch(b){aL(d._readableStreamController,b),aL(e._readableStreamController,b),f(b1(a,b));return}m||aK(d._readableStreamController,b),n||aK(e._readableStreamController,c),h=!1,i?u():j&&v()})},_closeSteps:()=>{h=!1,m||aJ(d._readableStreamController),n||aJ(e._readableStreamController),d._readableStreamController._pendingPullIntos.length>0&&aP(d._readableStreamController,0),e._readableStreamController._pendingPullIntos.length>0&&aP(e._readableStreamController,0),m&&n||f(void 0)},_errorSteps:()=>{h=!1}})}function t(b,c){W(g)&&(B(g),r(g=new aX(a)));let k=c?e:d,l=c?d:e;aZ(g,b,1,{_chunkSteps:b=>{q(()=>{i=!1,j=!1;let d=c?n:m;if(c?m:n)d||aQ(k._readableStreamController,b);else{let c;try{c=al(b)}catch(b){aL(k._readableStreamController,b),aL(l._readableStreamController,b),f(b1(a,b));return}d||aQ(k._readableStreamController,b),aK(l._readableStreamController,c)}h=!1,i?u():j&&v()})},_closeSteps:a=>{h=!1;let b=c?n:m,d=c?m:n;b||aJ(k._readableStreamController),d||aJ(l._readableStreamController),void 0!==a&&(b||aQ(k._readableStreamController,a),!d&&l._readableStreamController._pendingPullIntos.length>0&&aP(l._readableStreamController,0)),b&&d||f(void 0)},_errorSteps:()=>{h=!1}})}function u(){if(h)return i=!0,l(void 0);h=!0;let a=aN(d._readableStreamController);return null===a?s():t(a._view,!1),l(void 0)}function v(){if(h)return j=!0,l(void 0);h=!0;let a=aN(e._readableStreamController);return null===a?s():t(a._view,!0),l(void 0)}function w(){}return d=bZ(w,u,function(d){if(m=!0,b=d,n){let d=b1(a,ae([b,c]));f(d)}return p}),e=bZ(w,v,function(d){if(n=!0,c=d,m){let d=b1(a,ae([b,c]));f(d)}return p}),r(g),[d,e]}(this):function(a,b){let c,d,e,f,g,h=Q(a),i=!1,j=!1,m=!1,n=!1,p=k(a=>{g=a});function r(){return i?j=!0:(i=!0,X(h,{_chunkSteps:a=>{q(()=>{j=!1,m||bQ(e._readableStreamController,a),n||bQ(f._readableStreamController,a),i=!1,j&&r()})},_closeSteps:()=>{i=!1,m||bP(e._readableStreamController),n||bP(f._readableStreamController),m&&n||g(void 0)},_errorSteps:()=>{i=!1}})),l(void 0)}function s(){}return e=bY(s,r,function(b){if(m=!0,c=b,n){let b=b1(a,ae([c,d]));g(b)}return p}),f=bY(s,r,function(b){if(n=!0,d=b,m){let b=b1(a,ae([c,d]));g(b)}return p}),o(h._closedPromise,a=>(bR(e._readableStreamController,a),bR(f._readableStreamController,a),m&&n||g(void 0),null)),[e,f]}(this);return ae(a)}values(a){if(!b_(this))throw b4("values");return function(a,b){let c=new _(Q(a),b),d=Object.create(aa);return d._asyncIteratorImpl=c,d}(this,(I(a,"First parameter"),{preventCancel:!!(null==a?void 0:a.preventCancel)}).preventCancel)}[ak](a){return this.values(a)}static from(a){var b;let c;return f(a)&&void 0!==a.getReader?(b=a.getReader(),c=bY(e,function(){let a;try{a=b.read()}catch(a){return j(a)}return m(a,a=>{if(!f(a))throw TypeError("The promise returned by the reader.read() method must fulfill with an object");if(a.done)bP(c._readableStreamController);else{let b=a.value;bQ(c._readableStreamController,b)}},void 0)},function(a){try{return l(b.cancel(a))}catch(a){return j(a)}},0)):function(a){let b,c=function a(b,c="sync",d){if(void 0===d)if("async"===c){if(void 0===(d=aj(b,ak))){let c=aj(b,Symbol.iterator);var e=a(b,"sync",c);let d={[Symbol.iterator]:()=>e.iterator},f=async function*(){return yield*d}(),g=f.next;return{iterator:f,nextMethod:g,done:!1}}}else d=aj(b,Symbol.iterator);if(void 0===d)throw TypeError("The object is not iterable");let g=r(d,b,[]);if(!f(g))throw TypeError("The iterator method must return an object");let h=g.next;return{iterator:g,nextMethod:h,done:!1}}(a,"async");return b=bY(e,function(){let a;try{a=function(a){let b=r(a.nextMethod,a.iterator,[]);if(!f(b))throw TypeError("The iterator.next() method must return an object");return b}(c)}catch(a){return j(a)}return m(l(a),a=>{if(!f(a))throw TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(a.done)bP(b._readableStreamController);else{let c=a.value;bQ(b._readableStreamController,c)}},void 0)},function(a){let b,d,e=c.iterator;try{b=aj(e,"return")}catch(a){return j(a)}if(void 0===b)return l(void 0);try{d=r(b,e,[a])}catch(a){return j(a)}return m(l(d),a=>{if(!f(a))throw TypeError("The promise returned by the iterator.return() method must fulfill with an object")},void 0)},0)}(a)}}function bY(a,b,c,d=1,e=()=>1){let f=Object.create(bX.prototype);return b$(f),bU(f,Object.create(bK.prototype),a,b,c,d,e),f}function bZ(a,b,c){let d=Object.create(bX.prototype);return b$(d),aR(d,Object.create(ar.prototype),a,b,c,0,void 0),d}function b$(a){a._state="readable",a._reader=void 0,a._storedError=void 0,a._disturbed=!1}function b_(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_readableStreamController")&&a instanceof bX}function b0(a){return void 0!==a._reader}function b1(a,b){if(a._disturbed=!0,"closed"===a._state)return l(void 0);if("errored"===a._state)return j(a._storedError);b2(a);let c=a._reader;if(void 0!==c&&aY(c)){let a=c._readIntoRequests;c._readIntoRequests=new t,a.forEach(a=>{a._closeSteps(void 0)})}return m(a._readableStreamController[w](b),e,void 0)}function b2(a){a._state="closed";let b=a._reader;if(void 0!==b&&(F(b),W(b))){let a=b._readRequests;b._readRequests=new t,a.forEach(a=>{a._closeSteps()})}}function b3(a,b){a._state="errored",a._storedError=b;let c=a._reader;void 0!==c&&(E(c,b),W(c)?Y(c,b):a$(c,b))}function b4(a){return TypeError(`ReadableStream.prototype.${a} can only be used on a ReadableStream`)}function b5(a,b){I(a,b);let c=null==a?void 0:a.highWaterMark;return M(c,"highWaterMark","QueuingStrategyInit"),{highWaterMark:N(c)}}Object.defineProperties(bX,{from:{enumerable:!0}}),Object.defineProperties(bX.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),g(bX.from,"from"),g(bX.prototype.cancel,"cancel"),g(bX.prototype.getReader,"getReader"),g(bX.prototype.pipeThrough,"pipeThrough"),g(bX.prototype.pipeTo,"pipeTo"),g(bX.prototype.tee,"tee"),g(bX.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(bX.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(bX.prototype,ak,{value:bX.prototype.values,writable:!0,configurable:!0});let b6=a=>a.byteLength;g(b6,"size");class b7{constructor(a){L(a,1,"ByteLengthQueuingStrategy"),a=b5(a,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=a.highWaterMark}get highWaterMark(){if(!b9(this))throw b8("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!b9(this))throw b8("size");return b6}}function b8(a){return TypeError(`ByteLengthQueuingStrategy.prototype.${a} can only be used on a ByteLengthQueuingStrategy`)}function b9(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_byteLengthQueuingStrategyHighWaterMark")&&a instanceof b7}Object.defineProperties(b7.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(b7.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let ca=()=>1;g(ca,"size");class cb{constructor(a){L(a,1,"CountQueuingStrategy"),a=b5(a,"First parameter"),this._countQueuingStrategyHighWaterMark=a.highWaterMark}get highWaterMark(){if(!cd(this))throw cc("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!cd(this))throw cc("size");return ca}}function cc(a){return TypeError(`CountQueuingStrategy.prototype.${a} can only be used on a CountQueuingStrategy`)}function cd(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_countQueuingStrategyHighWaterMark")&&a instanceof cb}Object.defineProperties(cb.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(cb.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class ce{constructor(a={},b={},c={}){let d;void 0===a&&(a=null);let e=a2(b,"Second parameter"),f=a2(c,"Third parameter"),g=function(a,b){var c,d,e,f,g,h,i,j;I(a,b);let k=null==a?void 0:a.cancel,l=null==a?void 0:a.flush,m=null==a?void 0:a.readableType,n=null==a?void 0:a.start,o=null==a?void 0:a.transform,p=null==a?void 0:a.writableType;return{cancel:void 0===k?void 0:(c=k,d=a,J(c,`${b} has member 'cancel' that`),a=>s(c,d,[a])),flush:void 0===l?void 0:(e=l,f=a,J(e,`${b} has member 'flush' that`),a=>s(e,f,[a])),readableType:m,start:void 0===n?void 0:(g=n,h=a,J(g,`${b} has member 'start' that`),a=>r(g,h,[a])),transform:void 0===o?void 0:(i=o,j=a,J(i,`${b} has member 'transform' that`),(a,b)=>s(i,j,[a,b])),writableType:p}}(a,"First parameter");if(void 0!==g.readableType)throw RangeError("Invalid readableType specified");if(void 0!==g.writableType)throw RangeError("Invalid writableType specified");let h=a0(f,0),i=a1(f),o=a0(e,1),p=a1(e);(function(a,b,c,d,e,f){function g(){return b}a._writable=function(a,b,c,d,e=1,f=()=>1){let g=Object.create(a5.prototype);return a6(g),bq(g,Object.create(bo.prototype),a,b,c,d,e,f),g}(g,function(b){var c=a,d=b;let e=c._transformStreamController;return c._backpressure?m(c._backpressureChangePromise,()=>{let a=c._writable;if("erroring"===a._state)throw a._storedError;return co(e,d)},void 0):co(e,d)},function(){var b=a;let c=b._transformStreamController;if(void 0!==c._finishPromise)return c._finishPromise;let d=b._readable;c._finishPromise=k((a,b)=>{c._finishPromise_resolve=a,c._finishPromise_reject=b});let e=c._flushAlgorithm();return cm(c),n(e,()=>("errored"===d._state?cr(c,d._storedError):(bP(d._readableStreamController),cq(c)),null),a=>(bR(d._readableStreamController,a),cr(c,a),null)),c._finishPromise},function(b){var c=a,d=b;let e=c._transformStreamController;if(void 0!==e._finishPromise)return e._finishPromise;let f=c._readable;e._finishPromise=k((a,b)=>{e._finishPromise_resolve=a,e._finishPromise_reject=b});let g=e._cancelAlgorithm(d);return cm(e),n(g,()=>("errored"===f._state?cr(e,f._storedError):(bR(f._readableStreamController,d),cq(e)),null),a=>(bR(f._readableStreamController,a),cr(e,a),null)),e._finishPromise},c,d),a._readable=bY(g,function(){var b;return cj(b=a,!1),b._backpressureChangePromise},function(b){var c=a,d=b;let e=c._transformStreamController;if(void 0!==e._finishPromise)return e._finishPromise;let f=c._writable;e._finishPromise=k((a,b)=>{e._finishPromise_resolve=a,e._finishPromise_reject=b});let g=e._cancelAlgorithm(d);return cm(e),n(g,()=>("errored"===f._state?cr(e,f._storedError):(bu(f._writableStreamController,d),ci(c),cq(e)),null),a=>(bu(f._writableStreamController,a),ci(c),cr(e,a),null)),e._finishPromise},e,f),a._backpressure=void 0,a._backpressureChangePromise=void 0,a._backpressureChangePromise_resolve=void 0,cj(a,!0),a._transformStreamController=void 0})(this,k(a=>{d=a}),o,p,h,i),function(a,b){let c,d,e,f=Object.create(ck.prototype);c=void 0!==b.transform?a=>b.transform(a,f):a=>{try{return cn(f,a),l(void 0)}catch(a){return j(a)}},d=void 0!==b.flush?()=>b.flush(f):()=>l(void 0),e=void 0!==b.cancel?a=>b.cancel(a):()=>l(void 0),f._controlledTransformStream=a,a._transformStreamController=f,f._transformAlgorithm=c,f._flushAlgorithm=d,f._cancelAlgorithm=e,f._finishPromise=void 0,f._finishPromise_resolve=void 0,f._finishPromise_reject=void 0}(this,g),void 0!==g.start?d(g.start(this._transformStreamController)):d(void 0)}get readable(){if(!cf(this))throw cs("readable");return this._readable}get writable(){if(!cf(this))throw cs("writable");return this._writable}}function cf(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_transformStreamController")&&a instanceof ce}function cg(a,b){bR(a._readable._readableStreamController,b),ch(a,b)}function ch(a,b){cm(a._transformStreamController),bu(a._writable._writableStreamController,b),ci(a)}function ci(a){a._backpressure&&cj(a,!1)}function cj(a,b){void 0!==a._backpressureChangePromise&&a._backpressureChangePromise_resolve(),a._backpressureChangePromise=k(b=>{a._backpressureChangePromise_resolve=b}),a._backpressure=b}Object.defineProperties(ce.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ce.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class ck{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!cl(this))throw cp("desiredSize");return bS(this._controlledTransformStream._readable._readableStreamController)}enqueue(a){if(!cl(this))throw cp("enqueue");cn(this,a)}error(a){var b,c;if(!cl(this))throw cp("error");b=this,c=a,cg(b._controlledTransformStream,c)}terminate(){if(!cl(this))throw cp("terminate");var a=this;let b=a._controlledTransformStream;bP(b._readable._readableStreamController),ch(b,TypeError("TransformStream terminated"))}}function cl(a){return!!f(a)&&!!Object.prototype.hasOwnProperty.call(a,"_controlledTransformStream")&&a instanceof ck}function cm(a){a._transformAlgorithm=void 0,a._flushAlgorithm=void 0,a._cancelAlgorithm=void 0}function cn(a,b){let c=a._controlledTransformStream,d=c._readable._readableStreamController;if(!bT(d))throw TypeError("Readable side is not in a state that permits enqueue");try{bQ(d,b)}catch(a){throw ch(c,a),c._readable._storedError}!bN(d)!==c._backpressure&&cj(c,!0)}function co(a,b){return m(a._transformAlgorithm(b),void 0,b=>{throw cg(a._controlledTransformStream,b),b})}function cp(a){return TypeError(`TransformStreamDefaultController.prototype.${a} can only be used on a TransformStreamDefaultController`)}function cq(a){void 0!==a._finishPromise_resolve&&(a._finishPromise_resolve(),a._finishPromise_resolve=void 0,a._finishPromise_reject=void 0)}function cr(a,b){void 0!==a._finishPromise_reject&&(p(a._finishPromise),a._finishPromise_reject(b),a._finishPromise_resolve=void 0,a._finishPromise_reject=void 0)}function cs(a){return TypeError(`TransformStream.prototype.${a} can only be used on a TransformStream`)}Object.defineProperties(ck.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),g(ck.prototype.enqueue,"enqueue"),g(ck.prototype.error,"error"),g(ck.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ck.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),a.ByteLengthQueuingStrategy=b7,a.CountQueuingStrategy=cb,a.ReadableByteStreamController=ar,a.ReadableStream=bX,a.ReadableStreamBYOBReader=aX,a.ReadableStreamBYOBRequest=aq,a.ReadableStreamDefaultController=bK,a.ReadableStreamDefaultReader=V,a.TransformStream=ce,a.TransformStreamDefaultController=ck,a.WritableStream=a5,a.WritableStreamDefaultController=bo,a.WritableStreamDefaultWriter=bh})(b)},65543:(a,b,c)=>{"use strict";c.r(b),c.d(b,{AbortError:()=>P,Blob:()=>Q.YQ,FetchError:()=>n,File:()=>Q.ZH,FormData:()=>l.fS,Headers:()=>B,Request:()=>O,Response:()=>F,blobFrom:()=>Q.k4,blobFromSync:()=>Q.F8,default:()=>S,fileFrom:()=>Q.NX,fileFromSync:()=>Q._M,isRedirect:()=>D});var d=c(37067),e=c(44708),f=c(16141),g=c(57075),h=c(4573);let i=function(a){if(!/^data:/i.test(a))throw TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');let b=(a=a.replace(/\r?\n/g,"")).indexOf(",");if(-1===b||b<=4)throw TypeError("malformed data: URI");let c=a.substring(5,b).split(";"),d="",e=!1,f=c[0]||"text/plain",g=f;for(let a=1;a<c.length;a++)"base64"===c[a]?e=!0:c[a]&&(g+=`;${c[a]}`,0===c[a].indexOf("charset=")&&(d=c[a].substring(8)));c[0]||d.length||(g+=";charset=US-ASCII",d="US-ASCII");let h=e?"base64":"ascii",i=unescape(a.substring(b+1)),j=Buffer.from(i,h);return j.type=f,j.typeFull=g,j.charset=d,j};var j=c(57975),k=c(26204),l=c(70451);class m extends Error{constructor(a,b){super(a),Error.captureStackTrace(this,this.constructor),this.type=b}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}}class n extends m{constructor(a,b,c){super(a,b),c&&(this.code=this.errno=c.code,this.erroredSysCall=c.syscall)}}let o=Symbol.toStringTag,p=a=>"object"==typeof a&&"function"==typeof a.append&&"function"==typeof a.delete&&"function"==typeof a.get&&"function"==typeof a.getAll&&"function"==typeof a.has&&"function"==typeof a.set&&"function"==typeof a.sort&&"URLSearchParams"===a[o],q=a=>a&&"object"==typeof a&&"function"==typeof a.arrayBuffer&&"string"==typeof a.type&&"function"==typeof a.stream&&"function"==typeof a.constructor&&/^(Blob|File)$/.test(a[o]),r=(0,j.promisify)(g.pipeline),s=Symbol("Body internals");class t{constructor(a,{size:b=0}={}){let c=null;null===a?a=null:p(a)?a=h.Buffer.from(a.toString()):q(a)||h.Buffer.isBuffer(a)||(j.types.isAnyArrayBuffer(a)?a=h.Buffer.from(a):ArrayBuffer.isView(a)?a=h.Buffer.from(a.buffer,a.byteOffset,a.byteLength):a instanceof g||(a instanceof l.fS?c=(a=(0,l.$n)(a)).type.split("=")[1]:a=h.Buffer.from(String(a))));let d=a;h.Buffer.isBuffer(a)?d=g.Readable.from(a):q(a)&&(d=g.Readable.from(a.stream())),this[s]={body:a,stream:d,boundary:c,disturbed:!1,error:null},this.size=b,a instanceof g&&a.on("error",a=>{let b=a instanceof m?a:new n(`Invalid response body while trying to fetch ${this.url}: ${a.message}`,"system",a);this[s].error=b})}get body(){return this[s].stream}get bodyUsed(){return this[s].disturbed}async arrayBuffer(){let{buffer:a,byteOffset:b,byteLength:c}=await u(this);return a.slice(b,b+c)}async formData(){let a=this.headers.get("content-type");if(a.startsWith("application/x-www-form-urlencoded")){let a=new l.fS;for(let[b,c]of new URLSearchParams(await this.text()))a.append(b,c);return a}let{toFormData:b}=await c.e(696).then(c.bind(c,1696));return b(this.body,a)}async blob(){let a=this.headers&&this.headers.get("content-type")||this[s].body&&this[s].body.type||"",b=await this.arrayBuffer();return new k.A([b],{type:a})}async json(){return JSON.parse(await this.text())}async text(){let a=await u(this);return new TextDecoder().decode(a)}buffer(){return u(this)}}async function u(a){if(a[s].disturbed)throw TypeError(`body used already for: ${a.url}`);if(a[s].disturbed=!0,a[s].error)throw a[s].error;let{body:b}=a;if(null===b||!(b instanceof g))return h.Buffer.alloc(0);let c=[],d=0;try{for await(let e of b){if(a.size>0&&d+e.length>a.size){let c=new n(`content size at ${a.url} over limit: ${a.size}`,"max-size");throw b.destroy(c),c}d+=e.length,c.push(e)}}catch(b){throw b instanceof m?b:new n(`Invalid response body while trying to fetch ${a.url}: ${b.message}`,"system",b)}if(!0===b.readableEnded||!0===b._readableState.ended)try{if(c.every(a=>"string"==typeof a))return h.Buffer.from(c.join(""));return h.Buffer.concat(c,d)}catch(b){throw new n(`Could not create Buffer from response body for ${a.url}: ${b.message}`,"system",b)}throw new n(`Premature close of server response while trying to fetch ${a.url}`)}t.prototype.buffer=(0,j.deprecate)(t.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer"),Object.defineProperties(t.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,j.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});let v=(a,b)=>{let c,d,{body:e}=a[s];if(a.bodyUsed)throw Error("cannot clone body after it is used");return e instanceof g&&"function"!=typeof e.getBoundary&&(c=new g.PassThrough({highWaterMark:b}),d=new g.PassThrough({highWaterMark:b}),e.pipe(c),e.pipe(d),a[s].stream=c,e=d),e},w=(0,j.deprecate)(a=>a.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),x=(a,b)=>null===a?null:"string"==typeof a?"text/plain;charset=UTF-8":p(a)?"application/x-www-form-urlencoded;charset=UTF-8":q(a)?a.type||null:h.Buffer.isBuffer(a)||j.types.isAnyArrayBuffer(a)||ArrayBuffer.isView(a)?null:a instanceof l.fS?`multipart/form-data; boundary=${b[s].boundary}`:a&&"function"==typeof a.getBoundary?`multipart/form-data;boundary=${w(a)}`:a instanceof g?null:"text/plain;charset=UTF-8",y=async(a,{body:b})=>{null===b?a.end():await r(b,a)},z="function"==typeof d.validateHeaderName?d.validateHeaderName:a=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(a)){let b=TypeError(`Header name must be a valid HTTP token [${a}]`);throw Object.defineProperty(b,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),b}},A="function"==typeof d.validateHeaderValue?d.validateHeaderValue:(a,b)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(b)){let b=TypeError(`Invalid character in header content ["${a}"]`);throw Object.defineProperty(b,"code",{value:"ERR_INVALID_CHAR"}),b}};class B extends URLSearchParams{constructor(a){let b=[];if(a instanceof B)for(let[c,d]of Object.entries(a.raw()))b.push(...d.map(a=>[c,a]));else if(null==a);else if("object"!=typeof a||j.types.isBoxedPrimitive(a))throw TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");else{let c=a[Symbol.iterator];if(null==c)b.push(...Object.entries(a));else{if("function"!=typeof c)throw TypeError("Header pairs must be iterable");b=[...a].map(a=>{if("object"!=typeof a||j.types.isBoxedPrimitive(a))throw TypeError("Each header pair must be an iterable object");return[...a]}).map(a=>{if(2!==a.length)throw TypeError("Each header pair must be a name/value tuple");return[...a]})}}return super(b=b.length>0?b.map(([a,b])=>(z(a),A(a,String(b)),[String(a).toLowerCase(),String(b)])):void 0),new Proxy(this,{get(a,b,c){switch(b){case"append":case"set":return(c,d)=>(z(c),A(c,String(d)),URLSearchParams.prototype[b].call(a,String(c).toLowerCase(),String(d)));case"delete":case"has":case"getAll":return c=>(z(c),URLSearchParams.prototype[b].call(a,String(c).toLowerCase()));case"keys":return()=>(a.sort(),new Set(URLSearchParams.prototype.keys.call(a)).keys());default:return Reflect.get(a,b,c)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(a){let b=this.getAll(a);if(0===b.length)return null;let c=b.join(", ");return/^content-encoding$/i.test(a)&&(c=c.toLowerCase()),c}forEach(a,b){for(let c of this.keys())Reflect.apply(a,b,[this.get(c),c,this])}*values(){for(let a of this.keys())yield this.get(a)}*entries(){for(let a of this.keys())yield[a,this.get(a)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((a,b)=>(a[b]=this.getAll(b),a),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((a,b)=>{let c=this.getAll(b);return"host"===b?a[b]=c[0]:a[b]=c.length>1?c:c[0],a},{})}}Object.defineProperties(B.prototype,["get","entries","forEach","values"].reduce((a,b)=>(a[b]={enumerable:!0},a),{}));let C=new Set([301,302,303,307,308]),D=a=>C.has(a),E=Symbol("Response internals");class F extends t{constructor(a=null,b={}){super(a,b);let c=null!=b.status?b.status:200,d=new B(b.headers);if(null!==a&&!d.has("Content-Type")){let b=x(a,this);b&&d.append("Content-Type",b)}this[E]={type:"default",url:b.url,status:c,statusText:b.statusText||"",headers:d,counter:b.counter,highWaterMark:b.highWaterMark}}get type(){return this[E].type}get url(){return this[E].url||""}get status(){return this[E].status}get ok(){return this[E].status>=200&&this[E].status<300}get redirected(){return this[E].counter>0}get statusText(){return this[E].statusText}get headers(){return this[E].headers}get highWaterMark(){return this[E].highWaterMark}clone(){return new F(v(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(a,b=302){if(!D(b))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');return new F(null,{headers:{location:new URL(a).toString()},status:b})}static error(){let a=new F(null,{status:0,statusText:""});return a[E].type="error",a}static json(a,b={}){let c=JSON.stringify(a);if(void 0===c)throw TypeError("data is not JSON serializable");let d=new B(b&&b.headers);return d.has("content-type")||d.set("content-type","application/json"),new F(c,{...b,headers:d})}get[Symbol.toStringTag](){return"Response"}}Object.defineProperties(F.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var G=c(73136),H=c(77030);function I(a,b=!1){return null==a||(a=new URL(a),/^(about|blob|data):$/.test(a.protocol))?"no-referrer":(a.username="",a.password="",a.hash="",b&&(a.pathname="",a.search=""),a)}let J=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]);function K(a){if(/^about:(blank|srcdoc)$/.test(a)||"data:"===a.protocol||/^(blob|filesystem):$/.test(a.protocol))return!0;if(/^(http|ws)s:$/.test(a.protocol))return!0;let b=a.host.replace(/(^\[)|(]$)/g,""),c=(0,H.isIP)(b);return!!(4===c&&/^127\./.test(b)||6===c&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(b))||!("localhost"===a.host||a.host.endsWith(".localhost"))&&"file:"===a.protocol}let L=Symbol("Request internals"),M=a=>"object"==typeof a&&"object"==typeof a[L],N=(0,j.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)");class O extends t{constructor(a,b={}){let c;if(M(a)?c=new URL(a.url):(c=new URL(a),a={}),""!==c.username||""!==c.password)throw TypeError(`${c} is an url with embedded credentials.`);let d=b.method||a.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(d)&&(d=d.toUpperCase()),!M(b)&&"data"in b&&N(),(null!=b.body||M(a)&&null!==a.body)&&("GET"===d||"HEAD"===d))throw TypeError("Request with GET/HEAD method cannot have body");let e=b.body?b.body:M(a)&&null!==a.body?v(a):null;super(e,{size:b.size||a.size||0});let f=new B(b.headers||a.headers||{});if(null!==e&&!f.has("Content-Type")){let a=x(e,this);a&&f.set("Content-Type",a)}let g=M(a)?a.signal:null;if("signal"in b&&(g=b.signal),null!=g&&!(a=>"object"==typeof a&&("AbortSignal"===a[o]||"EventTarget"===a[o]))(g))throw TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let h=null==b.referrer?a.referrer:b.referrer;if(""===h)h="no-referrer";else if(h){let a=new URL(h);h=/^about:(\/\/)?client$/.test(a)?"client":a}else h=void 0;this[L]={method:d,redirect:b.redirect||a.redirect||"follow",headers:f,parsedURL:c,signal:g,referrer:h},this.follow=void 0===b.follow?void 0===a.follow?20:a.follow:b.follow,this.compress=void 0===b.compress?void 0===a.compress||a.compress:b.compress,this.counter=b.counter||a.counter||0,this.agent=b.agent||a.agent,this.highWaterMark=b.highWaterMark||a.highWaterMark||16384,this.insecureHTTPParser=b.insecureHTTPParser||a.insecureHTTPParser||!1,this.referrerPolicy=b.referrerPolicy||a.referrerPolicy||""}get method(){return this[L].method}get url(){return(0,G.format)(this[L].parsedURL)}get headers(){return this[L].headers}get redirect(){return this[L].redirect}get signal(){return this[L].signal}get referrer(){return"no-referrer"===this[L].referrer?"":"client"===this[L].referrer?"about:client":this[L].referrer?this[L].referrer.toString():void 0}get referrerPolicy(){return this[L].referrerPolicy}set referrerPolicy(a){this[L].referrerPolicy=function(a){if(!J.has(a))throw TypeError(`Invalid referrerPolicy: ${a}`);return a}(a)}clone(){return new O(this)}get[Symbol.toStringTag](){return"Request"}}Object.defineProperties(O.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});class P extends m{constructor(a,b="aborted"){super(a,b)}}var Q=c(14334);let R=new Set(["data:","http:","https:"]);async function S(a,b){return new Promise((c,j)=>{let k=new O(a,b),{parsedURL:l,options:m}=(a=>{let{parsedURL:b}=a[L],c=new B(a[L].headers);c.has("Accept")||c.set("Accept","*/*");let d=null;if(null===a.body&&/^(post|put)$/i.test(a.method)&&(d="0"),null!==a.body){let b=(a=>{let{body:b}=a[s];return null===b?0:q(b)?b.size:h.Buffer.isBuffer(b)?b.length:b&&"function"==typeof b.getLengthSync&&b.hasKnownLength&&b.hasKnownLength()?b.getLengthSync():null})(a);"number"!=typeof b||Number.isNaN(b)||(d=String(b))}d&&c.set("Content-Length",d),""===a.referrerPolicy&&(a.referrerPolicy="strict-origin-when-cross-origin"),a.referrer&&"no-referrer"!==a.referrer?a[L].referrer=function(a,{referrerURLCallback:b,referrerOriginCallback:c}={}){if("no-referrer"===a.referrer||""===a.referrerPolicy)return null;let d=a.referrerPolicy;if("about:client"===a.referrer)return"no-referrer";let e=a.referrer,f=I(e),g=I(e,!0);f.toString().length>4096&&(f=g),b&&(f=b(f)),c&&(g=c(g));let h=new URL(a.url);switch(d){case"no-referrer":return"no-referrer";case"origin":return g;case"unsafe-url":return f;case"strict-origin":if(K(f)&&!K(h))return"no-referrer";return g.toString();case"strict-origin-when-cross-origin":if(f.origin===h.origin)return f;if(K(f)&&!K(h))return"no-referrer";return g;case"same-origin":if(f.origin===h.origin)return f;return"no-referrer";case"origin-when-cross-origin":if(f.origin===h.origin)return f;return g;case"no-referrer-when-downgrade":if(K(f)&&!K(h))return"no-referrer";return f;default:throw TypeError(`Invalid referrerPolicy: ${d}`)}}(a):a[L].referrer="no-referrer",a[L].referrer instanceof URL&&c.set("Referer",a.referrer),c.has("User-Agent")||c.set("User-Agent","node-fetch"),a.compress&&!c.has("Accept-Encoding")&&c.set("Accept-Encoding","gzip, deflate, br");let{agent:e}=a;"function"==typeof e&&(e=e(b));let f=(a=>{if(a.search)return a.search;let b=a.href.length-1,c=a.hash||("#"===a.href[b]?"#":"");return"?"===a.href[b-c.length]?"?":""})(b),g={path:b.pathname+f,method:a.method,headers:c[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:a.insecureHTTPParser,agent:e};return{parsedURL:b,options:g}})(k);if(!R.has(l.protocol))throw TypeError(`node-fetch cannot load ${a}. URL scheme "${l.protocol.replace(/:$/,"")}" is not supported.`);if("data:"===l.protocol){let a=i(k.url);c(new F(a,{headers:{"Content-Type":a.typeFull}}));return}let o=("https:"===l.protocol?e:d).request,{signal:p}=k,r=null,t=()=>{let a=new P("The operation was aborted.");j(a),k.body&&k.body instanceof g.Readable&&k.body.destroy(a),r&&r.body&&r.body.emit("error",a)};if(p&&p.aborted)return void t();let u=()=>{t(),x()},w=o(l.toString(),m);p&&p.addEventListener("abort",u);let x=()=>{w.abort(),p&&p.removeEventListener("abort",u)};w.on("error",a=>{j(new n(`request to ${k.url} failed, reason: ${a.message}`,"system",a)),x()}),function(a,b){let c,d=h.Buffer.from("0\r\n\r\n"),e=!1,f=!1;a.on("response",a=>{let{headers:b}=a;e="chunked"===b["transfer-encoding"]&&!b["content-length"]}),a.on("socket",g=>{let i=()=>{if(e&&!f){let a=Error("Premature close");a.code="ERR_STREAM_PREMATURE_CLOSE",b(a)}},j=a=>{(f=0===h.Buffer.compare(a.slice(-5),d))||!c||(f=0===h.Buffer.compare(c.slice(-3),d.slice(0,3))&&0===h.Buffer.compare(a.slice(-2),d.slice(3))),c=a};g.prependListener("close",i),g.on("data",j),a.on("close",()=>{g.removeListener("close",i),g.removeListener("data",j)})})}(w,a=>{r&&r.body&&r.body.destroy(a)}),process.version<"v14"&&w.on("socket",a=>{let b;a.prependListener("end",()=>{b=a._eventsCount}),a.prependListener("close",c=>{if(r&&b<a._eventsCount&&!c){let a=Error("Premature close");a.code="ERR_STREAM_PREMATURE_CLOSE",r.body.emit("error",a)}})}),w.on("response",a=>{w.setTimeout(0);let d=function(a=[]){return new B(a.reduce((a,b,c,d)=>(c%2==0&&a.push(d.slice(c,c+2)),a),[]).filter(([a,b])=>{try{return z(a),A(a,String(b)),!0}catch{return!1}}))}(a.rawHeaders);if(D(a.statusCode)){let f=d.get("Location"),h=null;try{h=null===f?null:new URL(f,k.url)}catch{if("manual"!==k.redirect){j(new n(`uri requested responds with an invalid redirect URL: ${f}`,"invalid-redirect")),x();return}}switch(k.redirect){case"error":j(new n(`uri requested responds with a redirect, redirect mode is set to error: ${k.url}`,"no-redirect")),x();return;case"manual":break;case"follow":{var e;if(null===h)break;if(k.counter>=k.follow){j(new n(`maximum redirect reached at: ${k.url}`,"max-redirect")),x();return}let f={headers:new B(k.headers),follow:k.follow,counter:k.counter+1,agent:k.agent,compress:k.compress,method:k.method,body:v(k),signal:k.signal,size:k.size,referrer:k.referrer,referrerPolicy:k.referrerPolicy};if(!((a,b)=>{let c=new URL(b).hostname,d=new URL(a).hostname;return c===d||c.endsWith(`.${d}`)})(k.url,h)||(e=k.url,new URL(h).protocol!==new URL(e).protocol))for(let a of["authorization","www-authenticate","cookie","cookie2"])f.headers.delete(a);if(303!==a.statusCode&&k.body&&b.body instanceof g.Readable){j(new n("Cannot follow redirect with body being a readable stream","unsupported-redirect")),x();return}(303===a.statusCode||(301===a.statusCode||302===a.statusCode)&&"POST"===k.method)&&(f.method="GET",f.body=void 0,f.headers.delete("content-length"));let i=function(a){let b=(a.get("referrer-policy")||"").split(/[,\s]+/),c="";for(let a of b)a&&J.has(a)&&(c=a);return c}(d);i&&(f.referrerPolicy=i),c(S(new O(h,f))),x();return}default:return j(TypeError(`Redirect option '${k.redirect}' is not a valid value of RequestRedirect`))}}p&&a.once("end",()=>{p.removeEventListener("abort",u)});let h=(0,g.pipeline)(a,new g.PassThrough,a=>{a&&j(a)});process.version<"v12.10"&&a.on("aborted",u);let i={url:k.url,status:a.statusCode,statusText:a.statusMessage,headers:d,size:k.size,counter:k.counter,highWaterMark:k.highWaterMark},l=d.get("Content-Encoding");if(!k.compress||"HEAD"===k.method||null===l||204===a.statusCode||304===a.statusCode)return void c(r=new F(h,i));let m={flush:f.Z_SYNC_FLUSH,finishFlush:f.Z_SYNC_FLUSH};if("gzip"===l||"x-gzip"===l)return void c(r=new F(h=(0,g.pipeline)(h,f.createGunzip(m),a=>{a&&j(a)}),i));if("deflate"===l||"x-deflate"===l){let b=(0,g.pipeline)(a,new g.PassThrough,a=>{a&&j(a)});b.once("data",a=>{c(r=new F(h=(15&a[0])==8?(0,g.pipeline)(h,f.createInflate(),a=>{a&&j(a)}):(0,g.pipeline)(h,f.createInflateRaw(),a=>{a&&j(a)}),i))}),b.once("end",()=>{r||c(r=new F(h,i))});return}if("br"===l)return void c(r=new F(h=(0,g.pipeline)(h,f.createBrotliDecompress(),a=>{a&&j(a)}),i));c(r=new F(h,i))}),y(w,k).catch(j)})}},70451:(a,b,c)=>{"use strict";c.d(b,{$n:()=>o,fS:()=>n});var d=c(26204),e=c(86596),{toStringTag:f,iterator:g,hasInstance:h}=Symbol,i=Math.random,j="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),k=(a,b,c)=>(a+="",/^(Blob|File)$/.test(b&&b[f])?[(c=void 0!==c?c+"":"File"==b[f]?b.name:"blob",a),b.name!==c||"blob"==b[f]?new e.A([b],c,b):b]:[a,b+""]),l=(a,b)=>(b?a:a.replace(/\r?\n|\r/g,"\r\n")).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),m=(a,b,c)=>{if(b.length<c)throw TypeError(`Failed to execute '${a}' on 'FormData': ${c} arguments required, but only ${b.length} present.`)};let n=class{#g=[];constructor(...a){if(a.length)throw TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[f](){return"FormData"}[g](){return this.entries()}static[h](a){return a&&"object"==typeof a&&"FormData"===a[f]&&!j.some(b=>"function"!=typeof a[b])}append(...a){m("append",arguments,2),this.#g.push(k(...a))}delete(a){m("delete",arguments,1),a+="",this.#g=this.#g.filter(([b])=>b!==a)}get(a){m("get",arguments,1),a+="";for(var b=this.#g,c=b.length,d=0;d<c;d++)if(b[d][0]===a)return b[d][1];return null}getAll(a,b){return m("getAll",arguments,1),b=[],a+="",this.#g.forEach(c=>c[0]===a&&b.push(c[1])),b}has(a){return m("has",arguments,1),a+="",this.#g.some(b=>b[0]===a)}forEach(a,b){for(var[c,d]of(m("forEach",arguments,1),this))a.call(b,d,c,this)}set(...a){m("set",arguments,2);var b=[],c=!0;a=k(...a),this.#g.forEach(d=>{d[0]===a[0]?c&&(c=!b.push(a)):b.push(d)}),c&&b.push(a),this.#g=b}*entries(){yield*this.#g}*keys(){for(var[a]of this)yield a}*values(){for(var[,a]of this)yield a}};function o(a,b=d.A){var c=`${i()}${i()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),e=[],f=`--${c}\r
Content-Disposition: form-data; name="`;return a.forEach((a,b)=>"string"==typeof a?e.push(f+l(b)+`"\r
\r
${a.replace(/\r(?!\n)|(?<!\r)\n/g,"\r\n")}\r
`):e.push(f+l(b)+`"; filename="${l(a.name,1)}"\r
Content-Type: ${a.type||"application/octet-stream"}\r
\r
`,a,"\r\n")),e.push(`--${c}--`),new b(e,{type:"multipart/form-data; boundary="+c})}},75484:(a,b,c)=>{if(!globalThis.ReadableStream)try{let a=c(1708),{emitWarning:b}=a;try{a.emitWarning=()=>{},Object.assign(globalThis,c(37830)),a.emitWarning=b}catch(c){throw a.emitWarning=b,c}}catch(a){Object.assign(globalThis,c(63707))}try{let{Blob:a}=c(79428);a&&!a.prototype.stream&&(a.prototype.stream=function(a){let b=0,c=this;return new ReadableStream({type:"bytes",async pull(a){let d=c.slice(b,Math.min(c.size,b+65536)),e=await d.arrayBuffer();b+=e.byteLength,a.enqueue(new Uint8Array(e)),b===c.size&&a.close()}})})}catch(a){}},77680:(a,b,c)=>{if(!globalThis.DOMException)try{let{MessageChannel:a}=c(73566),b=new a().port1,d=new ArrayBuffer;b.postMessage(d,[d,d])}catch(a){"DOMException"===a.constructor.name&&(globalThis.DOMException=a.constructor)}a.exports=globalThis.DOMException},86596:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(26204);let e=class extends d.A{#h=0;#i="";constructor(a,b,c={}){if(arguments.length<2)throw TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(a,c),null===c&&(c={});let d=void 0===c.lastModified?Date.now():Number(c.lastModified);Number.isNaN(d)||(this.#h=d),this.#i=String(b)}get name(){return this.#i}get lastModified(){return this.#h}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](a){return!!a&&a instanceof d.A&&/^(File)$/.test(a[Symbol.toStringTag])}}}};