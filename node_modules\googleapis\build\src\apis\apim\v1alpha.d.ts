import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace apim_v1alpha {
    export interface Options extends GlobalOptions {
        version: 'v1alpha';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * API Management API
     *
     * Enables users to discover shadow APIs in existing Google Cloud infrastructure.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const apim = google.apim('v1alpha');
     * ```
     */
    export class Apim {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Message describing ApiObservation object
     */
    export interface Schema$ApiObservation {
        /**
         * The number of observed API Operations.
         */
        apiOperationCount?: string | null;
        /**
         * Create time stamp
         */
        createTime?: string | null;
        /**
         * The hostname of requests processed for this Observation.
         */
        hostname?: string | null;
        /**
         * Last event detected time stamp
         */
        lastEventDetectedTime?: string | null;
        /**
         * Identifier. Name of resource
         */
        name?: string | null;
        /**
         * The IP address (IPv4 or IPv6) of the origin server that the request was sent to. This field can include port information. Examples: `"***********"`, `"********:80"`, `"FE80::0202:B3FF:FE1E:8329"`.
         */
        serverIps?: string[] | null;
        /**
         * Location of the Observation Source, for example "us-central1" or "europe-west1."
         */
        sourceLocations?: string[] | null;
        /**
         * Style of ApiObservation
         */
        style?: string | null;
        /**
         * User-defined tags to organize and sort
         */
        tags?: string[] | null;
        /**
         * Update time stamp
         */
        updateTime?: string | null;
    }
    /**
     * Message describing ApiOperation object
     */
    export interface Schema$ApiOperation {
        /**
         * The number of occurrences of this API Operation.
         */
        count?: string | null;
        /**
         * First seen time stamp
         */
        firstSeenTime?: string | null;
        /**
         * An HTTP Operation.
         */
        httpOperation?: Schema$HttpOperation;
        /**
         * Last seen time stamp
         */
        lastSeenTime?: string | null;
        /**
         * Identifier. Name of resource
         */
        name?: string | null;
    }
    /**
     * Message for requesting batch edit tags for ApiObservations
     */
    export interface Schema$BatchEditTagsApiObservationsRequest {
        /**
         * Required. The request message specifying the resources to update. A maximum of 1000 apiObservations can be modified in a batch.
         */
        requests?: Schema$EditTagsApiObservationsRequest[];
    }
    /**
     * Message for response to edit Tags for ApiObservations
     */
    export interface Schema$BatchEditTagsApiObservationsResponse {
        /**
         * ApiObservations that were changed
         */
        apiObservations?: Schema$ApiObservation[];
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Message for disabling an ObservationJob
     */
    export interface Schema$DisableObservationJobRequest {
    }
    /**
     * Message for requesting edit tags for ApiObservation
     */
    export interface Schema$EditTagsApiObservationsRequest {
        /**
         * Required. Identifier of ApiObservation need to be edit tags Format example: "apigee.googleapis.com|us-west1|443"
         */
        apiObservationId?: string | null;
        /**
         * Required. Tag actions to be applied
         */
        tagActions?: Schema$TagAction[];
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Message for enabling an ObservationJob
     */
    export interface Schema$EnableObservationJobRequest {
    }
    /**
     * The GCLB observation source.
     */
    export interface Schema$GclbObservationSource {
        /**
         * Required. The VPC networks where traffic will be observed. All load balancers within this network will be observed. Currently, this is limited to only one network.
         */
        pscNetworkConfigs?: Schema$GclbObservationSourcePscNetworkConfig[];
    }
    /**
     * Network information for setting up a PSC connection.
     */
    export interface Schema$GclbObservationSourcePscNetworkConfig {
        /**
         * Required. The VPC network. Format: `projects/{project_id\}/global/networks/{network\}`
         */
        network?: string | null;
        /**
         * Required. The subnetwork in the source region that will be used to connect to the Cloud Load Balancers via PSC NEGs. Must belong to `network`. Format: projects/{project_id\}/regions/{region\}/subnetworks/{subnet\}
         */
        subnetwork?: string | null;
    }
    /**
     * An HTTP-based API Operation, sometimes called a "REST" Operation.
     */
    export interface Schema$HttpOperation {
        /**
         * HTTP Method.
         */
        method?: string | null;
        /**
         * Path of the HTTP request.
         */
        path?: string | null;
        /**
         * Path params of HttpOperation
         */
        pathParams?: Schema$HttpOperationPathParam[];
        /**
         * Query params of HttpOperation
         */
        queryParams?: {
            [key: string]: Schema$HttpOperationQueryParam;
        } | null;
        /**
         * Request metadata.
         */
        request?: Schema$HttpOperationHttpRequest;
        /**
         * Response metadata.
         */
        response?: Schema$HttpOperationHttpResponse;
    }
    /**
     * An aggregation of HTTP header occurrences.
     */
    export interface Schema$HttpOperationHeader {
        /**
         * The number of occurrences of this Header across transactions.
         */
        count?: string | null;
        /**
         * Data type of header
         */
        dataType?: string | null;
        /**
         * Header name.
         */
        name?: string | null;
    }
    /**
     * An aggregation of HTTP requests.
     */
    export interface Schema$HttpOperationHttpRequest {
        /**
         * Unordered map from header name to header metadata
         */
        headers?: {
            [key: string]: Schema$HttpOperationHeader;
        } | null;
    }
    /**
     * An aggregation of HTTP responses.
     */
    export interface Schema$HttpOperationHttpResponse {
        /**
         * Unordered map from header name to header metadata
         */
        headers?: {
            [key: string]: Schema$HttpOperationHeader;
        } | null;
        /**
         * Map of status code to observed count
         */
        responseCodes?: {
            [key: string]: string;
        } | null;
    }
    /**
     * HTTP Path parameter.
     */
    export interface Schema$HttpOperationPathParam {
        /**
         * Data type of path param
         */
        dataType?: string | null;
        /**
         * Segment location in the path, 1-indexed
         */
        position?: number | null;
    }
    /**
     * An aggregation of HTTP query parameter occurrences.
     */
    export interface Schema$HttpOperationQueryParam {
        /**
         * The number of occurrences of this query parameter across transactions.
         */
        count?: string | null;
        /**
         * Data type of path param
         */
        dataType?: string | null;
        /**
         * Name of query param
         */
        name?: string | null;
    }
    /**
     * Message for response to listing ApiObservations
     */
    export interface Schema$ListApiObservationsResponse {
        /**
         * The ApiObservation from the specified project and location and ObservationJobs.
         */
        apiObservations?: Schema$ApiObservation[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * Message for response to listing tags
     */
    export interface Schema$ListApiObservationTagsResponse {
        /**
         * The tags from the specified project
         */
        apiObservationTags?: string[] | null;
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * Message for response to listing ApiOperations
     */
    export interface Schema$ListApiOperationsResponse {
        /**
         * The ApiOperations from the specified project and location and ObservationJob and ApiObservation.
         */
        apiOperations?: Schema$ApiOperation[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * Message for response to listing ObservationJobs
     */
    export interface Schema$ListObservationJobsResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The ObservationJob from the specified project and location.
         */
        observationJobs?: Schema$ObservationJob[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Message for response to listing ObservationSources
     */
    export interface Schema$ListObservationSourcesResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The ObservationSource from the specified project and location.
         */
        observationSources?: Schema$ObservationSource[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Message describing ObservationJob object
     */
    export interface Schema$ObservationJob {
        /**
         * Output only. [Output only] Create time stamp
         */
        createTime?: string | null;
        /**
         * Identifier. name of resource Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}
         */
        name?: string | null;
        /**
         * Optional. These should be of the same kind of source.
         */
        sources?: string[] | null;
        /**
         * Output only. The observation job state
         */
        state?: string | null;
        /**
         * Output only. [Output only] Update time stamp
         */
        updateTime?: string | null;
    }
    /**
     * Observation source configuration types
     */
    export interface Schema$ObservationSource {
        /**
         * Output only. [Output only] Create time stamp
         */
        createTime?: string | null;
        /**
         * The GCLB observation source
         */
        gclbObservationSource?: Schema$GclbObservationSource;
        /**
         * Identifier. name of resource For MVP, each region can only have 1 source.
         */
        name?: string | null;
        /**
         * Output only. The observation source state
         */
        state?: string | null;
        /**
         * Output only. [Output only] Update time stamp
         */
        updateTime?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Message for edit tag action
     */
    export interface Schema$TagAction {
        /**
         * Required. Action to be applied
         */
        action?: string | null;
        /**
         * Required. Tag to be added or removed
         */
        tag?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        observationJobs: Resource$Projects$Locations$Observationjobs;
        observationSources: Resource$Projects$Locations$Observationsources;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.get({
         *     // Resource name for the location.
         *     name: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "displayName": "my_displayName",
         *   //   "labels": {},
         *   //   "locationId": "my_locationId",
         *   //   "metadata": {},
         *   //   "name": "my_name"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.list({
         *     // Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         *     extraLocationTypes: 'placeholder-value',
         *     // A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         *     filter: 'placeholder-value',
         *     // The resource that owns the locations collection, if applicable.
         *     name: 'projects/my-project',
         *     // The maximum number of results to return. If not set, the service selects a default.
         *     pageSize: 'placeholder-value',
         *     // A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "locations": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        /**
         * ListApiObservationTags lists all extant tags on any observation in the given project.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.listApiObservationTags({
         *     // Optional. The maximum number of tags to return. The service may return fewer than this value. If unspecified, at most 10 tags will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         *     pageSize: 'placeholder-value',
         *     // Optional. A page token, received from a previous `ListApiObservationTags` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiObservationTags` must match the call that provided the page token.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent, which owns this collection of tags. Format: projects/{project\}/locations/{location\}
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "apiObservationTags": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listApiObservationTags(params: Params$Resource$Projects$Locations$Listapiobservationtags, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listApiObservationTags(params?: Params$Resource$Projects$Locations$Listapiobservationtags, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListApiObservationTagsResponse>>;
        listApiObservationTags(params: Params$Resource$Projects$Locations$Listapiobservationtags, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listApiObservationTags(params: Params$Resource$Projects$Locations$Listapiobservationtags, options: MethodOptions | BodyResponseCallback<Schema$ListApiObservationTagsResponse>, callback: BodyResponseCallback<Schema$ListApiObservationTagsResponse>): void;
        listApiObservationTags(params: Params$Resource$Projects$Locations$Listapiobservationtags, callback: BodyResponseCallback<Schema$ListApiObservationTagsResponse>): void;
        listApiObservationTags(callback: BodyResponseCallback<Schema$ListApiObservationTagsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Locations$Listapiobservationtags extends StandardParameters {
        /**
         * Optional. The maximum number of tags to return. The service may return fewer than this value. If unspecified, at most 10 tags will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListApiObservationTags` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiObservationTags` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of tags. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Observationjobs {
        context: APIRequestContext;
        apiObservations: Resource$Projects$Locations$Observationjobs$Apiobservations;
        constructor(context: APIRequestContext);
        /**
         * CreateObservationJob creates a new ObservationJob but does not have any effecton its own. It is a configuration that can be used in an Observation Job to collect data about existing APIs.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationJobs.create({
         *     // Required. The ID to use for the Observation Job. This value should be 4-63 characters, and valid characters are /a-z-/.
         *     observationJobId: 'placeholder-value',
         *     // Required. The parent resource where this ObservationJob will be created. Format: projects/{project\}/locations/{location\}
         *     parent: 'projects/my-project/locations/my-location',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "name": "my_name",
         *       //   "sources": [],
         *       //   "state": "my_state",
         *       //   "updateTime": "my_updateTime"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Observationjobs$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Observationjobs$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Observationjobs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Observationjobs$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Observationjobs$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * DeleteObservationJob deletes an ObservationJob. This method will fail if the observation job is currently being used by any ObservationSource, even if not enabled.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationJobs.delete({
         *     // Required. Name of the resource Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}
         *     name: 'projects/my-project/locations/my-location/observationJobs/my-observationJob',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Observationjobs$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Observationjobs$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Observationjobs$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Observationjobs$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Observationjobs$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Disables the given ObservationJob.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationJobs.disable({
         *     // Required. The name of the ObservationJob to disable. Format: projects/{project\}/locations/{location\}/observationJobs/{job\}
         *     name: 'projects/my-project/locations/my-location/observationJobs/my-observationJob',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        disable(params: Params$Resource$Projects$Locations$Observationjobs$Disable, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        disable(params?: Params$Resource$Projects$Locations$Observationjobs$Disable, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        disable(params: Params$Resource$Projects$Locations$Observationjobs$Disable, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        disable(params: Params$Resource$Projects$Locations$Observationjobs$Disable, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        disable(params: Params$Resource$Projects$Locations$Observationjobs$Disable, callback: BodyResponseCallback<Schema$Operation>): void;
        disable(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Enables the given ObservationJob.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationJobs.enable({
         *     // Required. The name of the ObservationJob to enable. Format: projects/{project\}/locations/{location\}/observationJobs/{job\}
         *     name: 'projects/my-project/locations/my-location/observationJobs/my-observationJob',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        enable(params: Params$Resource$Projects$Locations$Observationjobs$Enable, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        enable(params?: Params$Resource$Projects$Locations$Observationjobs$Enable, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        enable(params: Params$Resource$Projects$Locations$Observationjobs$Enable, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        enable(params: Params$Resource$Projects$Locations$Observationjobs$Enable, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        enable(params: Params$Resource$Projects$Locations$Observationjobs$Enable, callback: BodyResponseCallback<Schema$Operation>): void;
        enable(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * GetObservationJob retrieves a single ObservationJob by name.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationJobs.get({
         *     // Required. The name of the ObservationJob to retrieve. Format: projects/{project\}/locations/{location\}/observationJobs/{job\}
         *     name: 'projects/my-project/locations/my-location/observationJobs/my-observationJob',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "name": "my_name",
         *   //   "sources": [],
         *   //   "state": "my_state",
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Observationjobs$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Observationjobs$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ObservationJob>>;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Get, options: MethodOptions | BodyResponseCallback<Schema$ObservationJob>, callback: BodyResponseCallback<Schema$ObservationJob>): void;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Get, callback: BodyResponseCallback<Schema$ObservationJob>): void;
        get(callback: BodyResponseCallback<Schema$ObservationJob>): void;
        /**
         * ListObservationJobs gets all ObservationJobs for a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationJobs.list({
         *     // Optional. The maximum number of ObservationJobs to return. The service may return fewer than this value. If unspecified, at most 10 ObservationJobs will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         *     pageSize: 'placeholder-value',
         *     // Optional. A page token, received from a previous `ListObservationJobs` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListObservationJobs` must match the call that provided the page token.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent, which owns this collection of ObservationJobs. Format: projects/{project\}/locations/{location\}
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "observationJobs": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Observationjobs$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Observationjobs$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListObservationJobsResponse>>;
        list(params: Params$Resource$Projects$Locations$Observationjobs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Observationjobs$List, options: MethodOptions | BodyResponseCallback<Schema$ListObservationJobsResponse>, callback: BodyResponseCallback<Schema$ListObservationJobsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Observationjobs$List, callback: BodyResponseCallback<Schema$ListObservationJobsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListObservationJobsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Create extends StandardParameters {
        /**
         * Required. The ID to use for the Observation Job. This value should be 4-63 characters, and valid characters are /a-z-/.
         */
        observationJobId?: string;
        /**
         * Required. The parent resource where this ObservationJob will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ObservationJob;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Delete extends StandardParameters {
        /**
         * Required. Name of the resource Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Disable extends StandardParameters {
        /**
         * Required. The name of the ObservationJob to disable. Format: projects/{project\}/locations/{location\}/observationJobs/{job\}
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DisableObservationJobRequest;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Enable extends StandardParameters {
        /**
         * Required. The name of the ObservationJob to enable. Format: projects/{project\}/locations/{location\}/observationJobs/{job\}
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$EnableObservationJobRequest;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Get extends StandardParameters {
        /**
         * Required. The name of the ObservationJob to retrieve. Format: projects/{project\}/locations/{location\}/observationJobs/{job\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$List extends StandardParameters {
        /**
         * Optional. The maximum number of ObservationJobs to return. The service may return fewer than this value. If unspecified, at most 10 ObservationJobs will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListObservationJobs` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListObservationJobs` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of ObservationJobs. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Observationjobs$Apiobservations {
        context: APIRequestContext;
        apiOperations: Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations;
        constructor(context: APIRequestContext);
        /**
         * BatchEditTagsApiObservations adds or removes Tags for ApiObservations.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await apim.projects.locations.observationJobs.apiObservations.batchEditTags(
         *       {
         *         // Required. The parent resource shared by all ApiObservations being edited. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}
         *         parent:
         *           'projects/my-project/locations/my-location/observationJobs/my-observationJob',
         *
         *         // Request body metadata
         *         requestBody: {
         *           // request body parameters
         *           // {
         *           //   "requests": []
         *           // }
         *         },
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "apiObservations": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchEditTags(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Batchedittags, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchEditTags(params?: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Batchedittags, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$BatchEditTagsApiObservationsResponse>>;
        batchEditTags(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Batchedittags, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchEditTags(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Batchedittags, options: MethodOptions | BodyResponseCallback<Schema$BatchEditTagsApiObservationsResponse>, callback: BodyResponseCallback<Schema$BatchEditTagsApiObservationsResponse>): void;
        batchEditTags(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Batchedittags, callback: BodyResponseCallback<Schema$BatchEditTagsApiObservationsResponse>): void;
        batchEditTags(callback: BodyResponseCallback<Schema$BatchEditTagsApiObservationsResponse>): void;
        /**
         * GetApiObservation retrieves a single ApiObservation by name.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationJobs.apiObservations.get(
         *     {
         *       // Required. The name of the ApiObservation to retrieve. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}/apiObservations/{api_observation\}
         *       name: 'projects/my-project/locations/my-location/observationJobs/my-observationJob/apiObservations/my-apiObservation',
         *     },
         *   );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "apiOperationCount": "my_apiOperationCount",
         *   //   "createTime": "my_createTime",
         *   //   "hostname": "my_hostname",
         *   //   "lastEventDetectedTime": "my_lastEventDetectedTime",
         *   //   "name": "my_name",
         *   //   "serverIps": [],
         *   //   "sourceLocations": [],
         *   //   "style": "my_style",
         *   //   "tags": [],
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ApiObservation>>;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Get, options: MethodOptions | BodyResponseCallback<Schema$ApiObservation>, callback: BodyResponseCallback<Schema$ApiObservation>): void;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Get, callback: BodyResponseCallback<Schema$ApiObservation>): void;
        get(callback: BodyResponseCallback<Schema$ApiObservation>): void;
        /**
         * ListApiObservations gets all ApiObservations for a given project and location and ObservationJob.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await apim.projects.locations.observationJobs.apiObservations.list({
         *       // Optional. The maximum number of ApiObservations to return. The service may return fewer than this value. If unspecified, at most 10 ApiObservations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         *       pageSize: 'placeholder-value',
         *       // Optional. A page token, received from a previous `ListApiObservations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiObservations` must match the call that provided the page token.
         *       pageToken: 'placeholder-value',
         *       // Required. The parent, which owns this collection of ApiObservations. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}
         *       parent:
         *         'projects/my-project/locations/my-location/observationJobs/my-observationJob',
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "apiObservations": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListApiObservationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$List, options: MethodOptions | BodyResponseCallback<Schema$ListApiObservationsResponse>, callback: BodyResponseCallback<Schema$ListApiObservationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$List, callback: BodyResponseCallback<Schema$ListApiObservationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListApiObservationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Batchedittags extends StandardParameters {
        /**
         * Required. The parent resource shared by all ApiObservations being edited. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BatchEditTagsApiObservationsRequest;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Get extends StandardParameters {
        /**
         * Required. The name of the ApiObservation to retrieve. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}/apiObservations/{api_observation\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Apiobservations$List extends StandardParameters {
        /**
         * Optional. The maximum number of ApiObservations to return. The service may return fewer than this value. If unspecified, at most 10 ApiObservations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListApiObservations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiObservations` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of ApiObservations. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * GetApiOperation retrieves a single ApiOperation by name.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await apim.projects.locations.observationJobs.apiObservations.apiOperations.get(
         *       {
         *         // Required. The name of the ApiOperation to retrieve. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}/apiObservations/{api_observation\}/apiOperation/{api_operation\}
         *         name: 'projects/my-project/locations/my-location/observationJobs/my-observationJob/apiObservations/my-apiObservation/apiOperations/my-apiOperation',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "count": "my_count",
         *   //   "firstSeenTime": "my_firstSeenTime",
         *   //   "httpOperation": {},
         *   //   "lastSeenTime": "my_lastSeenTime",
         *   //   "name": "my_name"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ApiOperation>>;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$Get, options: MethodOptions | BodyResponseCallback<Schema$ApiOperation>, callback: BodyResponseCallback<Schema$ApiOperation>): void;
        get(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$Get, callback: BodyResponseCallback<Schema$ApiOperation>): void;
        get(callback: BodyResponseCallback<Schema$ApiOperation>): void;
        /**
         * ListApiOperations gets all ApiOperations for a given project and location and ObservationJob and ApiObservation.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await apim.projects.locations.observationJobs.apiObservations.apiOperations.list(
         *       {
         *         // Optional. The maximum number of ApiOperations to return. The service may return fewer than this value. If unspecified, at most 10 ApiOperations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         *         pageSize: 'placeholder-value',
         *         // Optional. A page token, received from a previous `ListApiApiOperations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiApiOperations` must match the call that provided the page token.
         *         pageToken: 'placeholder-value',
         *         // Required. The parent, which owns this collection of ApiOperations. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}/apiObservations/{api_observation\}
         *         parent:
         *           'projects/my-project/locations/my-location/observationJobs/my-observationJob/apiObservations/my-apiObservation',
         *       },
         *     );
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "apiOperations": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListApiOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$List, options: MethodOptions | BodyResponseCallback<Schema$ListApiOperationsResponse>, callback: BodyResponseCallback<Schema$ListApiOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$List, callback: BodyResponseCallback<Schema$ListApiOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListApiOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$Get extends StandardParameters {
        /**
         * Required. The name of the ApiOperation to retrieve. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}/apiObservations/{api_observation\}/apiOperation/{api_operation\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Observationjobs$Apiobservations$Apioperations$List extends StandardParameters {
        /**
         * Optional. The maximum number of ApiOperations to return. The service may return fewer than this value. If unspecified, at most 10 ApiOperations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListApiApiOperations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiApiOperations` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of ApiOperations. Format: projects/{project\}/locations/{location\}/observationJobs/{observation_job\}/apiObservations/{api_observation\}
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Observationsources {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * CreateObservationSource creates a new ObservationSource but does not affect any deployed infrastructure. It is a configuration that can be used in an Observation Job to collect data about APIs running in user's dataplane.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationSources.create({
         *     // Required. The ID to use for the Observation Source. This value should be 4-63 characters, and valid characters are /a-z-/.
         *     observationSourceId: 'placeholder-value',
         *     // Required. Value for parent.
         *     parent: 'projects/my-project/locations/my-location',
         *     // Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         *     requestId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "gclbObservationSource": {},
         *       //   "name": "my_name",
         *       //   "state": "my_state",
         *       //   "updateTime": "my_updateTime"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Observationsources$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Observationsources$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Observationsources$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Observationsources$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Observationsources$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * DeleteObservationSource deletes an observation source. This method will fail if the observation source is currently being used by any ObservationJob, even if not enabled.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationSources.delete({
         *     // Required. Name of the resource Format: projects/{project\}/locations/{location\}/observationSources/{source\}
         *     name: 'projects/my-project/locations/my-location/observationSources/my-observationSource',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Observationsources$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Observationsources$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Observationsources$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Observationsources$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Observationsources$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * GetObservationSource retrieves a single ObservationSource by name.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationSources.get({
         *     // Required. The name of the ObservationSource to retrieve. Format: projects/{project\}/locations/{location\}/observationSources/{source\}
         *     name: 'projects/my-project/locations/my-location/observationSources/my-observationSource',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "gclbObservationSource": {},
         *   //   "name": "my_name",
         *   //   "state": "my_state",
         *   //   "updateTime": "my_updateTime"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Observationsources$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Observationsources$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ObservationSource>>;
        get(params: Params$Resource$Projects$Locations$Observationsources$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Observationsources$Get, options: MethodOptions | BodyResponseCallback<Schema$ObservationSource>, callback: BodyResponseCallback<Schema$ObservationSource>): void;
        get(params: Params$Resource$Projects$Locations$Observationsources$Get, callback: BodyResponseCallback<Schema$ObservationSource>): void;
        get(callback: BodyResponseCallback<Schema$ObservationSource>): void;
        /**
         * ListObservationSources gets all ObservationSources for a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.observationSources.list({
         *     // Optional. The maximum number of ObservationSources to return. The service may return fewer than this value. If unspecified, at most 10 ObservationSources will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         *     pageSize: 'placeholder-value',
         *     // Optional. A page token, received from a previous `ListObservationSources` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListObservationSources` must match the call that provided the page token.
         *     pageToken: 'placeholder-value',
         *     // Required. The parent, which owns this collection of ObservationSources. Format: projects/{project\}/locations/{location\}
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "observationSources": [],
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Observationsources$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Observationsources$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListObservationSourcesResponse>>;
        list(params: Params$Resource$Projects$Locations$Observationsources$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Observationsources$List, options: MethodOptions | BodyResponseCallback<Schema$ListObservationSourcesResponse>, callback: BodyResponseCallback<Schema$ListObservationSourcesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Observationsources$List, callback: BodyResponseCallback<Schema$ListObservationSourcesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListObservationSourcesResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Observationsources$Create extends StandardParameters {
        /**
         * Required. The ID to use for the Observation Source. This value should be 4-63 characters, and valid characters are /a-z-/.
         */
        observationSourceId?: string;
        /**
         * Required. Value for parent.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ObservationSource;
    }
    export interface Params$Resource$Projects$Locations$Observationsources$Delete extends StandardParameters {
        /**
         * Required. Name of the resource Format: projects/{project\}/locations/{location\}/observationSources/{source\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Observationsources$Get extends StandardParameters {
        /**
         * Required. The name of the ObservationSource to retrieve. Format: projects/{project\}/locations/{location\}/observationSources/{source\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Observationsources$List extends StandardParameters {
        /**
         * Optional. The maximum number of ObservationSources to return. The service may return fewer than this value. If unspecified, at most 10 ObservationSources will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListObservationSources` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListObservationSources` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of ObservationSources. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.operations.cancel({
         *     // The name of the operation resource to be cancelled.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.operations.delete({
         *     // The name of the operation resource to be deleted.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.operations.get({
         *     // The name of the operation resource.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/apim.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const apim = google.apim('v1alpha');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await apim.projects.locations.operations.list({
         *     // The standard list filter.
         *     filter: 'placeholder-value',
         *     // The name of the operation's parent resource.
         *     name: 'projects/my-project/locations/my-location',
         *     // The standard list page size.
         *     pageSize: 'placeholder-value',
         *     // The standard list page token.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "operations": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export {};
}
