import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { databaseService } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile and check admin role
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile || userProfile.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get dashboard statistics
    const stats = await databaseService.getDashboardStats();

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get admin stats error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to get statistics' 
      },
      { status: 500 }
    );
  }
}
