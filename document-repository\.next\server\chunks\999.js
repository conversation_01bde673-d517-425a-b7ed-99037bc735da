"use strict";exports.id=999,exports.ids=[999],exports.modules={44999:(a,b,c)=>{c.d(b,{cookies:()=>d.U});var d=c(99933);c(86280),c(73913)},73913:(a,b,c)=>{let d=c(63033),e=c(29294),f=c(84971),g=c(76926),h=c(80023),i=c(98479),j=c(71617);c(43763);new WeakMap;(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},76926:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(61120));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})},86280:(a,b,c)=>{let d=c(92584),e=c(29294),f=c(63033),g=c(84971),h=c(80023),i=c(68388),j=c(76926);c(44523);c(8719),c(71617);c(43763);new WeakMap;(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},94069:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return h},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return o},getModifiedCookieValues:function(){return k},responseCookiesToRequestCookies:function(){return q},wrapWithMutableAccessCheck:function(){return n}});let d=c(23158),e=c(43763),f=c(29294),g=c(63033);class h extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new h}}class i{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return h.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let j=Symbol.for("next.mutated.cookies");function k(a){let b=a[j];return b&&Array.isArray(b)&&0!==b.length?b:[]}function l(a,b){let c=k(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class m{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,i=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case j:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{i()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{i()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function n(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return p("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return p("cookies().set"),a.set(...c),b};default:return e.ReflectAdapter.get(a,c,d)}}});return b}function o(a){return"action"===a.phase}function p(a){if(!o((0,g.getExpectedRequestStore)(a)))throw new h}function q(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}},99933:(a,b,c)=>{Object.defineProperty(b,"U",{enumerable:!0,get:function(){return n}});let d=c(94069),e=c(23158),f=c(29294),g=c(63033),h=c(84971),i=c(80023),j=c(68388),k=c(76926);c(44523);let l=c(8719),m=c(71617);function n(){let a="cookies",b=f.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b){if(c&&"after"===c.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(b.forceStatic)return p(d.RequestCookiesAdapter.seal(new e.RequestCookies(new Headers({}))));if(c){if("cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(b.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(c)switch(c.type){case"prerender":var k=c;let f=o.get(k);if(f)return f;let g=(0,j.makeHangingPromise)(k.renderSignal,"`cookies()`");return o.set(k,g),g;case"prerender-client":let n="`cookies`";throw Object.defineProperty(new m.InvariantError(`${n} must not be used within a client component. Next.js should be preventing ${n} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,h.postponeWithTracking)(b.route,a,c.dynamicTracking);break;case"prerender-legacy":(0,h.throwToInterruptStaticGeneration)(a,b,c)}(0,h.trackDynamicDataInDynamicRender)(b,c)}let n=(0,g.getExpectedRequestStore)(a);return p((0,d.areCookiesMutableInCurrentPhase)(n)?n.userspaceMutableCookies:n.cookies)}c(43763);let o=new WeakMap;function p(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),Object.defineProperties(c,{[Symbol.iterator]:{value:a[Symbol.iterator]?a[Symbol.iterator].bind(a):q.bind(a)},size:{get:()=>a.size},get:{value:a.get.bind(a)},getAll:{value:a.getAll.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},delete:{value:a.delete.bind(a)},clear:{value:"function"==typeof a.clear?a.clear.bind(a):r.bind(a,c)},toString:{value:a.toString.bind(a)}}),c}function q(){return this.getAll().map(a=>[a.name,a]).values()}function r(a){for(let a of this.getAll())this.delete(a.name);return a}(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})}};