# API Documentation

This document describes the REST API endpoints available in the Document Repository application.

## Base URL

- Development: `http://localhost:3000`
- Production: `https://your-domain.com`

## Authentication

All API endpoints (except public ones) require authentication. The application uses Supabase Auth with JWT tokens.

### Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Rate Limiting

- **General API**: 100 requests per 15 minutes per IP
- **File Upload**: 10 uploads per 15 minutes per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (ISO 8601)

## Error Responses

All endpoints return errors in the following format:

```json
{
  "success": false,
  "error": "Error message",
  "details": ["Additional error details"] // Optional
}
```

### HTTP Status Codes

- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

## Authentication Endpoints

### Sign Out

```http
POST /api/auth/signout
```

Signs out the current user.

**Response:**
```json
{
  "success": true,
  "message": "Signed out successfully"
}
```

### OAuth Callback

```http
GET /api/auth/callback?code=<auth_code>
```

Handles OAuth authentication callback.

**Parameters:**
- `code` (query) - OAuth authorization code

**Response:**
Redirects to dashboard on success, error page on failure.

## File Management Endpoints

### Upload File

```http
POST /api/files/upload
```

Uploads a new file to the repository.

**Request Body (multipart/form-data):**
- `file` (file) - The file to upload
- `description` (string, optional) - File description
- `tags` (string, optional) - Comma-separated tags
- `isPublic` (boolean, optional) - Whether file is publicly accessible

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "filename": "unique_filename.pdf",
    "original_filename": "document.pdf",
    "file_size": 1024000,
    "file_type": "pdf",
    "mime_type": "application/pdf",
    "google_drive_id": "drive_file_id",
    "google_drive_url": "https://drive.google.com/...",
    "uploaded_by": "user_uuid",
    "uploaded_at": "2023-12-25T10:30:00Z",
    "description": "Important document",
    "tags": ["important", "document"],
    "is_public": false,
    "download_count": 0,
    "status": "completed"
  },
  "message": "File uploaded successfully"
}
```

### Get Files

```http
GET /api/files/upload
```

Retrieves user's files with pagination and filtering.

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10) - Items per page
- `search` (string, optional) - Search term
- `sortBy` (string, optional) - Sort field
- `sortOrder` (string, optional) - Sort order (asc/desc)
- `fileType` (string, optional) - Filter by file type
- `isPublic` (boolean, optional) - Filter by public status
- `tags` (string, optional) - Comma-separated tags to filter

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "filename": "unique_filename.pdf",
      "original_filename": "document.pdf",
      // ... other file properties
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### Get File Details

```http
GET /api/files/{id}
```

Retrieves details of a specific file.

**Parameters:**
- `id` (path) - File UUID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "filename": "unique_filename.pdf",
    // ... all file properties
  }
}
```

### Update File

```http
PUT /api/files/{id}
```

Updates file metadata (owner or admin only).

**Parameters:**
- `id` (path) - File UUID

**Request Body:**
```json
{
  "description": "Updated description",
  "tags": ["tag1", "tag2"],
  "is_public": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    // Updated file object
  },
  "message": "File updated successfully"
}
```

### Delete File

```http
DELETE /api/files/{id}
```

Deletes a file (owner or admin only).

**Parameters:**
- `id` (path) - File UUID

**Response:**
```json
{
  "success": true,
  "message": "File deleted successfully"
}
```

### Download File

```http
GET /api/files/{id}/download
```

Gets download URL for a file.

**Parameters:**
- `id` (path) - File UUID

**Response:**
```json
{
  "success": true,
  "data": {
    "downloadUrl": "https://drive.google.com/uc?export=download&id=...",
    "filename": "document.pdf",
    "fileSize": 1024000,
    "mimeType": "application/pdf"
  }
}
```

### Direct Download

```http
POST /api/files/{id}/download
```

Downloads file content directly (streams file data).

**Parameters:**
- `id` (path) - File UUID

**Response:**
Binary file content with appropriate headers.

## Admin Endpoints

### Get Dashboard Statistics

```http
GET /api/admin/stats
```

Retrieves dashboard statistics (admin only).

**Response:**
```json
{
  "success": true,
  "data": {
    "totalFiles": 150,
    "totalUsers": 25,
    "totalStorage": 1073741824,
    "recentUploads": [
      {
        // Recent file objects
      }
    ]
  }
}
```

### Get All Users

```http
GET /api/admin/users
```

Retrieves all users with pagination (admin only).

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 20) - Items per page
- `search` (string, optional) - Search term
- `sortBy` (string, optional) - Sort field
- `sortOrder` (string, optional) - Sort order (asc/desc)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "role": "user",
      "created_at": "2023-12-25T10:30:00Z",
      "updated_at": "2023-12-25T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "totalPages": 2,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## File Validation Rules

### Allowed File Types

- **Documents**: PDF, DOC, DOCX, TXT
- **Images**: JPG, JPEG, PNG, GIF, WebP
- **Archives**: ZIP, RAR, 7Z
- **Audio**: MP3, WAV, OGG
- **Video**: MP4, AVI, MOV, WebM

### File Size Limits

- **Development**: 10MB per file
- **Production**: 50MB per file (configurable)

### Security Validations

- File type validation (extension and MIME type)
- Content validation (magic number checking)
- Virus scanning (EICAR test detection)
- Filename sanitization
- Path traversal protection

## Webhooks

### File Upload Webhook

Triggered when a file upload is completed.

**Payload:**
```json
{
  "event": "file.uploaded",
  "data": {
    "file_id": "uuid",
    "user_id": "uuid",
    "filename": "document.pdf",
    "file_size": 1024000,
    "uploaded_at": "2023-12-25T10:30:00Z"
  }
}
```

## SDK Examples

### JavaScript/TypeScript

```typescript
// Upload file
const formData = new FormData();
formData.append('file', file);
formData.append('description', 'My document');
formData.append('tags', 'important,work');

const response = await fetch('/api/files/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
```

### cURL

```bash
# Upload file
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.pdf" \
  -F "description=Important document" \
  -F "tags=work,important" \
  http://localhost:3000/api/files/upload

# Get files
curl -X GET \
  -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:3000/api/files/upload?page=1&limit=10"

# Download file
curl -X GET \
  -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/files/{file_id}/download
```

## Error Handling

### Common Error Scenarios

1. **File Too Large**
   ```json
   {
     "success": false,
     "error": "File size too large. Maximum size: 50MB"
   }
   ```

2. **Invalid File Type**
   ```json
   {
     "success": false,
     "error": "File type not allowed",
     "details": ["File type .exe is not allowed for security reasons"]
   }
   ```

3. **Rate Limit Exceeded**
   ```json
   {
     "success": false,
     "error": "Rate limit exceeded. Please try again later.",
     "resetTime": "2023-12-25T11:00:00Z"
   }
   ```

4. **Unauthorized Access**
   ```json
   {
     "success": false,
     "error": "Access denied"
   }
   ```

## Best Practices

### Client Implementation

1. **Always check response status** before processing data
2. **Implement retry logic** for rate-limited requests
3. **Handle file upload progress** for better UX
4. **Validate files client-side** before uploading
5. **Use appropriate timeouts** for large file uploads

### Security Considerations

1. **Never expose sensitive tokens** in client-side code
2. **Validate all user inputs** before sending to API
3. **Implement proper error handling** to avoid information leakage
4. **Use HTTPS** in production environments
5. **Implement CSRF protection** for state-changing operations

### Performance Tips

1. **Use pagination** for large datasets
2. **Implement caching** for frequently accessed data
3. **Compress large files** before uploading
4. **Use appropriate file formats** for different use cases
5. **Monitor API usage** to optimize performance
