(()=>{var a={};a.id=533,a.ids=[533],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1708:a=>{"use strict";a.exports=require("node:process")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:a=>{"use strict";a.exports=require("node:buffer")},6710:(a,b,c)=>{"use strict";c.d(b,{v:()=>f});var d=c(56621);class e{async createUser(a){let{data:b,error:c}=await this.supabase.from("users").insert(a).select().single();if(c)throw c;return b}async getUserById(a){let{data:b,error:c}=await this.supabase.from("users").select("*").eq("id",a).single();if(c&&"PGRST116"!==c.code)throw c;return b}async getUserByEmail(a){let{data:b,error:c}=await this.supabase.from("users").select("*").eq("email",a).single();if(c&&"PGRST116"!==c.code)throw c;return b}async updateUser(a,b){let{data:c,error:d}=await this.supabase.from("users").update(b).eq("id",a).select().single();if(d)throw d;return c}async getAllUsers(a){let b=this.supabase.from("users").select("*",{count:"exact"});a.search&&(b=b.or(`email.ilike.%${a.search}%,full_name.ilike.%${a.search}%`)),b=a.sortBy?b.order(a.sortBy,{ascending:"asc"===a.sortOrder}):b.order("created_at",{ascending:!1});let c=(a.page-1)*a.limit,d=c+a.limit-1;b=b.range(c,d);let{data:e,error:f,count:g}=await b;if(f)throw f;let h=Math.ceil((g||0)/a.limit);return{data:e||[],pagination:{page:a.page,limit:a.limit,total:g||0,totalPages:h,hasNext:a.page<h,hasPrev:a.page>1}}}async createFile(a){let{data:b,error:c}=await this.supabase.from("files").insert(a).select().single();if(c)throw c;return b}async getFileById(a){let{data:b,error:c}=await this.supabase.from("files").select("*").eq("id",a).single();if(c&&"PGRST116"!==c.code)throw c;return b}async updateFile(a,b){let{data:c,error:d}=await this.supabase.from("files").update(b).eq("id",a).select().single();if(d)throw d;return c}async deleteFile(a){let{error:b}=await this.supabase.from("files").delete().eq("id",a);if(b)throw b}async getFilesByUser(a,b){let c=this.supabase.from("files").select("*",{count:"exact"}).eq("uploaded_by",a);b.search&&(c=c.or(`filename.ilike.%${b.search}%,original_filename.ilike.%${b.search}%,description.ilike.%${b.search}%`)),b.filter?.fileType&&(c=c.eq("file_type",b.filter.fileType)),b.filter?.isPublic!==void 0&&(c=c.eq("is_public",b.filter.isPublic)),b.filter?.tags&&b.filter.tags.length>0&&(c=c.overlaps("tags",b.filter.tags)),c=b.sortBy?c.order(b.sortBy,{ascending:"asc"===b.sortOrder}):c.order("uploaded_at",{ascending:!1});let d=(b.page-1)*b.limit,e=d+b.limit-1;c=c.range(d,e);let{data:f,error:g,count:h}=await c;if(g)throw g;let i=Math.ceil((h||0)/b.limit);return{data:f||[],pagination:{page:b.page,limit:b.limit,total:h||0,totalPages:i,hasNext:b.page<i,hasPrev:b.page>1}}}async getAllFiles(a){let b=this.supabase.from("files").select(`
        *,
        users!files_uploaded_by_fkey(email, full_name)
      `,{count:"exact"});a.search&&(b=b.or(`filename.ilike.%${a.search}%,original_filename.ilike.%${a.search}%,description.ilike.%${a.search}%`)),a.filter?.fileType&&(b=b.eq("file_type",a.filter.fileType)),a.filter?.uploadedBy&&(b=b.eq("uploaded_by",a.filter.uploadedBy)),a.filter?.isPublic!==void 0&&(b=b.eq("is_public",a.filter.isPublic)),a.filter?.tags&&a.filter.tags.length>0&&(b=b.overlaps("tags",a.filter.tags)),b=a.sortBy?b.order(a.sortBy,{ascending:"asc"===a.sortOrder}):b.order("uploaded_at",{ascending:!1});let c=(a.page-1)*a.limit,d=c+a.limit-1;b=b.range(c,d);let{data:e,error:f,count:g}=await b;if(f)throw f;let h=Math.ceil((g||0)/a.limit);return{data:e||[],pagination:{page:a.page,limit:a.limit,total:g||0,totalPages:h,hasNext:a.page<h,hasPrev:a.page>1}}}async incrementDownloadCount(a){let{error:b}=await this.supabase.rpc("increment_download_count",{file_id:a});if(b)throw b}async createFilePermission(a){let{data:b,error:c}=await this.supabase.from("file_permissions").insert(a).select().single();if(c)throw c;return b}async getFilePermissions(a){let{data:b,error:c}=await this.supabase.from("file_permissions").select(`
        *,
        users!file_permissions_user_id_fkey(email, full_name)
      `).eq("file_id",a);if(c)throw c;return b||[]}async deleteFilePermission(a){let{error:b}=await this.supabase.from("file_permissions").delete().eq("id",a);if(b)throw b}async createAuditLog(a){let{data:b,error:c}=await this.supabase.from("audit_logs").insert(a).select().single();if(c)throw c;return b}async getAuditLogs(a){let b=this.supabase.from("audit_logs").select(`
        *,
        users!audit_logs_user_id_fkey(email, full_name)
      `,{count:"exact"});a.filter?.userId&&(b=b.eq("user_id",a.filter.userId)),a.filter?.action&&(b=b.eq("action",a.filter.action)),a.filter?.resourceType&&(b=b.eq("resource_type",a.filter.resourceType)),b=a.sortBy?b.order(a.sortBy,{ascending:"asc"===a.sortOrder}):b.order("created_at",{ascending:!1});let c=(a.page-1)*a.limit,d=c+a.limit-1;b=b.range(c,d);let{data:e,error:f,count:g}=await b;if(f)throw f;let h=Math.ceil((g||0)/a.limit);return{data:e||[],pagination:{page:a.page,limit:a.limit,total:g||0,totalPages:h,hasNext:a.page<h,hasPrev:a.page>1}}}async getDashboardStats(){let[a,b,c]=await Promise.all([this.supabase.from("files").select("*",{count:"exact",head:!0}),this.supabase.from("users").select("*",{count:"exact",head:!0}),this.supabase.from("files").select("*").gte("uploaded_at",new Date(Date.now()-6048e5).toISOString()).order("uploaded_at",{ascending:!1}).limit(10)]);return{totalFiles:a.count||0,totalUsers:b.count||0,recentUploads:c.data||[]}}constructor(){this.supabase=(0,d.HK)()}}let f=new e},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";function d(a){let b=Date.now(),c=Math.random().toString(36).substring(2,15),d=a.split(".").pop(),e=a.replace(/\.[^/.]+$/,"");return`${e}_${b}_${c}.${d}`}function e(a){return a.split(".").pop()?.toLowerCase()||""}function f(a){return({jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",webp:"image/webp",svg:"image/svg+xml",pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",txt:"text/plain",csv:"text/csv",html:"text/html",css:"text/css",js:"text/javascript",json:"application/json",xml:"application/xml",zip:"application/zip",rar:"application/x-rar-compressed","7z":"application/x-7z-compressed",tar:"application/x-tar",gz:"application/gzip",mp3:"audio/mpeg",wav:"audio/wav",ogg:"audio/ogg",m4a:"audio/mp4",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",wmv:"video/x-ms-wmv",flv:"video/x-flv",webm:"video/webm"})[a]||"application/octet-stream"}function g(a){let b={};for(let[c,d]of a.entries())"page"===c||"limit"===c?b[c]=parseInt(d,10):"tags"===c?b[c]=d.split(",").filter(Boolean):"isPublic"===c?b[c]="true"===d:b[c]=d;return b}c.d(b,{Og:()=>f,_n:()=>g,lg:()=>e,wh:()=>d})},11723:a=>{"use strict";a.exports=require("querystring")},11997:a=>{"use strict";a.exports=require("punycode")},12412:a=>{"use strict";a.exports=require("assert")},15346:()=>{},16141:a=>{"use strict";a.exports=require("node:zlib")},19771:a=>{"use strict";a.exports=require("process")},20225:(a,b,c)=>{"use strict";c.d(b,{l:()=>g});var d=c(34090),e=c(27910);class f{constructor(){this.auth=new d.q7g.auth.OAuth2(process.env.GOOGLE_DRIVE_CLIENT_ID,process.env.GOOGLE_DRIVE_CLIENT_SECRET,process.env.GOOGLE_DRIVE_REDIRECT_URI),this.auth.setCredentials({refresh_token:process.env.GOOGLE_DRIVE_REFRESH_TOKEN}),this.drive=d.q7g.drive({version:"v3",auth:this.auth})}async uploadFile(a,b,c,d){try{let f={name:b,parents:d?[d]:[process.env.GOOGLE_DRIVE_FOLDER_ID]},g={mimeType:c,body:e.Readable.from(a)},h=await this.drive.files.create({resource:f,media:g,fields:"id,webViewLink,webContentLink"});return await this.drive.permissions.create({fileId:h.data.id,resource:{role:"reader",type:"anyone"}}),{id:h.data.id,webViewLink:h.data.webViewLink,webContentLink:h.data.webContentLink}}catch(a){throw console.error("Error uploading file to Google Drive:",a),Error("Failed to upload file to Google Drive")}}async downloadFile(a){try{let b=await this.drive.files.get({fileId:a,alt:"media"});return Buffer.from(b.data)}catch(a){throw console.error("Error downloading file from Google Drive:",a),Error("Failed to download file from Google Drive")}}async deleteFile(a){try{await this.drive.files.delete({fileId:a})}catch(a){throw console.error("Error deleting file from Google Drive:",a),Error("Failed to delete file from Google Drive")}}async getFileMetadata(a){try{return(await this.drive.files.get({fileId:a,fields:"id,name,size,mimeType,createdTime,modifiedTime,webViewLink,webContentLink"})).data}catch(a){throw console.error("Error getting file metadata from Google Drive:",a),Error("Failed to get file metadata from Google Drive")}}async createFolder(a,b){try{let c={name:a,mimeType:"application/vnd.google-apps.folder",parents:b?[b]:[process.env.GOOGLE_DRIVE_FOLDER_ID]};return(await this.drive.files.create({resource:c,fields:"id"})).data.id}catch(a){throw console.error("Error creating folder in Google Drive:",a),Error("Failed to create folder in Google Drive")}}async listFiles(a,b=10,c){try{let d=a?`'${a}' in parents and trashed=false`:`'${process.env.GOOGLE_DRIVE_FOLDER_ID}' in parents and trashed=false`,e=await this.drive.files.list({q:d,pageSize:b,pageToken:c,fields:"nextPageToken, files(id,name,size,mimeType,createdTime,modifiedTime,webViewLink)",orderBy:"modifiedTime desc"});return{files:e.data.files,nextPageToken:e.data.nextPageToken}}catch(a){throw console.error("Error listing files from Google Drive:",a),Error("Failed to list files from Google Drive")}}async updateFilePermissions(a,b){try{if(b)await this.drive.permissions.create({fileId:a,resource:{role:"reader",type:"anyone"}});else for(let b of(await this.drive.permissions.list({fileId:a})).data.permissions)"anyone"===b.type&&await this.drive.permissions.delete({fileId:a,permissionId:b.id})}catch(a){throw console.error("Error updating file permissions in Google Drive:",a),Error("Failed to update file permissions in Google Drive")}}generateDirectDownloadLink(a){return`https://drive.google.com/uc?export=download&id=${a}`}generatePreviewLink(a){return`https://drive.google.com/file/d/${a}/preview`}}let g=new f},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},35529:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>Q,patchFetch:()=>P,routeModule:()=>L,serverHooks:()=>O,workAsyncStorage:()=>M,workUnitAsyncStorage:()=>N});var d={};c.r(d),c.d(d,{GET:()=>K,POST:()=>J});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(56621),w=c(20225),x=c(6710),y=c(10974),z=c(55511),A=c.n(z);let B=["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-powerpoint","application/vnd.openxmlformats-officedocument.presentationml.presentation","text/plain","text/csv","application/json","application/xml","text/xml","image/jpeg","image/png","image/gif","image/webp","image/svg+xml","application/zip","application/x-rar-compressed","application/x-7z-compressed","application/x-tar","application/gzip","audio/mpeg","audio/wav","audio/ogg","audio/mp4","video/mp4","video/x-msvideo","video/quicktime","video/x-ms-wmv","video/webm"],C=["exe","bat","cmd","com","pif","scr","vbs","js","jar","app","deb","pkg","dmg","msi","run","bin","sh","ps1","psm1","psd1","ps1xml","psc1","psc2","msh","msh1","msh2","mshxml","msh1xml","msh2xml","scf","lnk","inf","reg"];async function D(a){try{let b=await a.arrayBuffer(),c=new Uint8Array(b),d={"application/pdf":[[37,80,68,70]],"image/jpeg":[[255,216,255]],"image/png":[[137,80,78,71,13,10,26,10]],"image/gif":[[71,73,70,56]],"application/zip":[[80,75,3,4],[80,75,5,6],[80,75,7,8]]}[a.type];if(d&&!d.some(a=>a.every((a,b)=>c[b]===a)))return{valid:!1,error:"File content does not match the declared file type"};return{valid:!0}}catch(a){return{valid:!1,error:"Failed to validate file content"}}}let E=new Map;function F(a){return a.replace(/[<>]/g,"").replace(/['"]/g,"").replace(/[&]/g,"&amp;").trim()}async function G(a){return Array.from(new Uint8Array(await A().subtle.digest("SHA-256",a))).map(a=>a.toString(16).padStart(2,"0")).join("")}async function H(a){let b=new Uint8Array(a),c=new TextEncoder().encode("X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*");for(let a=0;a<=b.length-c.length;a++){let d=!0;for(let e=0;e<c.length;e++)if(b[a+e]!==c[e]){d=!1;break}if(d)return{clean:!1,threat:"EICAR test file detected"}}return{clean:!0}}async function I(a){var b;let c=[],d=(b=a.name).includes("\0")||b.includes("..")||b.includes("/")||b.includes("\\")?{valid:!1,error:"File name contains invalid characters"}:b.length>255?{valid:!1,error:"File name is too long (max 255 characters)"}:b.trim()?{valid:!0}:{valid:!1,error:"File name cannot be empty"};d.valid||c.push(d.error);let e=function(a){let b=a.name.split(".").pop()?.toLowerCase();return b&&C.includes(b)?{valid:!1,error:`File type .${b} is not allowed for security reasons`}:B.includes(a.type)?{valid:!0}:{valid:!1,error:`MIME type ${a.type} is not allowed`}}(a);e.valid||c.push(e.error);let f=a.size>0x3200000?{valid:!1,error:"File size exceeds maximum limit of 50MB"}:{valid:!0};f.valid||c.push(f.error);try{let b=await D(a);b.valid||c.push(b.error)}catch(a){c.push("Failed to validate file content")}try{let b=await a.arrayBuffer(),d=await H(b);d.clean||c.push(`Security threat detected: ${d.threat}`)}catch(a){c.push("Failed to scan file for viruses")}return{valid:0===c.length,errors:c}}async function J(a){try{let b=new u.NextResponse;Object.entries({"X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY","X-XSS-Protection":"1; mode=block","Referrer-Policy":"strict-origin-when-cross-origin","Content-Security-Policy":"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://api.supabase.co https://*.supabase.co https://accounts.google.com https://drive.google.com;","Strict-Transport-Security":"max-age=********; includeSubDomains","Permissions-Policy":"camera=(), microphone=(), geolocation=()"}).forEach(([a,c])=>{b.headers.set(a,c)});let c=(0,v.tY)(a,b),d=function(a){let b=a.headers.get("x-forwarded-for"),c=a.headers.get("x-real-ip"),d=a.headers.get("remote-addr");return b?b.split(",")[0].trim():c||d||"127.0.0.1"}(a),e=function(a,b=100,c=9e5){let d=Date.now(),e=E.get(a);if(!e||d>e.resetTime){let e={count:1,resetTime:d+c};return E.set(a,e),{allowed:!0,remaining:b-1,resetTime:e.resetTime}}return e.count>=b?{allowed:!1,remaining:0,resetTime:e.resetTime}:(e.count++,E.set(a,e),{allowed:!0,remaining:b-e.count,resetTime:e.resetTime})}(`upload:${d}`,10,9e5);if(!e.allowed)return u.NextResponse.json({success:!1,error:"Rate limit exceeded. Please try again later.",resetTime:e.resetTime},{status:429});let{data:{user:f},error:g}=await c.auth.getUser();if(g||!f)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await x.v.getUserById(f.id))return u.NextResponse.json({success:!1,error:"User profile not found"},{status:404});let h=await a.formData(),i=h.get("file"),j=F(h.get("description")||""),k=F(h.get("tags")||""),l="true"===h.get("isPublic");if(!i)return u.NextResponse.json({success:!1,error:"No file provided"},{status:400});let m=await I(i);if(!m.valid)return u.NextResponse.json({success:!1,error:"File validation failed",details:m.errors},{status:400});let n=(0,y.wh)(i.name),o=(0,y.lg)(i.name),p=i.type||(0,y.Og)(o),q=Buffer.from(await i.arrayBuffer());await G(q.buffer);let r=await w.l.uploadFile(q,n,p),s=await x.v.createFile({filename:n,original_filename:i.name,file_size:i.size,file_type:o,mime_type:p,google_drive_id:r.id,google_drive_url:r.webViewLink,uploaded_by:f.id,description:j||null,tags:k?k.split(",").map(a=>a.trim()).filter(Boolean):null,is_public:l,download_count:0,status:"completed"});return await x.v.createAuditLog({user_id:f.id,action:"upload",resource_type:"file",resource_id:s.id,details:{filename:i.name,file_size:i.size,file_type:o,is_public:l},ip_address:a.ip||null,user_agent:a.headers.get("user-agent")||null}),u.NextResponse.json({success:!0,data:s,message:"File uploaded successfully"})}catch(a){return console.error("File upload error:",a),u.NextResponse.json({success:!1,error:a instanceof Error?a.message:"Failed to upload file"},{status:500})}}async function K(a){try{let b,c=new u.NextResponse,d=(0,v.tY)(a,c),{data:{user:e},error:f}=await d.auth.getUser();if(f||!e)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let g=await x.v.getUserById(e.id);if(!g)return u.NextResponse.json({success:!1,error:"User profile not found"},{status:404});let{searchParams:h}=new URL(a.url),i=parseInt(h.get("page")||"1"),j=parseInt(h.get("limit")||"10"),k=h.get("search")||void 0,l=h.get("sortBy")||void 0,m=h.get("sortOrder")||"desc",n=h.get("fileType")||void 0,o=h.get("isPublic")?"true"===h.get("isPublic"):void 0,p=h.get("tags")?.split(",").filter(Boolean)||void 0;return b="admin"===g.role?await x.v.getAllFiles({page:i,limit:j,search:k,sortBy:l,sortOrder:m,filter:{fileType:n,isPublic:o,tags:p}}):await x.v.getFilesByUser(e.id,{page:i,limit:j,search:k,sortBy:l,sortOrder:m,filter:{fileType:n,isPublic:o,tags:p}}),u.NextResponse.json({success:!0,data:b.data,pagination:b.pagination})}catch(a){return console.error("Get files error:",a),u.NextResponse.json({success:!1,error:a instanceof Error?a.message:"Failed to get files"},{status:500})}}setInterval(()=>{let a=Date.now();for(let[b,c]of E.entries())a>c.resetTime&&E.delete(b)},3e5);let L=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/files/upload/route",pathname:"/api/files/upload",filename:"route",bundlePath:"app/api/files/upload/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Source Code\\new\\document-repository\\src\\app\\api\\files\\upload\\route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:M,workUnitAsyncStorage:N,serverHooks:O}=L;function P(){return(0,g.patchFetch)({workAsyncStorage:M,workUnitAsyncStorage:N})}async function Q(a,b,c){var d;let e="/api/files/upload/route";"/index"===e&&(e="/");let g=await L.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let F=null;!E||L.isDev||x||(F="/index"===(F=C)?"/":F);let G=!0===L.isDev||!E,H=E&&!G,I=a.method||"GET",J=(0,i.getTracer)(),K=J.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>L.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>L.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=J.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${I} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${I} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!E)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await L.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},l=await L.handleResponse({req:a,nextConfig:w,cacheKey:F,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!E)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&E||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};K?await g(K):await J.withPropagatedContext(a.headers,()=>J.trace(m.BaseServerSpan.handleRequest,{spanName:`${I} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":I,"http.target":a.url}},g))}catch(b){if(K||await L.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},37067:a=>{"use strict";a.exports=require("node:http")},37830:a=>{"use strict";a.exports=require("node:stream/web")},44708:a=>{"use strict";a.exports=require("node:https")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56621:(a,b,c)=>{"use strict";c.d(b,{HK:()=>j,tY:()=>i});var d=c(15481),e=c(92386);let f=process.env.NEXT_PUBLIC_SUPABASE_URL,g=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,h=process.env.SUPABASE_SERVICE_ROLE_KEY,i=(a,b)=>(0,e.createServerClient)(f,g,{cookies:{getAll:()=>a.cookies.getAll(),setAll(c){c.forEach(({name:b,value:c})=>a.cookies.set(b,c)),c.forEach(({name:a,value:c,options:d})=>b.cookies.set(a,c,d))}}}),j=()=>(0,d.UU)(f,h,{auth:{autoRefreshToken:!1,persistSession:!1}})},57075:a=>{"use strict";a.exports=require("node:stream")},57975:a=>{"use strict";a.exports=require("node:util")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:a=>{"use strict";a.exports=require("node:fs")},73136:a=>{"use strict";a.exports=require("node:url")},73496:a=>{"use strict";a.exports=require("http2")},73566:a=>{"use strict";a.exports=require("worker_threads")},74075:a=>{"use strict";a.exports=require("zlib")},76760:a=>{"use strict";a.exports=require("node:path")},76947:()=>{},77030:a=>{"use strict";a.exports=require("node:net")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,386,90],()=>b(b.s=35529));module.exports=c})();