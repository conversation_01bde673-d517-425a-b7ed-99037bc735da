'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { CloudArrowUpIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { formatFileSize, validateFileType, validateFileSize } from '@/lib/utils';

interface FileUploadProps {
  onUpload: (files: File[], metadata: FileMetadata[]) => void;
  maxFiles?: number;
  maxFileSize?: number;
  allowedFileTypes?: string[];
  disabled?: boolean;
}

interface FileMetadata {
  description: string;
  tags: string[];
  isPublic: boolean;
}

interface FileWithMetadata {
  file: File;
  metadata: FileMetadata;
}

export default function FileUpload({
  onUpload,
  maxFiles = 5,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  allowedFileTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar'],
  disabled = false,
}: FileUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<FileWithMetadata[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setErrors([]);
    const newErrors: string[] = [];

    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          newErrors.push(`${file.name}: File is too large (max ${formatFileSize(maxFileSize)})`);
        } else if (error.code === 'file-invalid-type') {
          newErrors.push(`${file.name}: File type not allowed`);
        } else {
          newErrors.push(`${file.name}: ${error.message}`);
        }
      });
    });

    // Validate accepted files
    const validFiles: File[] = [];
    acceptedFiles.forEach(file => {
      if (!validateFileType(file, allowedFileTypes)) {
        newErrors.push(`${file.name}: File type not allowed. Allowed types: ${allowedFileTypes.join(', ')}`);
        return;
      }

      if (!validateFileSize(file, maxFileSize)) {
        newErrors.push(`${file.name}: File is too large (max ${formatFileSize(maxFileSize)})`);
        return;
      }

      validFiles.push(file);
    });

    // Check total file count
    if (selectedFiles.length + validFiles.length > maxFiles) {
      newErrors.push(`Cannot upload more than ${maxFiles} files at once`);
      setErrors(newErrors);
      return;
    }

    // Add valid files with default metadata
    const newFilesWithMetadata: FileWithMetadata[] = validFiles.map(file => ({
      file,
      metadata: {
        description: '',
        tags: [],
        isPublic: false,
      },
    }));

    setSelectedFiles(prev => [...prev, ...newFilesWithMetadata]);
    setErrors(newErrors);
  }, [selectedFiles, maxFiles, maxFileSize, allowedFileTypes]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    disabled,
    maxFiles,
    maxSize: maxFileSize,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'application/zip': ['.zip'],
      'application/x-rar-compressed': ['.rar'],
    },
  });

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const updateFileMetadata = (index: number, metadata: Partial<FileMetadata>) => {
    setSelectedFiles(prev => prev.map((item, i) => 
      i === index 
        ? { ...item, metadata: { ...item.metadata, ...metadata } }
        : item
    ));
  };

  const handleUpload = () => {
    if (selectedFiles.length === 0) return;

    const files = selectedFiles.map(item => item.file);
    const metadata = selectedFiles.map(item => item.metadata);
    
    onUpload(files, metadata);
    setSelectedFiles([]);
  };

  return (
    <div className="w-full">
      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive 
            ? 'border-indigo-500 bg-indigo-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2 text-sm text-gray-600">
          {isDragActive
            ? 'Drop the files here...'
            : 'Drag and drop files here, or click to select files'
          }
        </p>
        <p className="text-xs text-gray-500 mt-1">
          Max {maxFiles} files, up to {formatFileSize(maxFileSize)} each
        </p>
        <p className="text-xs text-gray-500">
          Supported: {allowedFileTypes.join(', ')}
        </p>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <h4 className="text-sm font-medium text-red-800">Upload Errors:</h4>
          <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-4">
            Selected Files ({selectedFiles.length})
          </h4>
          <div className="space-y-4">
            {selectedFiles.map((item, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-900">
                        {item.file.name}
                      </span>
                      <span className="ml-2 text-xs text-gray-500">
                        ({formatFileSize(item.file.size)})
                      </span>
                    </div>
                    
                    <div className="mt-3 space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700">
                          Description (optional)
                        </label>
                        <input
                          type="text"
                          className="mt-1 block w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="Enter file description"
                          value={item.metadata.description}
                          onChange={(e) => updateFileMetadata(index, { description: e.target.value })}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs font-medium text-gray-700">
                          Tags (comma-separated)
                        </label>
                        <input
                          type="text"
                          className="mt-1 block w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="e.g., document, important, project"
                          value={item.metadata.tags.join(', ')}
                          onChange={(e) => updateFileMetadata(index, { 
                            tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                          })}
                        />
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`public-${index}`}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          checked={item.metadata.isPublic}
                          onChange={(e) => updateFileMetadata(index, { isPublic: e.target.checked })}
                        />
                        <label htmlFor={`public-${index}`} className="ml-2 block text-xs text-gray-700">
                          Make this file publicly accessible
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="ml-4 text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-end">
            <button
              type="button"
              onClick={handleUpload}
              disabled={disabled || selectedFiles.length === 0}
              className="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Upload {selectedFiles.length} File{selectedFiles.length !== 1 ? 's' : ''}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
