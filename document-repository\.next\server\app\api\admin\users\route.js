(()=>{var a={};a.id=950,a.ids=[950],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(a,b,c)=>{"use strict";c.d(b,{v:()=>f});var d=c(56621);class e{async createUser(a){let{data:b,error:c}=await this.supabase.from("users").insert(a).select().single();if(c)throw c;return b}async getUserById(a){let{data:b,error:c}=await this.supabase.from("users").select("*").eq("id",a).single();if(c&&"PGRST116"!==c.code)throw c;return b}async getUserByEmail(a){let{data:b,error:c}=await this.supabase.from("users").select("*").eq("email",a).single();if(c&&"PGRST116"!==c.code)throw c;return b}async updateUser(a,b){let{data:c,error:d}=await this.supabase.from("users").update(b).eq("id",a).select().single();if(d)throw d;return c}async getAllUsers(a){let b=this.supabase.from("users").select("*",{count:"exact"});a.search&&(b=b.or(`email.ilike.%${a.search}%,full_name.ilike.%${a.search}%`)),b=a.sortBy?b.order(a.sortBy,{ascending:"asc"===a.sortOrder}):b.order("created_at",{ascending:!1});let c=(a.page-1)*a.limit,d=c+a.limit-1;b=b.range(c,d);let{data:e,error:f,count:g}=await b;if(f)throw f;let h=Math.ceil((g||0)/a.limit);return{data:e||[],pagination:{page:a.page,limit:a.limit,total:g||0,totalPages:h,hasNext:a.page<h,hasPrev:a.page>1}}}async createFile(a){let{data:b,error:c}=await this.supabase.from("files").insert(a).select().single();if(c)throw c;return b}async getFileById(a){let{data:b,error:c}=await this.supabase.from("files").select("*").eq("id",a).single();if(c&&"PGRST116"!==c.code)throw c;return b}async updateFile(a,b){let{data:c,error:d}=await this.supabase.from("files").update(b).eq("id",a).select().single();if(d)throw d;return c}async deleteFile(a){let{error:b}=await this.supabase.from("files").delete().eq("id",a);if(b)throw b}async getFilesByUser(a,b){let c=this.supabase.from("files").select("*",{count:"exact"}).eq("uploaded_by",a);b.search&&(c=c.or(`filename.ilike.%${b.search}%,original_filename.ilike.%${b.search}%,description.ilike.%${b.search}%`)),b.filter?.fileType&&(c=c.eq("file_type",b.filter.fileType)),b.filter?.isPublic!==void 0&&(c=c.eq("is_public",b.filter.isPublic)),b.filter?.tags&&b.filter.tags.length>0&&(c=c.overlaps("tags",b.filter.tags)),c=b.sortBy?c.order(b.sortBy,{ascending:"asc"===b.sortOrder}):c.order("uploaded_at",{ascending:!1});let d=(b.page-1)*b.limit,e=d+b.limit-1;c=c.range(d,e);let{data:f,error:g,count:h}=await c;if(g)throw g;let i=Math.ceil((h||0)/b.limit);return{data:f||[],pagination:{page:b.page,limit:b.limit,total:h||0,totalPages:i,hasNext:b.page<i,hasPrev:b.page>1}}}async getAllFiles(a){let b=this.supabase.from("files").select(`
        *,
        users!files_uploaded_by_fkey(email, full_name)
      `,{count:"exact"});a.search&&(b=b.or(`filename.ilike.%${a.search}%,original_filename.ilike.%${a.search}%,description.ilike.%${a.search}%`)),a.filter?.fileType&&(b=b.eq("file_type",a.filter.fileType)),a.filter?.uploadedBy&&(b=b.eq("uploaded_by",a.filter.uploadedBy)),a.filter?.isPublic!==void 0&&(b=b.eq("is_public",a.filter.isPublic)),a.filter?.tags&&a.filter.tags.length>0&&(b=b.overlaps("tags",a.filter.tags)),b=a.sortBy?b.order(a.sortBy,{ascending:"asc"===a.sortOrder}):b.order("uploaded_at",{ascending:!1});let c=(a.page-1)*a.limit,d=c+a.limit-1;b=b.range(c,d);let{data:e,error:f,count:g}=await b;if(f)throw f;let h=Math.ceil((g||0)/a.limit);return{data:e||[],pagination:{page:a.page,limit:a.limit,total:g||0,totalPages:h,hasNext:a.page<h,hasPrev:a.page>1}}}async incrementDownloadCount(a){let{error:b}=await this.supabase.rpc("increment_download_count",{file_id:a});if(b)throw b}async createFilePermission(a){let{data:b,error:c}=await this.supabase.from("file_permissions").insert(a).select().single();if(c)throw c;return b}async getFilePermissions(a){let{data:b,error:c}=await this.supabase.from("file_permissions").select(`
        *,
        users!file_permissions_user_id_fkey(email, full_name)
      `).eq("file_id",a);if(c)throw c;return b||[]}async deleteFilePermission(a){let{error:b}=await this.supabase.from("file_permissions").delete().eq("id",a);if(b)throw b}async createAuditLog(a){let{data:b,error:c}=await this.supabase.from("audit_logs").insert(a).select().single();if(c)throw c;return b}async getAuditLogs(a){let b=this.supabase.from("audit_logs").select(`
        *,
        users!audit_logs_user_id_fkey(email, full_name)
      `,{count:"exact"});a.filter?.userId&&(b=b.eq("user_id",a.filter.userId)),a.filter?.action&&(b=b.eq("action",a.filter.action)),a.filter?.resourceType&&(b=b.eq("resource_type",a.filter.resourceType)),b=a.sortBy?b.order(a.sortBy,{ascending:"asc"===a.sortOrder}):b.order("created_at",{ascending:!1});let c=(a.page-1)*a.limit,d=c+a.limit-1;b=b.range(c,d);let{data:e,error:f,count:g}=await b;if(f)throw f;let h=Math.ceil((g||0)/a.limit);return{data:e||[],pagination:{page:a.page,limit:a.limit,total:g||0,totalPages:h,hasNext:a.page<h,hasPrev:a.page>1}}}async getDashboardStats(){let[a,b,c]=await Promise.all([this.supabase.from("files").select("*",{count:"exact",head:!0}),this.supabase.from("users").select("*",{count:"exact",head:!0}),this.supabase.from("files").select("*").gte("uploaded_at",new Date(Date.now()-6048e5).toISOString()).order("uploaded_at",{ascending:!1}).limit(10)]);return{totalFiles:a.count||0,totalUsers:b.count||0,recentUploads:c.data||[]}}constructor(){this.supabase=(0,d.HK)()}}let f=new e},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";function d(a){let b=Date.now(),c=Math.random().toString(36).substring(2,15),d=a.split(".").pop(),e=a.replace(/\.[^/.]+$/,"");return`${e}_${b}_${c}.${d}`}function e(a){return a.split(".").pop()?.toLowerCase()||""}function f(a){return({jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",webp:"image/webp",svg:"image/svg+xml",pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",txt:"text/plain",csv:"text/csv",html:"text/html",css:"text/css",js:"text/javascript",json:"application/json",xml:"application/xml",zip:"application/zip",rar:"application/x-rar-compressed","7z":"application/x-7z-compressed",tar:"application/x-tar",gz:"application/gzip",mp3:"audio/mpeg",wav:"audio/wav",ogg:"audio/ogg",m4a:"audio/mp4",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",wmv:"video/x-ms-wmv",flv:"video/x-flv",webm:"video/webm"})[a]||"application/octet-stream"}function g(a){let b={};for(let[c,d]of a.entries())"page"===c||"limit"===c?b[c]=parseInt(d,10):"tags"===c?b[c]=d.split(",").filter(Boolean):"isPublic"===c?b[c]="true"===d:b[c]=d;return b}c.d(b,{Og:()=>f,_n:()=>g,lg:()=>e,wh:()=>d})},11997:a=>{"use strict";a.exports=require("punycode")},15346:()=>{},27910:a=>{"use strict";a.exports=require("stream")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:a=>{"use strict";a.exports=require("tls")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56621:(a,b,c)=>{"use strict";c.d(b,{HK:()=>j,tY:()=>i});var d=c(15481),e=c(92386);let f=process.env.NEXT_PUBLIC_SUPABASE_URL,g=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,h=process.env.SUPABASE_SERVICE_ROLE_KEY,i=(a,b)=>(0,e.createServerClient)(f,g,{cookies:{getAll:()=>a.cookies.getAll(),setAll(c){c.forEach(({name:b,value:c})=>a.cookies.set(b,c)),c.forEach(({name:a,value:c,options:d})=>b.cookies.set(a,c,d))}}}),j=()=>(0,d.UU)(f,h,{auth:{autoRefreshToken:!1,persistSession:!1}})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72163:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>E,patchFetch:()=>D,routeModule:()=>z,serverHooks:()=>C,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>B});var d={};c.r(d),c.d(d,{GET:()=>y});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(56621),w=c(6710),x=c(10974);async function y(a){try{let b=new u.NextResponse,c=(0,v.tY)(a,b),{data:{user:d},error:e}=await c.auth.getUser();if(e||!d)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let f=await w.v.getUserById(d.id);if(!f||"admin"!==f.role)return u.NextResponse.json({success:!1,error:"Access denied"},{status:403});let{searchParams:g}=new URL(a.url),h=(0,x._n)(g),i={page:h.page||1,limit:h.limit||20,search:h.search,sortBy:h.sortBy,sortOrder:h.sortOrder||"desc"},j=await w.v.getAllUsers(i);return u.NextResponse.json({success:!0,data:j.data,pagination:j.pagination})}catch(a){return console.error("Get users error:",a),u.NextResponse.json({success:!1,error:a instanceof Error?a.message:"Failed to get users"},{status:500})}}let z=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Source Code\\new\\document-repository\\src\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:A,workUnitAsyncStorage:B,serverHooks:C}=z;function D(){return(0,g.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:B})}async function E(a,b,c){var d;let e="/api/admin/users/route";"/index"===e&&(e="/");let g=await z.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||z.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===z.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>z.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>z.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await z.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await z.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await z.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},74075:a=>{"use strict";a.exports=require("zlib")},76947:()=>{},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,386],()=>b(b.s=72163));module.exports=c})();