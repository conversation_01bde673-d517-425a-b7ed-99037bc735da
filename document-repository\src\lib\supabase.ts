import { createClient } from '@supabase/supabase-js';
import { createBrowserClient, createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Client-side Supabase client
export const createClientComponentClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey);
};

// Server-side Supabase client for Server Components
export const createServerComponentClient = async () => {
  const { cookies } = await import('next/headers');
  const cookieStore = await cookies();

  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          );
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  });
};

// Server-side Supabase client for Route Handlers
export const createRouteHandlerClient = (request: NextRequest, response: NextResponse) => {
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll();
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));
        cookiesToSet.forEach(({ name, value, options }) =>
          response.cookies.set(name, value, options)
        );
      },
    },
  });
};

// Service role client for admin operations
export const createServiceRoleClient = () => {
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

// Database types
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          role: 'admin' | 'user';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          role?: 'admin' | 'user';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          role?: 'admin' | 'user';
          updated_at?: string;
        };
      };
      files: {
        Row: {
          id: string;
          filename: string;
          original_filename: string;
          file_size: number;
          file_type: string;
          mime_type: string;
          google_drive_id: string;
          google_drive_url: string;
          uploaded_by: string;
          uploaded_at: string;
          updated_at: string;
          description: string | null;
          tags: string[] | null;
          is_public: boolean;
          download_count: number;
          status: 'uploading' | 'completed' | 'failed' | 'deleted';
        };
        Insert: {
          id?: string;
          filename: string;
          original_filename: string;
          file_size: number;
          file_type: string;
          mime_type: string;
          google_drive_id: string;
          google_drive_url: string;
          uploaded_by: string;
          uploaded_at?: string;
          updated_at?: string;
          description?: string | null;
          tags?: string[] | null;
          is_public?: boolean;
          download_count?: number;
          status?: 'uploading' | 'completed' | 'failed' | 'deleted';
        };
        Update: {
          filename?: string;
          original_filename?: string;
          file_size?: number;
          file_type?: string;
          mime_type?: string;
          google_drive_id?: string;
          google_drive_url?: string;
          updated_at?: string;
          description?: string | null;
          tags?: string[] | null;
          is_public?: boolean;
          download_count?: number;
          status?: 'uploading' | 'completed' | 'failed' | 'deleted';
        };
      };
      file_permissions: {
        Row: {
          id: string;
          file_id: string;
          user_id: string;
          permission_type: 'read' | 'write' | 'admin';
          granted_by: string;
          granted_at: string;
        };
        Insert: {
          id?: string;
          file_id: string;
          user_id: string;
          permission_type: 'read' | 'write' | 'admin';
          granted_by: string;
          granted_at?: string;
        };
        Update: {
          permission_type?: 'read' | 'write' | 'admin';
        };
      };
      audit_logs: {
        Row: {
          id: string;
          user_id: string;
          action: 'upload' | 'download' | 'delete' | 'share' | 'update';
          resource_type: 'file' | 'user' | 'permission';
          resource_id: string;
          details: any | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          action: 'upload' | 'download' | 'delete' | 'share' | 'update';
          resource_type: 'file' | 'user' | 'permission';
          resource_id: string;
          details?: any | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
        Update: {
          details?: any | null;
        };
      };
    };
  };
};
