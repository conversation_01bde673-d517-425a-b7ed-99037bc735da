const { google } = require('googleapis');

const oauth2Client = new google.auth.OAuth2(
  '881878955396-uf6u5s4qhuaffap1ba19qpa26iedve0l.apps.googleusercontent.com',
  '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',
  'http://localhost:3000/api/auth/callback'
);

const scopes = ['https://www.googleapis.com/auth/drive.file'];

const url = oauth2Client.generateAuthUrl({
  access_type: 'offline',
  scope: scopes,
  prompt: 'consent'
});

console.log('Visit this URL to authorize the application:');
console.log(url);
console.log('\nAfter authorization, you will be redirected to a URL with a code parameter.');
console.log('Copy that code and run: node get-token.js YOUR_CODE');

if (process.argv[2]) {
  oauth2Client.getToken(process.argv[2], (err, token) => {
    if (err) return console.error('Error retrieving access token', err);
    console.log('Your refresh token:', token.refresh_token);
  });
}