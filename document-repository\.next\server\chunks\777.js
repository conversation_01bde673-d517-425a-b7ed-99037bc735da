exports.id=777,exports.ids=[777],exports.modules={2915:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},15346:()=>{},16391:(a,b,c)=>{"use strict";c.d(b,{i7:()=>g});var d=c(52754);let e=process.env.NEXT_PUBLIC_SUPABASE_URL,f=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;process.env.SUPABASE_SERVICE_ROLE_KEY;let g=()=>(0,d.createBrowserClient)(e,f)},16987:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},35543:(a,b,c)=>{Promise.resolve().then(c.bind(c,88928))},61135:()=>{},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},72335:(a,b,c)=>{Promise.resolve().then(c.bind(c,86246))},76947:()=>{},86246:(a,b,c)=>{"use strict";c.d(b,{default:()=>q});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(16189),i=c(26740),j=c(45622),k=c(28884),l=c(55970),m=c(89322),n=c(29446),o=c(79192),p=c(16391);function q(){let[a,b]=(0,e.useState)(null),[c,f]=(0,e.useState)(!0),[q,r]=(0,e.useState)(!1),s=(0,h.useRouter)(),t=(0,h.usePathname)(),u=(0,p.i7)(),v=async()=>{try{await fetch("/api/auth/signout",{method:"POST"}),await u.auth.signOut(),s.push("/auth/login")}catch(a){console.error("Error signing out:",a)}},w=[{name:"Dashboard",href:"/dashboard",icon:i.A},{name:"Upload",href:"/upload",icon:j.A},...a?.role==="admin"?[{name:"Admin Panel",href:"/admin",icon:k.A}]:[]];return c?(0,d.jsx)("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"flex justify-between h-16",children:(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)("div",{className:"animate-pulse bg-gray-300 h-8 w-32 rounded"})})})})}):t?.startsWith("/auth/")?null:(0,d.jsxs)("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:[(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between h-16",children:[(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,d.jsx)(g(),{href:"/dashboard",className:"text-xl font-bold text-indigo-600",children:"DocRepository"})}),(0,d.jsx)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:w.map(a=>{let b=t===a.href;return(0,d.jsxs)(g(),{href:a.href,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${b?"border-indigo-500 text-gray-900":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}`,children:[(0,d.jsx)(a.icon,{className:"w-4 h-4 mr-2"}),a.name]},a.name)})})]}),(0,d.jsx)("div",{className:"hidden sm:ml-6 sm:flex sm:items-center",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(l.A,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:a?.full_name||a?.email||"User"}),a?.role==="admin"&&(0,d.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:"Admin"})]}),(0,d.jsxs)("button",{onClick:v,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[(0,d.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Sign out"]})]})}),(0,d.jsx)("div",{className:"sm:hidden flex items-center",children:(0,d.jsx)("button",{onClick:()=>r(!q),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500",children:q?(0,d.jsx)(n.A,{className:"block h-6 w-6"}):(0,d.jsx)(o.A,{className:"block h-6 w-6"})})})]})}),q&&(0,d.jsxs)("div",{className:"sm:hidden",children:[(0,d.jsx)("div",{className:"pt-2 pb-3 space-y-1",children:w.map(a=>{let b=t===a.href;return(0,d.jsx)(g(),{href:a.href,className:`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${b?"bg-indigo-50 border-indigo-500 text-indigo-700":"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700"}`,onClick:()=>r(!1),children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(a.icon,{className:"w-5 h-5 mr-3"}),a.name]})},a.name)})}),(0,d.jsxs)("div",{className:"pt-4 pb-3 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center px-4",children:[(0,d.jsx)(l.A,{className:"w-8 h-8 text-gray-400"}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("div",{className:"text-base font-medium text-gray-800",children:a?.full_name||"User"}),(0,d.jsx)("div",{className:"text-sm font-medium text-gray-500",children:a?.email}),a?.role==="admin"&&(0,d.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:"Admin"})]})]}),(0,d.jsx)("div",{className:"mt-3 space-y-1",children:(0,d.jsx)("button",{onClick:v,className:"block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(m.A,{className:"w-5 h-5 mr-3"}),"Sign out"]})})})]})]})]})}},88928:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Source Code\\\\new\\\\document-repository\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Source Code\\new\\document-repository\\src\\components\\Navigation.tsx","default")},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(37413),e=c(22376),f=c.n(e),g=c(68726),h=c.n(g);c(61135);var i=c(88928);let j={title:"Document Repository",description:"A secure document repository with Google Drive integration"};function k({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsxs)("body",{className:`${f().variable} ${h().variable} antialiased`,children:[(0,d.jsx)(i.default,{}),a]})})}}};