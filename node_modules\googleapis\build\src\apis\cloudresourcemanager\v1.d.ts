import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace cloudresourcemanager_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Resource Manager API
     *
     * Creates, reads, and updates metadata for Google Cloud Platform resource containers.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const cloudresourcemanager = google.cloudresourcemanager('v1');
     * ```
     */
    export class Cloudresourcemanager {
        context: APIRequestContext;
        folders: Resource$Folders;
        liens: Resource$Liens;
        operations: Resource$Operations;
        organizations: Resource$Organizations;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Identifying information for a single ancestor of a project.
     */
    export interface Schema$Ancestor {
        /**
         * Resource id of the ancestor.
         */
        resourceId?: Schema$ResourceId;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * A `Constraint` that is either enforced or not. For example a constraint `constraints/compute.disableSerialPortAccess`. If it is enforced on a VM instance, serial port connections will not be opened to that instance.
     */
    export interface Schema$BooleanConstraint {
    }
    /**
     * Used in `policy_type` to specify how `boolean_policy` will behave at this resource.
     */
    export interface Schema$BooleanPolicy {
        /**
         * If `true`, then the `Policy` is enforced. If `false`, then any configuration is acceptable. Suppose you have a `Constraint` `constraints/compute.disableSerialPortAccess` with `constraint_default` set to `ALLOW`. A `Policy` for that `Constraint` exhibits the following behavior: - If the `Policy` at this resource has enforced set to `false`, serial port connection attempts will be allowed. - If the `Policy` at this resource has enforced set to `true`, serial port connection attempts will be refused. - If the `Policy` at this resource is `RestoreDefault`, serial port connection attempts will be allowed. - If no `Policy` is set at this resource or anywhere higher in the resource hierarchy, serial port connection attempts will be allowed. - If no `Policy` is set at this resource, but one exists higher in the resource hierarchy, the behavior is as if the`Policy` were set at this resource. The following examples demonstrate the different possible layerings: Example 1 (nearest `Constraint` wins): `organizations/foo` has a `Policy` with: {enforced: false\} `projects/bar` has no `Policy` set. The constraint at `projects/bar` and `organizations/foo` will not be enforced. Example 2 (enforcement gets replaced): `organizations/foo` has a `Policy` with: {enforced: false\} `projects/bar` has a `Policy` with: {enforced: true\} The constraint at `organizations/foo` is not enforced. The constraint at `projects/bar` is enforced. Example 3 (RestoreDefault): `organizations/foo` has a `Policy` with: {enforced: true\} `projects/bar` has a `Policy` with: {RestoreDefault: {\}\} The constraint at `organizations/foo` is enforced. The constraint at `projects/bar` is not enforced, because `constraint_default` for the `Constraint` is `ALLOW`.
         */
        enforced?: boolean | null;
    }
    /**
     * The request sent to the ClearOrgPolicy method.
     */
    export interface Schema$ClearOrgPolicyRequest {
        /**
         * Name of the `Constraint` of the `Policy` to clear.
         */
        constraint?: string | null;
        /**
         * The current version, for concurrency control. Not sending an `etag` will cause the `Policy` to be cleared blindly.
         */
        etag?: string | null;
    }
    /**
     * Metadata describing a long running folder operation
     */
    export interface Schema$CloudresourcemanagerGoogleCloudResourcemanagerV2alpha1FolderOperation {
        /**
         * The resource name of the folder or organization we are either creating the folder under or moving the folder to.
         */
        destinationParent?: string | null;
        /**
         * The display name of the folder.
         */
        displayName?: string | null;
        /**
         * The type of this operation.
         */
        operationType?: string | null;
        /**
         * The resource name of the folder's parent. Only applicable when the operation_type is MOVE.
         */
        sourceParent?: string | null;
    }
    /**
     * Metadata describing a long running folder operation
     */
    export interface Schema$CloudresourcemanagerGoogleCloudResourcemanagerV2beta1FolderOperation {
        /**
         * The resource name of the folder or organization we are either creating the folder under or moving the folder to.
         */
        destinationParent?: string | null;
        /**
         * The display name of the folder.
         */
        displayName?: string | null;
        /**
         * The type of this operation.
         */
        operationType?: string | null;
        /**
         * The resource name of the folder's parent. Only applicable when the operation_type is MOVE.
         */
        sourceParent?: string | null;
    }
    /**
     * A `Constraint` describes a way in which a resource's configuration can be restricted. For example, it controls which cloud services can be activated across an organization, or whether a Compute Engine instance can have serial port connections established. `Constraints` can be configured by the organization's policy administrator to fit the needs of the organzation by setting Policies for `Constraints` at different locations in the organization's resource hierarchy. Policies are inherited down the resource hierarchy from higher levels, but can also be overridden. For details about the inheritance rules please read about [Policies](/resource-manager/reference/rest/v1/Policy). `Constraints` have a default behavior determined by the `constraint_default` field, which is the enforcement behavior that is used in the absence of a `Policy` being defined or inherited for the resource in question.
     */
    export interface Schema$Constraint {
        /**
         * Defines this constraint as being a BooleanConstraint.
         */
        booleanConstraint?: Schema$BooleanConstraint;
        /**
         * The evaluation behavior of this constraint in the absence of 'Policy'.
         */
        constraintDefault?: string | null;
        /**
         * Detailed description of what this `Constraint` controls as well as how and where it is enforced. Mutable.
         */
        description?: string | null;
        /**
         * The human readable name. Mutable.
         */
        displayName?: string | null;
        /**
         * Defines this constraint as being a ListConstraint.
         */
        listConstraint?: Schema$ListConstraint;
        /**
         * Immutable value, required to globally be unique. For example, `constraints/serviceuser.services`
         */
        name?: string | null;
        /**
         * Version of the `Constraint`. Default version is 0;
         */
        version?: number | null;
    }
    /**
     * Metadata pertaining to the Folder creation process.
     */
    export interface Schema$CreateFolderMetadata {
        /**
         * The display name of the folder.
         */
        displayName?: string | null;
        /**
         * The resource name of the folder or organization we are creating the folder under.
         */
        parent?: string | null;
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by CreateProject. It provides insight for when significant phases of Project creation have completed.
     */
    export interface Schema$CreateProjectMetadata {
        /**
         * Creation time of the project creation workflow.
         */
        createTime?: string | null;
        /**
         * True if the project can be retrieved using `GetProject`. No other operations on the project are guaranteed to work until the project creation is complete.
         */
        gettable?: boolean | null;
        /**
         * True if the project creation process is complete.
         */
        ready?: boolean | null;
    }
    /**
     * Runtime operation information for creating a TagValue.
     */
    export interface Schema$CreateTagBindingMetadata {
    }
    /**
     * Runtime operation information for creating a TagKey.
     */
    export interface Schema$CreateTagKeyMetadata {
    }
    /**
     * Runtime operation information for creating a TagValue.
     */
    export interface Schema$CreateTagValueMetadata {
    }
    /**
     * A status object which is used as the `metadata` field for the `Operation` returned by `DeleteFolder`.
     */
    export interface Schema$DeleteFolderMetadata {
    }
    /**
     * A status object which is used as the `metadata` field for the operation returned by DeleteOrganization.
     */
    export interface Schema$DeleteOrganizationMetadata {
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by `DeleteProject`.
     */
    export interface Schema$DeleteProjectMetadata {
    }
    /**
     * Runtime operation information for deleting a TagBinding.
     */
    export interface Schema$DeleteTagBindingMetadata {
    }
    /**
     * Runtime operation information for deleting a TagKey.
     */
    export interface Schema$DeleteTagKeyMetadata {
    }
    /**
     * Runtime operation information for deleting a TagValue.
     */
    export interface Schema$DeleteTagValueMetadata {
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * Metadata describing a long running folder operation
     */
    export interface Schema$FolderOperation {
        /**
         * The resource name of the folder or organization we are either creating the folder under or moving the folder to.
         */
        destinationParent?: string | null;
        /**
         * The display name of the folder.
         */
        displayName?: string | null;
        /**
         * The type of this operation.
         */
        operationType?: string | null;
        /**
         * The resource name of the folder's parent. Only applicable when the operation_type is MOVE.
         */
        sourceParent?: string | null;
    }
    /**
     * A classification of the Folder Operation error.
     */
    export interface Schema$FolderOperationError {
        /**
         * The type of operation error experienced.
         */
        errorMessageId?: string | null;
    }
    /**
     * The request sent to the GetAncestry method.
     */
    export interface Schema$GetAncestryRequest {
    }
    /**
     * Response from the projects.getAncestry method.
     */
    export interface Schema$GetAncestryResponse {
        /**
         * Ancestors are ordered from bottom to top of the resource hierarchy. The first ancestor is the project itself, followed by the project's parent, etc..
         */
        ancestor?: Schema$Ancestor[];
    }
    /**
     * The request sent to the GetEffectiveOrgPolicy method.
     */
    export interface Schema$GetEffectiveOrgPolicyRequest {
        /**
         * The name of the `Constraint` to compute the effective `Policy`.
         */
        constraint?: string | null;
    }
    /**
     * Request message for `GetIamPolicy` method.
     */
    export interface Schema$GetIamPolicyRequest {
        /**
         * OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`.
         */
        options?: Schema$GetPolicyOptions;
    }
    /**
     * The request sent to the GetOrgPolicy method.
     */
    export interface Schema$GetOrgPolicyRequest {
        /**
         * Name of the `Constraint` to get the `Policy`.
         */
        constraint?: string | null;
    }
    /**
     * Encapsulates settings provided to GetIamPolicy.
     */
    export interface Schema$GetPolicyOptions {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        requestedPolicyVersion?: number | null;
    }
    /**
     * A Lien represents an encumbrance on the actions that can be performed on a resource.
     */
    export interface Schema$Lien {
        /**
         * The creation time of this Lien.
         */
        createTime?: string | null;
        /**
         * A system-generated unique identifier for this Lien. Example: `liens/1234abcd`
         */
        name?: string | null;
        /**
         * A stable, user-visible/meaningful string identifying the origin of the Lien, intended to be inspected programmatically. Maximum length of 200 characters. Example: 'compute.googleapis.com'
         */
        origin?: string | null;
        /**
         * A reference to the resource this Lien is attached to. The server will validate the parent against those for which Liens are supported. Example: `projects/1234`
         */
        parent?: string | null;
        /**
         * Concise user-visible strings indicating why an action cannot be performed on a resource. Maximum length of 200 characters. Example: 'Holds production API key'
         */
        reason?: string | null;
        /**
         * The types of operations which should be blocked as a result of this Lien. Each value should correspond to an IAM permission. The server will validate the permissions against those for which Liens are supported. An empty list is meaningless and will be rejected. Example: ['resourcemanager.projects.delete']
         */
        restrictions?: string[] | null;
    }
    /**
     * The request sent to the `ListAvailableOrgPolicyConstraints` method on the project, folder, or organization.
     */
    export interface Schema$ListAvailableOrgPolicyConstraintsRequest {
        /**
         * Size of the pages to be returned. This is currently unsupported and will be ignored. The server may at any point start using this field to limit page size.
         */
        pageSize?: number | null;
        /**
         * Page token used to retrieve the next page. This is currently unsupported and will be ignored. The server may at any point start using this field.
         */
        pageToken?: string | null;
    }
    /**
     * The response returned from the `ListAvailableOrgPolicyConstraints` method. Returns all `Constraints` that could be set at this level of the hierarchy (contrast with the response from `ListPolicies`, which returns all policies which are set).
     */
    export interface Schema$ListAvailableOrgPolicyConstraintsResponse {
        /**
         * The collection of constraints that are settable on the request resource.
         */
        constraints?: Schema$Constraint[];
        /**
         * Page token used to retrieve the next page. This is currently not used.
         */
        nextPageToken?: string | null;
    }
    /**
     * A `Constraint` that allows or disallows a list of string values, which are configured by an Organization's policy administrator with a `Policy`.
     */
    export interface Schema$ListConstraint {
        /**
         * Optional. The Google Cloud Console will try to default to a configuration that matches the value specified in this `Constraint`.
         */
        suggestedValue?: string | null;
        /**
         * Indicates whether subtrees of Cloud Resource Manager resource hierarchy can be used in `Policy.allowed_values` and `Policy.denied_values`. For example, `"under:folders/123"` would match any resource under the 'folders/123' folder.
         */
        supportsUnder?: boolean | null;
    }
    /**
     * The response message for Liens.ListLiens.
     */
    export interface Schema$ListLiensResponse {
        /**
         * A list of Liens.
         */
        liens?: Schema$Lien[];
        /**
         * Token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
    }
    /**
     * The request sent to the ListOrgPolicies method.
     */
    export interface Schema$ListOrgPoliciesRequest {
        /**
         * Size of the pages to be returned. This is currently unsupported and will be ignored. The server may at any point start using this field to limit page size.
         */
        pageSize?: number | null;
        /**
         * Page token used to retrieve the next page. This is currently unsupported and will be ignored. The server may at any point start using this field.
         */
        pageToken?: string | null;
    }
    /**
     * The response returned from the `ListOrgPolicies` method. It will be empty if no `Policies` are set on the resource.
     */
    export interface Schema$ListOrgPoliciesResponse {
        /**
         * Page token used to retrieve the next page. This is currently not used, but the server may at any point start supplying a valid token.
         */
        nextPageToken?: string | null;
        /**
         * The `Policies` that are set on the resource. It will be empty if no `Policies` are set.
         */
        policies?: Schema$OrgPolicy[];
    }
    /**
     * Used in `policy_type` to specify how `list_policy` behaves at this resource. `ListPolicy` can define specific values and subtrees of Cloud Resource Manager resource hierarchy (`Organizations`, `Folders`, `Projects`) that are allowed or denied by setting the `allowed_values` and `denied_values` fields. This is achieved by using the `under:` and optional `is:` prefixes. The `under:` prefix is used to denote resource subtree values. The `is:` prefix is used to denote specific values, and is required only if the value contains a ":". Values prefixed with "is:" are treated the same as values with no prefix. Ancestry subtrees must be in one of the following formats: - "projects/", e.g. "projects/tokyo-rain-123" - "folders/", e.g. "folders/1234" - "organizations/", e.g. "organizations/1234" The `supports_under` field of the associated `Constraint` defines whether ancestry prefixes can be used. You can set `allowed_values` and `denied_values` in the same `Policy` if `all_values` is `ALL_VALUES_UNSPECIFIED`. `ALLOW` or `DENY` are used to allow or deny all values. If `all_values` is set to either `ALLOW` or `DENY`, `allowed_values` and `denied_values` must be unset.
     */
    export interface Schema$ListPolicy {
        /**
         * List of values allowed at this resource. Can only be set if `all_values` is set to `ALL_VALUES_UNSPECIFIED`.
         */
        allowedValues?: string[] | null;
        /**
         * The policy all_values state.
         */
        allValues?: string | null;
        /**
         * List of values denied at this resource. Can only be set if `all_values` is set to `ALL_VALUES_UNSPECIFIED`.
         */
        deniedValues?: string[] | null;
        /**
         * Determines the inheritance behavior for this `Policy`. By default, a `ListPolicy` set at a resource supersedes any `Policy` set anywhere up the resource hierarchy. However, if `inherit_from_parent` is set to `true`, then the values from the effective `Policy` of the parent resource are inherited, meaning the values set in this `Policy` are added to the values inherited up the hierarchy. Setting `Policy` hierarchies that inherit both allowed values and denied values isn't recommended in most circumstances to keep the configuration simple and understandable. However, it is possible to set a `Policy` with `allowed_values` set that inherits a `Policy` with `denied_values` set. In this case, the values that are allowed must be in `allowed_values` and not present in `denied_values`. For example, suppose you have a `Constraint` `constraints/serviceuser.services`, which has a `constraint_type` of `list_constraint`, and with `constraint_default` set to `ALLOW`. Suppose that at the Organization level, a `Policy` is applied that restricts the allowed API activations to {`E1`, `E2`\}. Then, if a `Policy` is applied to a project below the Organization that has `inherit_from_parent` set to `false` and field all_values set to DENY, then an attempt to activate any API will be denied. The following examples demonstrate different possible layerings for `projects/bar` parented by `organizations/foo`: Example 1 (no inherited values): `organizations/foo` has a `Policy` with values: {allowed_values: "E1" allowed_values:"E2"\} `projects/bar` has `inherit_from_parent` `false` and values: {allowed_values: "E3" allowed_values: "E4"\} The accepted values at `organizations/foo` are `E1`, `E2`. The accepted values at `projects/bar` are `E3`, and `E4`. Example 2 (inherited values): `organizations/foo` has a `Policy` with values: {allowed_values: "E1" allowed_values:"E2"\} `projects/bar` has a `Policy` with values: {value: "E3" value: "E4" inherit_from_parent: true\} The accepted values at `organizations/foo` are `E1`, `E2`. The accepted values at `projects/bar` are `E1`, `E2`, `E3`, and `E4`. Example 3 (inheriting both allowed and denied values): `organizations/foo` has a `Policy` with values: {allowed_values: "E1" allowed_values: "E2"\} `projects/bar` has a `Policy` with: {denied_values: "E1"\} The accepted values at `organizations/foo` are `E1`, `E2`. The value accepted at `projects/bar` is `E2`. Example 4 (RestoreDefault): `organizations/foo` has a `Policy` with values: {allowed_values: "E1" allowed_values:"E2"\} `projects/bar` has a `Policy` with values: {RestoreDefault: {\}\} The accepted values at `organizations/foo` are `E1`, `E2`. The accepted values at `projects/bar` are either all or none depending on the value of `constraint_default` (if `ALLOW`, all; if `DENY`, none). Example 5 (no policy inherits parent policy): `organizations/foo` has no `Policy` set. `projects/bar` has no `Policy` set. The accepted values at both levels are either all or none depending on the value of `constraint_default` (if `ALLOW`, all; if `DENY`, none). Example 6 (ListConstraint allowing all): `organizations/foo` has a `Policy` with values: {allowed_values: "E1" allowed_values: "E2"\} `projects/bar` has a `Policy` with: {all: ALLOW\} The accepted values at `organizations/foo` are `E1`, E2`. Any value is accepted at `projects/bar`. Example 7 (ListConstraint allowing none): `organizations/foo` has a `Policy` with values: {allowed_values: "E1" allowed_values: "E2"\} `projects/bar` has a `Policy` with: {all: DENY\} The accepted values at `organizations/foo` are `E1`, E2`. No value is accepted at `projects/bar`. Example 10 (allowed and denied subtrees of Resource Manager hierarchy): Given the following resource hierarchy O1-\>{F1, F2\}; F1-\>{P1\}; F2-\>{P2, P3\}, `organizations/foo` has a `Policy` with values: {allowed_values: "under:organizations/O1"\} `projects/bar` has a `Policy` with: {allowed_values: "under:projects/P3"\} {denied_values: "under:folders/F2"\} The accepted values at `organizations/foo` are `organizations/O1`, `folders/F1`, `folders/F2`, `projects/P1`, `projects/P2`, `projects/P3`. The accepted values at `projects/bar` are `organizations/O1`, `folders/F1`, `projects/P1`.
         */
        inheritFromParent?: boolean | null;
        /**
         * Optional. The Google Cloud Console will try to default to a configuration that matches the value specified in this `Policy`. If `suggested_value` is not set, it will inherit the value specified higher in the hierarchy, unless `inherit_from_parent` is `false`.
         */
        suggestedValue?: string | null;
    }
    /**
     * A page of the response received from the ListProjects method. A paginated response where more pages are available has `next_page_token` set. This token can be used in a subsequent request to retrieve the next request page.
     */
    export interface Schema$ListProjectsResponse {
        /**
         * Pagination token. If the result set is too large to fit in a single response, this token is returned. It encodes the position of the current result cursor. Feeding this value into a new list request with the `page_token` parameter gives the next page of the results. When `next_page_token` is not filled in, there is no next page and the list returned is the last page in the result set. Pagination tokens have a limited lifetime.
         */
        nextPageToken?: string | null;
        /**
         * The list of Projects that matched the list filter. This list can be paginated.
         */
        projects?: Schema$Project[];
    }
    /**
     * Metadata pertaining to the folder move process.
     */
    export interface Schema$MoveFolderMetadata {
        /**
         * The resource name of the folder or organization to move the folder to.
         */
        destinationParent?: string | null;
        /**
         * The display name of the folder.
         */
        displayName?: string | null;
        /**
         * The resource name of the folder's parent.
         */
        sourceParent?: string | null;
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by MoveProject.
     */
    export interface Schema$MoveProjectMetadata {
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The root node in the resource hierarchy to which a particular entity's (e.g., company) resources belong.
     */
    export interface Schema$Organization {
        /**
         * Timestamp when the Organization was created. Assigned by the server.
         */
        creationTime?: string | null;
        /**
         * A human-readable string that refers to the Organization in the Google Cloud console. This string is set by the server and cannot be changed. The string will be set to the primary domain (for example, "google.com") of the G Suite customer that owns the organization.
         */
        displayName?: string | null;
        /**
         * The organization's current lifecycle state. Assigned by the server.
         */
        lifecycleState?: string | null;
        /**
         * Output only. The resource name of the organization. This is the organization's relative path in the API. Its format is "organizations/[organization_id]". For example, "organizations/1234".
         */
        name?: string | null;
        /**
         * The owner of this Organization. The owner should be specified on creation. Once set, it cannot be changed. This field is required.
         */
        owner?: Schema$OrganizationOwner;
    }
    /**
     * The entity that owns an Organization. The lifetime of the Organization and all of its descendants are bound to the `OrganizationOwner`. If the `OrganizationOwner` is deleted, the Organization and all its descendants will be deleted.
     */
    export interface Schema$OrganizationOwner {
        /**
         * The G Suite customer id used in the Directory API.
         */
        directoryCustomerId?: string | null;
    }
    /**
     * Defines a Cloud Organization `Policy` which is used to specify `Constraints` for configurations of Cloud Platform resources.
     */
    export interface Schema$OrgPolicy {
        /**
         * For boolean `Constraints`, whether to enforce the `Constraint` or not.
         */
        booleanPolicy?: Schema$BooleanPolicy;
        /**
         * The name of the `Constraint` the `Policy` is configuring, for example, `constraints/serviceuser.services`. A [list of available constraints](/resource-manager/docs/organization-policy/org-policy-constraints) is available. Immutable after creation.
         */
        constraint?: string | null;
        /**
         * An opaque tag indicating the current version of the `Policy`, used for concurrency control. When the `Policy` is returned from either a `GetPolicy` or a `ListOrgPolicy` request, this `etag` indicates the version of the current `Policy` to use when executing a read-modify-write loop. When the `Policy` is returned from a `GetEffectivePolicy` request, the `etag` will be unset. When the `Policy` is used in a `SetOrgPolicy` method, use the `etag` value that was returned from a `GetOrgPolicy` request as part of a read-modify-write loop for concurrency control. Not setting the `etag`in a `SetOrgPolicy` request will result in an unconditional write of the `Policy`.
         */
        etag?: string | null;
        /**
         * List of values either allowed or disallowed.
         */
        listPolicy?: Schema$ListPolicy;
        /**
         * Restores the default behavior of the constraint; independent of `Constraint` type.
         */
        restoreDefault?: Schema$RestoreDefault;
        /**
         * The time stamp the `Policy` was previously updated. This is set by the server, not specified by the caller, and represents the last time a call to `SetOrgPolicy` was made for that `Policy`. Any value set by the client will be ignored.
         */
        updateTime?: string | null;
        /**
         * Version of the `Policy`. Default version is 0;
         */
        version?: number | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * A Project is a high-level Google Cloud Platform entity. It is a container for ACLs, APIs, App Engine Apps, VMs, and other Google Cloud Platform resources.
     */
    export interface Schema$Project {
        /**
         * Output only. If this project is a Management Project, list of capabilities configured on the parent folder. Note, presence of any capability implies that this is a Management Project. Example: `folders/123/capabilities/app-management`. OUTPUT ONLY.
         */
        configuredCapabilities?: string[] | null;
        /**
         * Creation time. Read-only.
         */
        createTime?: string | null;
        /**
         * The labels associated with this Project. Label keys must be between 1 and 63 characters long and must conform to the following regular expression: a-z{0,62\}. Label values must be between 0 and 63 characters long and must conform to the regular expression [a-z0-9_-]{0,63\}. A label value can be empty. No more than 256 labels can be associated with a given resource. Clients should store labels in a representation such as JSON that does not depend on specific characters being disallowed. Example: "environment" : "dev" Read-write.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The Project lifecycle state. Read-only.
         */
        lifecycleState?: string | null;
        /**
         * The optional user-assigned display name of the Project. When present it must be between 4 to 30 characters. Allowed characters are: lowercase and uppercase letters, numbers, hyphen, single-quote, double-quote, space, and exclamation point. Example: `My Project` Read-write.
         */
        name?: string | null;
        /**
         * An optional reference to a parent Resource. Supported parent types include "organization" and "folder". Once set, the parent cannot be cleared. The `parent` can be set on creation or using the `UpdateProject` method; the end user must have the `resourcemanager.projects.create` permission on the parent.
         */
        parent?: Schema$ResourceId;
        /**
         * The unique, user-assigned ID of the Project. It must be 6 to 30 lowercase letters, digits, or hyphens. It must start with a letter. Trailing hyphens are prohibited. Example: `tokyo-rain-123` Read-only after creation.
         */
        projectId?: string | null;
        /**
         * The number uniquely identifying the project. Example: `415104041262` Read-only.
         */
        projectNumber?: string | null;
        /**
         * Optional. Input only. Immutable. Tag keys/values directly bound to this project. Each item in the map must be expressed as " : ". For example: "123/environment" : "production", "123/costCenter" : "marketing" Note: Currently this field is in Preview.
         */
        tags?: {
            [key: string]: string;
        } | null;
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by CreateProject. It provides insight for when significant phases of Project creation have completed.
     */
    export interface Schema$ProjectCreationStatus {
        /**
         * Creation time of the project creation workflow.
         */
        createTime?: string | null;
        /**
         * True if the project can be retrieved using GetProject. No other operations on the project are guaranteed to work until the project creation is complete.
         */
        gettable?: boolean | null;
        /**
         * True if the project creation process is complete.
         */
        ready?: boolean | null;
    }
    /**
     * A container to reference an id for any resource type. A `resource` in Google Cloud Platform is a generic term for something you (a developer) may want to interact with through one of our API's. Some examples are an App Engine app, a Compute Engine instance, a Cloud SQL database, and so on.
     */
    export interface Schema$ResourceId {
        /**
         * The type-specific id. This should correspond to the id used in the type-specific API's.
         */
        id?: string | null;
        /**
         * The resource type this id is for. At present, the valid types are: "organization", "folder", and "project".
         */
        type?: string | null;
    }
    /**
     * Ignores policies set above this resource and restores the `constraint_default` enforcement behavior of the specific `Constraint` at this resource. Suppose that `constraint_default` is set to `ALLOW` for the `Constraint` `constraints/serviceuser.services`. Suppose that organization foo.com sets a `Policy` at their Organization resource node that restricts the allowed service activations to deny all service activations. They could then set a `Policy` with the `policy_type` `restore_default` on several experimental projects, restoring the `constraint_default` enforcement of the `Constraint` for only those projects, allowing those projects to have all services activated.
     */
    export interface Schema$RestoreDefault {
    }
    /**
     * The request sent to the `SearchOrganizations` method.
     */
    export interface Schema$SearchOrganizationsRequest {
        /**
         * An optional query string used to filter the Organizations to return in the response. Filter rules are case-insensitive. Organizations may be filtered by `owner.directoryCustomerId` or by `domain`, where the domain is a verified G Suite domain, for example: * Filter `owner.directorycustomerid:123456789` returns Organization resources with `owner.directory_customer_id` equal to `123456789`. * Filter `domain:google.com` returns Organization resources corresponding to the domain `google.com`. This field is optional.
         */
        filter?: string | null;
        /**
         * The maximum number of Organizations to return in the response. The server can return fewer organizations than requested. If unspecified, server picks an appropriate default.
         */
        pageSize?: number | null;
        /**
         * A pagination token returned from a previous call to `SearchOrganizations` that indicates from where listing should continue. This field is optional.
         */
        pageToken?: string | null;
    }
    /**
     * The response returned from the `SearchOrganizations` method.
     */
    export interface Schema$SearchOrganizationsResponse {
        /**
         * A pagination token to be used to retrieve the next page of results. If the result is too large to fit within the page size specified in the request, this field will be set with a token that can be used to fetch the next page of results. If this field is empty, it indicates that this response contains the last page of results.
         */
        nextPageToken?: string | null;
        /**
         * The list of Organizations that matched the search query, possibly paginated.
         */
        organizations?: Schema$Organization[];
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * The request sent to the SetOrgPolicyRequest method.
     */
    export interface Schema$SetOrgPolicyRequest {
        /**
         * `Policy` to set on the resource.
         */
        policy?: Schema$OrgPolicy;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * A status object which is used as the `metadata` field for the `Operation` returned by `UndeleteFolder`.
     */
    export interface Schema$UndeleteFolderMetadata {
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by UndeleteOrganization.
     */
    export interface Schema$UndeleteOrganizationMetadata {
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by `UndeleteProject`.
     */
    export interface Schema$UndeleteProjectMetadata {
    }
    /**
     * The request sent to the UndeleteProject method.
     */
    export interface Schema$UndeleteProjectRequest {
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by UpdateFolder.
     */
    export interface Schema$UpdateFolderMetadata {
    }
    /**
     * A status object which is used as the `metadata` field for the Operation returned by UpdateProject.
     */
    export interface Schema$UpdateProjectMetadata {
    }
    /**
     * Runtime operation information for updating a TagKey.
     */
    export interface Schema$UpdateTagKeyMetadata {
    }
    /**
     * Runtime operation information for updating a TagValue.
     */
    export interface Schema$UpdateTagValueMetadata {
    }
    export class Resource$Folders {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Clears a `Policy` from a resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.folders.clearOrgPolicy({
         *     // Name of the resource for the `Policy` to clear.
         *     resource: 'folders/my-folder',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint",
         *       //   "etag": "my_etag"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        clearOrgPolicy(params: Params$Resource$Folders$Clearorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        clearOrgPolicy(params?: Params$Resource$Folders$Clearorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        clearOrgPolicy(params: Params$Resource$Folders$Clearorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        clearOrgPolicy(params: Params$Resource$Folders$Clearorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        clearOrgPolicy(params: Params$Resource$Folders$Clearorgpolicy, callback: BodyResponseCallback<Schema$Empty>): void;
        clearOrgPolicy(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the effective `Policy` on a resource. This is the result of merging `Policies` in the resource hierarchy. The returned `Policy` will not have an `etag`set because it is a computed `Policy` across multiple resources. Subtrees of Resource Manager resource hierarchy with 'under:' prefix will not be expanded.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.folders.getEffectiveOrgPolicy({
         *     // The name of the resource to start computing the effective `Policy`.
         *     resource: 'folders/my-folder',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getEffectiveOrgPolicy(params: Params$Resource$Folders$Geteffectiveorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getEffectiveOrgPolicy(params?: Params$Resource$Folders$Geteffectiveorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        getEffectiveOrgPolicy(params: Params$Resource$Folders$Geteffectiveorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getEffectiveOrgPolicy(params: Params$Resource$Folders$Geteffectiveorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getEffectiveOrgPolicy(params: Params$Resource$Folders$Geteffectiveorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getEffectiveOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Gets a `Policy` on a resource. If no `Policy` is set on the resource, a `Policy` is returned with default values including `POLICY_TYPE_NOT_SET` for the `policy_type oneof`. The `etag` value can be used with `SetOrgPolicy()` to create or update a `Policy` during read-modify-write.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.folders.getOrgPolicy({
         *     // Name of the resource the `Policy` is set on.
         *     resource: 'folders/my-folder',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getOrgPolicy(params: Params$Resource$Folders$Getorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getOrgPolicy(params?: Params$Resource$Folders$Getorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        getOrgPolicy(params: Params$Resource$Folders$Getorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getOrgPolicy(params: Params$Resource$Folders$Getorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getOrgPolicy(params: Params$Resource$Folders$Getorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Lists `Constraints` that could be applied on the specified resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await cloudresourcemanager.folders.listAvailableOrgPolicyConstraints({
         *       // Name of the resource to list `Constraints` for.
         *       resource: 'folders/my-folder',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "pageSize": 0,
         *         //   "pageToken": "my_pageToken"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "constraints": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listAvailableOrgPolicyConstraints(params: Params$Resource$Folders$Listavailableorgpolicyconstraints, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listAvailableOrgPolicyConstraints(params?: Params$Resource$Folders$Listavailableorgpolicyconstraints, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAvailableOrgPolicyConstraintsResponse>>;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Folders$Listavailableorgpolicyconstraints, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Folders$Listavailableorgpolicyconstraints, options: MethodOptions | BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>, callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Folders$Listavailableorgpolicyconstraints, callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        listAvailableOrgPolicyConstraints(callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        /**
         * Lists all the `Policies` set for a particular resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.folders.listOrgPolicies({
         *     // Name of the resource to list Policies for.
         *     resource: 'folders/my-folder',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "pageSize": 0,
         *       //   "pageToken": "my_pageToken"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "policies": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listOrgPolicies(params: Params$Resource$Folders$Listorgpolicies, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listOrgPolicies(params?: Params$Resource$Folders$Listorgpolicies, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOrgPoliciesResponse>>;
        listOrgPolicies(params: Params$Resource$Folders$Listorgpolicies, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listOrgPolicies(params: Params$Resource$Folders$Listorgpolicies, options: MethodOptions | BodyResponseCallback<Schema$ListOrgPoliciesResponse>, callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        listOrgPolicies(params: Params$Resource$Folders$Listorgpolicies, callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        listOrgPolicies(callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        /**
         * Updates the specified `Policy` on the resource. Creates a new `Policy` for that `Constraint` on the resource if one does not exist. Not supplying an `etag` on the request `Policy` results in an unconditional write of the `Policy`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.folders.setOrgPolicy({
         *     // Resource name of the resource to attach the `Policy`.
         *     resource: 'folders/my-folder',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "policy": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setOrgPolicy(params: Params$Resource$Folders$Setorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setOrgPolicy(params?: Params$Resource$Folders$Setorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        setOrgPolicy(params: Params$Resource$Folders$Setorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setOrgPolicy(params: Params$Resource$Folders$Setorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        setOrgPolicy(params: Params$Resource$Folders$Setorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        setOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
    }
    export interface Params$Resource$Folders$Clearorgpolicy extends StandardParameters {
        /**
         * Name of the resource for the `Policy` to clear.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ClearOrgPolicyRequest;
    }
    export interface Params$Resource$Folders$Geteffectiveorgpolicy extends StandardParameters {
        /**
         * The name of the resource to start computing the effective `Policy`.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetEffectiveOrgPolicyRequest;
    }
    export interface Params$Resource$Folders$Getorgpolicy extends StandardParameters {
        /**
         * Name of the resource the `Policy` is set on.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetOrgPolicyRequest;
    }
    export interface Params$Resource$Folders$Listavailableorgpolicyconstraints extends StandardParameters {
        /**
         * Name of the resource to list `Constraints` for.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ListAvailableOrgPolicyConstraintsRequest;
    }
    export interface Params$Resource$Folders$Listorgpolicies extends StandardParameters {
        /**
         * Name of the resource to list Policies for.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ListOrgPoliciesRequest;
    }
    export interface Params$Resource$Folders$Setorgpolicy extends StandardParameters {
        /**
         * Resource name of the resource to attach the `Policy`.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetOrgPolicyRequest;
    }
    export class Resource$Liens {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Create a Lien which applies to the resource denoted by the `parent` field. Callers of this method will require permission on the `parent` resource. For example, applying to `projects/1234` requires permission `resourcemanager.projects.updateLiens`. NOTE: Some resources may limit the number of Liens which may be applied.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.liens.create({
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "name": "my_name",
         *       //   "origin": "my_origin",
         *       //   "parent": "my_parent",
         *       //   "reason": "my_reason",
         *       //   "restrictions": []
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "name": "my_name",
         *   //   "origin": "my_origin",
         *   //   "parent": "my_parent",
         *   //   "reason": "my_reason",
         *   //   "restrictions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Liens$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Liens$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Lien>>;
        create(params: Params$Resource$Liens$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Liens$Create, options: MethodOptions | BodyResponseCallback<Schema$Lien>, callback: BodyResponseCallback<Schema$Lien>): void;
        create(params: Params$Resource$Liens$Create, callback: BodyResponseCallback<Schema$Lien>): void;
        create(callback: BodyResponseCallback<Schema$Lien>): void;
        /**
         * Delete a Lien by `name`. Callers of this method will require permission on the `parent` resource. For example, a Lien with a `parent` of `projects/1234` requires permission `resourcemanager.projects.updateLiens`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.liens.delete({
         *     // Required. The name/identifier of the Lien to delete.
         *     name: 'liens/.*',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Liens$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Liens$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Liens$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Liens$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Liens$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Retrieve a Lien by `name`. Callers of this method will require permission on the `parent` resource. For example, a Lien with a `parent` of `projects/1234` requires permission `resourcemanager.projects.get`
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.liens.get({
         *     // Required. The name/identifier of the Lien.
         *     name: 'liens/.*',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "name": "my_name",
         *   //   "origin": "my_origin",
         *   //   "parent": "my_parent",
         *   //   "reason": "my_reason",
         *   //   "restrictions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Liens$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Liens$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Lien>>;
        get(params: Params$Resource$Liens$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Liens$Get, options: MethodOptions | BodyResponseCallback<Schema$Lien>, callback: BodyResponseCallback<Schema$Lien>): void;
        get(params: Params$Resource$Liens$Get, callback: BodyResponseCallback<Schema$Lien>): void;
        get(callback: BodyResponseCallback<Schema$Lien>): void;
        /**
         * List all Liens applied to the `parent` resource. Callers of this method will require permission on the `parent` resource. For example, a Lien with a `parent` of `projects/1234` requires permission `resourcemanager.projects.get`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.liens.list({
         *     // The maximum number of items to return. This is a suggestion for the server. The server can return fewer liens than requested. If unspecified, server picks an appropriate default.
         *     pageSize: 'placeholder-value',
         *     // The `next_page_token` value returned from a previous List request, if any.
         *     pageToken: 'placeholder-value',
         *     // Required. The name of the resource to list all attached Liens. For example, `projects/1234`. (google.api.field_policy).resource_type annotation is not set since the parent depends on the meta api implementation. This field could be a project or other sub project resources.
         *     parent: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "liens": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Liens$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Liens$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLiensResponse>>;
        list(params: Params$Resource$Liens$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Liens$List, options: MethodOptions | BodyResponseCallback<Schema$ListLiensResponse>, callback: BodyResponseCallback<Schema$ListLiensResponse>): void;
        list(params: Params$Resource$Liens$List, callback: BodyResponseCallback<Schema$ListLiensResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLiensResponse>): void;
    }
    export interface Params$Resource$Liens$Create extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$Lien;
    }
    export interface Params$Resource$Liens$Delete extends StandardParameters {
        /**
         * Required. The name/identifier of the Lien to delete.
         */
        name?: string;
    }
    export interface Params$Resource$Liens$Get extends StandardParameters {
        /**
         * Required. The name/identifier of the Lien.
         */
        name?: string;
    }
    export interface Params$Resource$Liens$List extends StandardParameters {
        /**
         * The maximum number of items to return. This is a suggestion for the server. The server can return fewer liens than requested. If unspecified, server picks an appropriate default.
         */
        pageSize?: number;
        /**
         * The `next_page_token` value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The name of the resource to list all attached Liens. For example, `projects/1234`. (google.api.field_policy).resource_type annotation is not set since the parent depends on the meta api implementation. This field could be a project or other sub project resources.
         */
        parent?: string;
    }
    export class Resource$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.operations.get({
         *     // The name of the operation resource.
         *     name: 'operations/.*',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export class Resource$Organizations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Clears a `Policy` from a resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.clearOrgPolicy({
         *     // Name of the resource for the `Policy` to clear.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint",
         *       //   "etag": "my_etag"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        clearOrgPolicy(params: Params$Resource$Organizations$Clearorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        clearOrgPolicy(params?: Params$Resource$Organizations$Clearorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        clearOrgPolicy(params: Params$Resource$Organizations$Clearorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        clearOrgPolicy(params: Params$Resource$Organizations$Clearorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        clearOrgPolicy(params: Params$Resource$Organizations$Clearorgpolicy, callback: BodyResponseCallback<Schema$Empty>): void;
        clearOrgPolicy(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Fetches an Organization resource identified by the specified resource name.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.get({
         *     // The resource name of the Organization to fetch. This is the organization's relative path in the API, formatted as "organizations/[organizationId]". For example, "organizations/1234".
         *     name: 'organizations/my-organization',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "creationTime": "my_creationTime",
         *   //   "displayName": "my_displayName",
         *   //   "lifecycleState": "my_lifecycleState",
         *   //   "name": "my_name",
         *   //   "owner": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Organization>>;
        get(params: Params$Resource$Organizations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Get, options: MethodOptions | BodyResponseCallback<Schema$Organization>, callback: BodyResponseCallback<Schema$Organization>): void;
        get(params: Params$Resource$Organizations$Get, callback: BodyResponseCallback<Schema$Organization>): void;
        get(callback: BodyResponseCallback<Schema$Organization>): void;
        /**
         * Gets the effective `Policy` on a resource. This is the result of merging `Policies` in the resource hierarchy. The returned `Policy` will not have an `etag`set because it is a computed `Policy` across multiple resources. Subtrees of Resource Manager resource hierarchy with 'under:' prefix will not be expanded.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.getEffectiveOrgPolicy({
         *     // The name of the resource to start computing the effective `Policy`.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getEffectiveOrgPolicy(params: Params$Resource$Organizations$Geteffectiveorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getEffectiveOrgPolicy(params?: Params$Resource$Organizations$Geteffectiveorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        getEffectiveOrgPolicy(params: Params$Resource$Organizations$Geteffectiveorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getEffectiveOrgPolicy(params: Params$Resource$Organizations$Geteffectiveorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getEffectiveOrgPolicy(params: Params$Resource$Organizations$Geteffectiveorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getEffectiveOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Gets the access control policy for an Organization resource. May be empty if no such policy or resource exists. The `resource` field should be the organization's resource name, e.g. "organizations/123". Authorization requires the Google IAM permission `resourcemanager.organizations.getIamPolicy` on the specified organization
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.getIamPolicy({
         *     // REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "options": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Organizations$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Organizations$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Organizations$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Organizations$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Organizations$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Gets a `Policy` on a resource. If no `Policy` is set on the resource, a `Policy` is returned with default values including `POLICY_TYPE_NOT_SET` for the `policy_type oneof`. The `etag` value can be used with `SetOrgPolicy()` to create or update a `Policy` during read-modify-write.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.getOrgPolicy({
         *     // Name of the resource the `Policy` is set on.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getOrgPolicy(params: Params$Resource$Organizations$Getorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getOrgPolicy(params?: Params$Resource$Organizations$Getorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        getOrgPolicy(params: Params$Resource$Organizations$Getorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getOrgPolicy(params: Params$Resource$Organizations$Getorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getOrgPolicy(params: Params$Resource$Organizations$Getorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Lists `Constraints` that could be applied on the specified resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await cloudresourcemanager.organizations.listAvailableOrgPolicyConstraints({
         *       // Name of the resource to list `Constraints` for.
         *       resource: 'organizations/my-organization',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "pageSize": 0,
         *         //   "pageToken": "my_pageToken"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "constraints": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listAvailableOrgPolicyConstraints(params: Params$Resource$Organizations$Listavailableorgpolicyconstraints, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listAvailableOrgPolicyConstraints(params?: Params$Resource$Organizations$Listavailableorgpolicyconstraints, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAvailableOrgPolicyConstraintsResponse>>;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Organizations$Listavailableorgpolicyconstraints, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Organizations$Listavailableorgpolicyconstraints, options: MethodOptions | BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>, callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Organizations$Listavailableorgpolicyconstraints, callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        listAvailableOrgPolicyConstraints(callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        /**
         * Lists all the `Policies` set for a particular resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.listOrgPolicies({
         *     // Name of the resource to list Policies for.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "pageSize": 0,
         *       //   "pageToken": "my_pageToken"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "policies": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listOrgPolicies(params: Params$Resource$Organizations$Listorgpolicies, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listOrgPolicies(params?: Params$Resource$Organizations$Listorgpolicies, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOrgPoliciesResponse>>;
        listOrgPolicies(params: Params$Resource$Organizations$Listorgpolicies, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listOrgPolicies(params: Params$Resource$Organizations$Listorgpolicies, options: MethodOptions | BodyResponseCallback<Schema$ListOrgPoliciesResponse>, callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        listOrgPolicies(params: Params$Resource$Organizations$Listorgpolicies, callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        listOrgPolicies(callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        /**
         * Searches Organization resources that are visible to the user and satisfy the specified filter. This method returns Organizations in an unspecified order. New Organizations do not necessarily appear at the end of the results. Search will only return organizations on which the user has the permission `resourcemanager.organizations.get` or has super admin privileges.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.search({
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "filter": "my_filter",
         *       //   "pageSize": 0,
         *       //   "pageToken": "my_pageToken"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "organizations": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        search(params: Params$Resource$Organizations$Search, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        search(params?: Params$Resource$Organizations$Search, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$SearchOrganizationsResponse>>;
        search(params: Params$Resource$Organizations$Search, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        search(params: Params$Resource$Organizations$Search, options: MethodOptions | BodyResponseCallback<Schema$SearchOrganizationsResponse>, callback: BodyResponseCallback<Schema$SearchOrganizationsResponse>): void;
        search(params: Params$Resource$Organizations$Search, callback: BodyResponseCallback<Schema$SearchOrganizationsResponse>): void;
        search(callback: BodyResponseCallback<Schema$SearchOrganizationsResponse>): void;
        /**
         * Sets the access control policy on an Organization resource. Replaces any existing policy. The `resource` field should be the organization's resource name, e.g. "organizations/123". Authorization requires the Google IAM permission `resourcemanager.organizations.setIamPolicy` on the specified organization
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.setIamPolicy({
         *     // REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "policy": {},
         *       //   "updateMask": "my_updateMask"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Organizations$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Organizations$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Organizations$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Organizations$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Organizations$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Updates the specified `Policy` on the resource. Creates a new `Policy` for that `Constraint` on the resource if one does not exist. Not supplying an `etag` on the request `Policy` results in an unconditional write of the `Policy`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.setOrgPolicy({
         *     // Resource name of the resource to attach the `Policy`.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "policy": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setOrgPolicy(params: Params$Resource$Organizations$Setorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setOrgPolicy(params?: Params$Resource$Organizations$Setorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        setOrgPolicy(params: Params$Resource$Organizations$Setorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setOrgPolicy(params: Params$Resource$Organizations$Setorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        setOrgPolicy(params: Params$Resource$Organizations$Setorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        setOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Returns permissions that a caller has on the specified Organization. The `resource` field should be the organization's resource name, e.g. "organizations/123". There are no permissions required for making this API call.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.organizations.testIamPermissions({
         *     // REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'organizations/my-organization',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "permissions": []
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "permissions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Organizations$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Organizations$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Organizations$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Organizations$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Organizations$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Organizations$Clearorgpolicy extends StandardParameters {
        /**
         * Name of the resource for the `Policy` to clear.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ClearOrgPolicyRequest;
    }
    export interface Params$Resource$Organizations$Get extends StandardParameters {
        /**
         * The resource name of the Organization to fetch. This is the organization's relative path in the API, formatted as "organizations/[organizationId]". For example, "organizations/1234".
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Geteffectiveorgpolicy extends StandardParameters {
        /**
         * The name of the resource to start computing the effective `Policy`.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetEffectiveOrgPolicyRequest;
    }
    export interface Params$Resource$Organizations$Getiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetIamPolicyRequest;
    }
    export interface Params$Resource$Organizations$Getorgpolicy extends StandardParameters {
        /**
         * Name of the resource the `Policy` is set on.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetOrgPolicyRequest;
    }
    export interface Params$Resource$Organizations$Listavailableorgpolicyconstraints extends StandardParameters {
        /**
         * Name of the resource to list `Constraints` for.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ListAvailableOrgPolicyConstraintsRequest;
    }
    export interface Params$Resource$Organizations$Listorgpolicies extends StandardParameters {
        /**
         * Name of the resource to list Policies for.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ListOrgPoliciesRequest;
    }
    export interface Params$Resource$Organizations$Search extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$SearchOrganizationsRequest;
    }
    export interface Params$Resource$Organizations$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Organizations$Setorgpolicy extends StandardParameters {
        /**
         * Resource name of the resource to attach the `Policy`.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetOrgPolicyRequest;
    }
    export interface Params$Resource$Organizations$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Clears a `Policy` from a resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.clearOrgPolicy({
         *     // Name of the resource for the `Policy` to clear.
         *     resource: 'projects/my-project',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint",
         *       //   "etag": "my_etag"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        clearOrgPolicy(params: Params$Resource$Projects$Clearorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        clearOrgPolicy(params?: Params$Resource$Projects$Clearorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        clearOrgPolicy(params: Params$Resource$Projects$Clearorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        clearOrgPolicy(params: Params$Resource$Projects$Clearorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        clearOrgPolicy(params: Params$Resource$Projects$Clearorgpolicy, callback: BodyResponseCallback<Schema$Empty>): void;
        clearOrgPolicy(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Request that a new Project be created. The result is an Operation which can be used to track the creation process. This process usually takes a few seconds, but can sometimes take much longer. The tracking Operation is automatically deleted after a few hours, so there is no need to call DeleteOperation. Authorization requires the Google IAM permission `resourcemanager.projects.create` on the specified parent for the new project. The parent is identified by a specified ResourceId, which must include both an ID and a type, such as organization. This method does not associate the new project with a billing account. You can set or update the billing account associated with a project using the [`projects.updateBillingInfo`] (/billing/reference/rest/v1/projects/updateBillingInfo) method.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.create({
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "configuredCapabilities": [],
         *       //   "createTime": "my_createTime",
         *       //   "labels": {},
         *       //   "lifecycleState": "my_lifecycleState",
         *       //   "name": "my_name",
         *       //   "parent": {},
         *       //   "projectId": "my_projectId",
         *       //   "projectNumber": "my_projectNumber",
         *       //   "tags": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Marks the Project identified by the specified `project_id` (for example, `my-project-123`) for deletion. This method will only affect the Project if it has a lifecycle state of ACTIVE. This method changes the Project's lifecycle state from ACTIVE to DELETE_REQUESTED. The deletion starts at an unspecified time, at which point the Project is no longer accessible. Until the deletion completes, you can check the lifecycle state checked by retrieving the Project with GetProject, and the Project remains visible to ListProjects. However, you cannot update the project. After the deletion completes, the Project is not retrievable by the GetProject and ListProjects methods. The caller must have delete permissions for this Project.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.delete({
         *     // The Project ID (for example, `foo-bar-123`). Required.
         *     projectId: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Retrieves the Project identified by the specified `project_id` (for example, `my-project-123`). The caller must have read permissions for this Project.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.get({
         *     // Required. The Project ID (for example, `my-project-123`).
         *     projectId: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "configuredCapabilities": [],
         *   //   "createTime": "my_createTime",
         *   //   "labels": {},
         *   //   "lifecycleState": "my_lifecycleState",
         *   //   "name": "my_name",
         *   //   "parent": {},
         *   //   "projectId": "my_projectId",
         *   //   "projectNumber": "my_projectNumber",
         *   //   "tags": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Project>>;
        get(params: Params$Resource$Projects$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Get, options: MethodOptions | BodyResponseCallback<Schema$Project>, callback: BodyResponseCallback<Schema$Project>): void;
        get(params: Params$Resource$Projects$Get, callback: BodyResponseCallback<Schema$Project>): void;
        get(callback: BodyResponseCallback<Schema$Project>): void;
        /**
         * Gets a list of ancestors in the resource hierarchy for the Project identified by the specified `project_id` (for example, `my-project-123`). The caller must have read permissions for this Project.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.getAncestry({
         *     // Required. The Project ID (for example, `my-project-123`).
         *     projectId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "ancestor": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getAncestry(params: Params$Resource$Projects$Getancestry, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getAncestry(params?: Params$Resource$Projects$Getancestry, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GetAncestryResponse>>;
        getAncestry(params: Params$Resource$Projects$Getancestry, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getAncestry(params: Params$Resource$Projects$Getancestry, options: MethodOptions | BodyResponseCallback<Schema$GetAncestryResponse>, callback: BodyResponseCallback<Schema$GetAncestryResponse>): void;
        getAncestry(params: Params$Resource$Projects$Getancestry, callback: BodyResponseCallback<Schema$GetAncestryResponse>): void;
        getAncestry(callback: BodyResponseCallback<Schema$GetAncestryResponse>): void;
        /**
         * Gets the effective `Policy` on a resource. This is the result of merging `Policies` in the resource hierarchy. The returned `Policy` will not have an `etag`set because it is a computed `Policy` across multiple resources. Subtrees of Resource Manager resource hierarchy with 'under:' prefix will not be expanded.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.getEffectiveOrgPolicy({
         *     // The name of the resource to start computing the effective `Policy`.
         *     resource: 'projects/my-project',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getEffectiveOrgPolicy(params: Params$Resource$Projects$Geteffectiveorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getEffectiveOrgPolicy(params?: Params$Resource$Projects$Geteffectiveorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        getEffectiveOrgPolicy(params: Params$Resource$Projects$Geteffectiveorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getEffectiveOrgPolicy(params: Params$Resource$Projects$Geteffectiveorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getEffectiveOrgPolicy(params: Params$Resource$Projects$Geteffectiveorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getEffectiveOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Returns the IAM access control policy for the specified Project. Permission is denied if the policy or the resource does not exist. Authorization requires the Google IAM permission `resourcemanager.projects.getIamPolicy` on the project. For additional information about `resource` (e.g. my-project-id) structure and identification, see [Resource Names](https://cloud.google.com/apis/design/resource_names).
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.getIamPolicy({
         *     // REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "options": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Gets a `Policy` on a resource. If no `Policy` is set on the resource, a `Policy` is returned with default values including `POLICY_TYPE_NOT_SET` for the `policy_type oneof`. The `etag` value can be used with `SetOrgPolicy()` to create or update a `Policy` during read-modify-write.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.getOrgPolicy({
         *     // Name of the resource the `Policy` is set on.
         *     resource: 'projects/my-project',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "constraint": "my_constraint"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getOrgPolicy(params: Params$Resource$Projects$Getorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getOrgPolicy(params?: Params$Resource$Projects$Getorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        getOrgPolicy(params: Params$Resource$Projects$Getorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getOrgPolicy(params: Params$Resource$Projects$Getorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getOrgPolicy(params: Params$Resource$Projects$Getorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        getOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Lists Projects that the caller has the `resourcemanager.projects.get` permission on and satisfy the specified filter. This method returns Projects in an unspecified order. This method is eventually consistent with project mutations; this means that a newly created project may not appear in the results or recent updates to an existing project may not be reflected in the results. To retrieve the latest state of a project, use the GetProject method. NOTE: If the request filter contains a `parent.type` and `parent.id` and the caller has the `resourcemanager.projects.list` permission on the parent, the results will be drawn from an alternate index which provides more consistent results. In future versions of this API, this List method will be split into List and Search to properly capture the behavioral difference.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.list({
         *     // Optional. An expression for filtering the results of the request. Filter rules are case insensitive. If multiple fields are included in a filter query, the query will return results that match any of the fields. Some eligible fields for filtering are: + `name` + `id` + `labels.` (where *key* is the name of a label) + `parent.type` + `parent.id` + `lifecycleState` Some examples of filter queries: | Query | Description | |------------------|-----------------------------------------------------| | name:how* | The project's name starts with "how". | | name:Howl | The project's name is `Howl` or `howl`. | | name:HOWL | Equivalent to above. | | NAME:howl | Equivalent to above. | | labels.color:* | The project has the label `color`. | | labels.color:red | The project's label `color` has the value `red`. | | labels.color:red labels.size:big | The project's label `color` has the value `red` or its label `size` has the value `big`. | | lifecycleState:DELETE_REQUESTED | Only show projects that are pending deletion.| If no filter is specified, the call will return projects for which the user has the `resourcemanager.projects.get` permission. NOTE: To perform a by-parent query (eg., what projects are directly in a Folder), the caller must have the `resourcemanager.projects.list` permission on the parent and the filter must contain both a `parent.type` and a `parent.id` restriction (example: "parent.type:folder parent.id:123"). In this case an alternate search index is used which provides more consistent results.
         *     filter: 'placeholder-value',
         *     // Optional. The maximum number of Projects to return in the response. The server can return fewer Projects than requested. If unspecified, server picks an appropriate default.
         *     pageSize: 'placeholder-value',
         *     // Optional. A pagination token returned from a previous call to ListProjects that indicates from where listing should continue.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "projects": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListProjectsResponse>>;
        list(params: Params$Resource$Projects$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$List, options: MethodOptions | BodyResponseCallback<Schema$ListProjectsResponse>, callback: BodyResponseCallback<Schema$ListProjectsResponse>): void;
        list(params: Params$Resource$Projects$List, callback: BodyResponseCallback<Schema$ListProjectsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListProjectsResponse>): void;
        /**
         * Lists `Constraints` that could be applied on the specified resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res =
         *     await cloudresourcemanager.projects.listAvailableOrgPolicyConstraints({
         *       // Name of the resource to list `Constraints` for.
         *       resource: 'projects/my-project',
         *
         *       // Request body metadata
         *       requestBody: {
         *         // request body parameters
         *         // {
         *         //   "pageSize": 0,
         *         //   "pageToken": "my_pageToken"
         *         // }
         *       },
         *     });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "constraints": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listAvailableOrgPolicyConstraints(params: Params$Resource$Projects$Listavailableorgpolicyconstraints, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listAvailableOrgPolicyConstraints(params?: Params$Resource$Projects$Listavailableorgpolicyconstraints, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAvailableOrgPolicyConstraintsResponse>>;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Projects$Listavailableorgpolicyconstraints, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Projects$Listavailableorgpolicyconstraints, options: MethodOptions | BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>, callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        listAvailableOrgPolicyConstraints(params: Params$Resource$Projects$Listavailableorgpolicyconstraints, callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        listAvailableOrgPolicyConstraints(callback: BodyResponseCallback<Schema$ListAvailableOrgPolicyConstraintsResponse>): void;
        /**
         * Lists all the `Policies` set for a particular resource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.listOrgPolicies({
         *     // Name of the resource to list Policies for.
         *     resource: 'projects/my-project',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "pageSize": 0,
         *       //   "pageToken": "my_pageToken"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "policies": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listOrgPolicies(params: Params$Resource$Projects$Listorgpolicies, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listOrgPolicies(params?: Params$Resource$Projects$Listorgpolicies, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOrgPoliciesResponse>>;
        listOrgPolicies(params: Params$Resource$Projects$Listorgpolicies, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listOrgPolicies(params: Params$Resource$Projects$Listorgpolicies, options: MethodOptions | BodyResponseCallback<Schema$ListOrgPoliciesResponse>, callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        listOrgPolicies(params: Params$Resource$Projects$Listorgpolicies, callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        listOrgPolicies(callback: BodyResponseCallback<Schema$ListOrgPoliciesResponse>): void;
        /**
         * Sets the IAM access control policy for the specified Project. CAUTION: This method will replace the existing policy, and cannot be used to append additional IAM settings. NOTE: Removing service accounts from policies or changing their roles can render services completely inoperable. It is important to understand how the service account is being used before removing or updating its roles. For additional information about `resource` (e.g. my-project-id) structure and identification, see [Resource Names](https://cloud.google.com/apis/design/resource_names). The following constraints apply when using `setIamPolicy()`: + Project does not support `allUsers` and `allAuthenticatedUsers` as `members` in a `Binding` of a `Policy`. + The owner role can be granted to a `user`, `serviceAccount`, or a group that is part of an organization. For example, <EMAIL> could be added as an owner to a project in the myownpersonaldomain.com organization, but not the examplepetstore.com organization. + Service accounts can be made owners of a project directly without any restrictions. However, to be added as an owner, a user must be invited via Cloud Platform console and must accept the invitation. + A user cannot be granted the owner role using `setIamPolicy()`. The user must be granted the owner role using the Cloud Platform Console and must explicitly accept the invitation. + You can only grant ownership of a project to a member by using the Google Cloud console. Inviting a member will deliver an invitation email that they must accept. An invitation email is not generated if you are granting a role other than owner, or if both the member you are inviting and the project are part of your organization. + If the project is not part of an organization, there must be at least one owner who has accepted the Terms of Service (ToS) agreement in the policy. Calling `setIamPolicy()` to remove the last ToS-accepted owner from the policy will fail. This restriction also applies to legacy projects that no longer have owners who have accepted the ToS. Edits to IAM policies will be rejected until the lack of a ToS-accepting owner is rectified. If the project is part of an organization, you can remove all owners, potentially making the organization inaccessible. Authorization requires the Google IAM permission `resourcemanager.projects.setIamPolicy` on the project
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.setIamPolicy({
         *     // REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "policy": {},
         *       //   "updateMask": "my_updateMask"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "auditConfigs": [],
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Updates the specified `Policy` on the resource. Creates a new `Policy` for that `Constraint` on the resource if one does not exist. Not supplying an `etag` on the request `Policy` results in an unconditional write of the `Policy`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.setOrgPolicy({
         *     // Resource name of the resource to attach the `Policy`.
         *     resource: 'projects/my-project',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "policy": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "booleanPolicy": {},
         *   //   "constraint": "my_constraint",
         *   //   "etag": "my_etag",
         *   //   "listPolicy": {},
         *   //   "restoreDefault": {},
         *   //   "updateTime": "my_updateTime",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setOrgPolicy(params: Params$Resource$Projects$Setorgpolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setOrgPolicy(params?: Params$Resource$Projects$Setorgpolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$OrgPolicy>>;
        setOrgPolicy(params: Params$Resource$Projects$Setorgpolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setOrgPolicy(params: Params$Resource$Projects$Setorgpolicy, options: MethodOptions | BodyResponseCallback<Schema$OrgPolicy>, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        setOrgPolicy(params: Params$Resource$Projects$Setorgpolicy, callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        setOrgPolicy(callback: BodyResponseCallback<Schema$OrgPolicy>): void;
        /**
         * Returns permissions that a caller has on the specified Project. For additional information about `resource` (e.g. my-project-id) structure and identification, see [Resource Names](https://cloud.google.com/apis/design/resource_names). There are no permissions required for making this API call.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: [
         *       'https://www.googleapis.com/auth/cloud-platform',
         *       'https://www.googleapis.com/auth/cloud-platform.read-only',
         *     ],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.testIamPermissions({
         *     // REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "permissions": []
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "permissions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Restores the Project identified by the specified `project_id` (for example, `my-project-123`). You can only use this method for a Project that has a lifecycle state of DELETE_REQUESTED. After deletion starts, the Project cannot be restored. The caller must have undelete permissions for this Project.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.undelete({
         *     // Required. The project ID (for example, `foo-bar-123`).
         *     projectId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        undelete(params: Params$Resource$Projects$Undelete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        undelete(params?: Params$Resource$Projects$Undelete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        undelete(params: Params$Resource$Projects$Undelete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        undelete(params: Params$Resource$Projects$Undelete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        undelete(params: Params$Resource$Projects$Undelete, callback: BodyResponseCallback<Schema$Empty>): void;
        undelete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Updates the attributes of the Project identified by the specified `project_id` (for example, `my-project-123`). The caller must have modify permissions for this Project.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/cloudresourcemanager.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const cloudresourcemanager = google.cloudresourcemanager('v1');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await cloudresourcemanager.projects.update({
         *     // The project ID (for example, `my-project-123`). Required.
         *     projectId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "configuredCapabilities": [],
         *       //   "createTime": "my_createTime",
         *       //   "labels": {},
         *       //   "lifecycleState": "my_lifecycleState",
         *       //   "name": "my_name",
         *       //   "parent": {},
         *       //   "projectId": "my_projectId",
         *       //   "projectNumber": "my_projectNumber",
         *       //   "tags": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "configuredCapabilities": [],
         *   //   "createTime": "my_createTime",
         *   //   "labels": {},
         *   //   "lifecycleState": "my_lifecycleState",
         *   //   "name": "my_name",
         *   //   "parent": {},
         *   //   "projectId": "my_projectId",
         *   //   "projectNumber": "my_projectNumber",
         *   //   "tags": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        update(params: Params$Resource$Projects$Update, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        update(params?: Params$Resource$Projects$Update, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Project>>;
        update(params: Params$Resource$Projects$Update, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        update(params: Params$Resource$Projects$Update, options: MethodOptions | BodyResponseCallback<Schema$Project>, callback: BodyResponseCallback<Schema$Project>): void;
        update(params: Params$Resource$Projects$Update, callback: BodyResponseCallback<Schema$Project>): void;
        update(callback: BodyResponseCallback<Schema$Project>): void;
    }
    export interface Params$Resource$Projects$Clearorgpolicy extends StandardParameters {
        /**
         * Name of the resource for the `Policy` to clear.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ClearOrgPolicyRequest;
    }
    export interface Params$Resource$Projects$Create extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$Project;
    }
    export interface Params$Resource$Projects$Delete extends StandardParameters {
        /**
         * The Project ID (for example, `foo-bar-123`). Required.
         */
        projectId?: string;
    }
    export interface Params$Resource$Projects$Get extends StandardParameters {
        /**
         * Required. The Project ID (for example, `my-project-123`).
         */
        projectId?: string;
    }
    export interface Params$Resource$Projects$Getancestry extends StandardParameters {
        /**
         * Required. The Project ID (for example, `my-project-123`).
         */
        projectId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetAncestryRequest;
    }
    export interface Params$Resource$Projects$Geteffectiveorgpolicy extends StandardParameters {
        /**
         * The name of the resource to start computing the effective `Policy`.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetEffectiveOrgPolicyRequest;
    }
    export interface Params$Resource$Projects$Getiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Getorgpolicy extends StandardParameters {
        /**
         * Name of the resource the `Policy` is set on.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GetOrgPolicyRequest;
    }
    export interface Params$Resource$Projects$List extends StandardParameters {
        /**
         * Optional. An expression for filtering the results of the request. Filter rules are case insensitive. If multiple fields are included in a filter query, the query will return results that match any of the fields. Some eligible fields for filtering are: + `name` + `id` + `labels.` (where *key* is the name of a label) + `parent.type` + `parent.id` + `lifecycleState` Some examples of filter queries: | Query | Description | |------------------|-----------------------------------------------------| | name:how* | The project's name starts with "how". | | name:Howl | The project's name is `Howl` or `howl`. | | name:HOWL | Equivalent to above. | | NAME:howl | Equivalent to above. | | labels.color:* | The project has the label `color`. | | labels.color:red | The project's label `color` has the value `red`. | | labels.color:red labels.size:big | The project's label `color` has the value `red` or its label `size` has the value `big`. | | lifecycleState:DELETE_REQUESTED | Only show projects that are pending deletion.| If no filter is specified, the call will return projects for which the user has the `resourcemanager.projects.get` permission. NOTE: To perform a by-parent query (eg., what projects are directly in a Folder), the caller must have the `resourcemanager.projects.list` permission on the parent and the filter must contain both a `parent.type` and a `parent.id` restriction (example: "parent.type:folder parent.id:123"). In this case an alternate search index is used which provides more consistent results.
         */
        filter?: string;
        /**
         * Optional. The maximum number of Projects to return in the response. The server can return fewer Projects than requested. If unspecified, server picks an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A pagination token returned from a previous call to ListProjects that indicates from where listing should continue.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Listavailableorgpolicyconstraints extends StandardParameters {
        /**
         * Name of the resource to list `Constraints` for.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ListAvailableOrgPolicyConstraintsRequest;
    }
    export interface Params$Resource$Projects$Listorgpolicies extends StandardParameters {
        /**
         * Name of the resource to list Policies for.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ListOrgPoliciesRequest;
    }
    export interface Params$Resource$Projects$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Setorgpolicy extends StandardParameters {
        /**
         * Resource name of the resource to attach the `Policy`.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetOrgPolicyRequest;
    }
    export interface Params$Resource$Projects$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Undelete extends StandardParameters {
        /**
         * Required. The project ID (for example, `foo-bar-123`).
         */
        projectId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UndeleteProjectRequest;
    }
    export interface Params$Resource$Projects$Update extends StandardParameters {
        /**
         * The project ID (for example, `my-project-123`). Required.
         */
        projectId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Project;
    }
    export {};
}
