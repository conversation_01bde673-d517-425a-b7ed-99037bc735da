'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import FileUpload from '@/components/FileUpload';
import { FileUploadProgress } from '@/types';

interface FileMetadata {
  description: string;
  tags: string[];
  isPublic: boolean;
}

export default function UploadPage() {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress[]>([]);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleUpload = async (files: File[], metadata: FileMetadata[]) => {
    setUploading(true);
    setError('');
    
    // Initialize progress tracking
    const initialProgress: FileUploadProgress[] = files.map((file, index) => ({
      id: `${Date.now()}-${index}`,
      filename: file.name,
      progress: 0,
      status: 'pending',
    }));
    setUploadProgress(initialProgress);

    try {
      // Upload files one by one
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileMetadata = metadata[i];
        
        // Update progress to uploading
        setUploadProgress(prev => prev.map((item, index) => 
          index === i 
            ? { ...item, status: 'uploading', progress: 0 }
            : item
        ));

        // Create form data
        const formData = new FormData();
        formData.append('file', file);
        formData.append('description', fileMetadata.description);
        formData.append('tags', fileMetadata.tags.join(','));
        formData.append('isPublic', fileMetadata.isPublic.toString());

        try {
          const response = await fetch('/api/files/upload', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Upload failed');
          }

          // Update progress to completed
          setUploadProgress(prev => prev.map((item, index) => 
            index === i 
              ? { ...item, status: 'completed', progress: 100 }
              : item
          ));

        } catch (fileError) {
          console.error(`Error uploading ${file.name}:`, fileError);
          
          // Update progress to error
          setUploadProgress(prev => prev.map((item, index) => 
            index === i 
              ? { 
                  ...item, 
                  status: 'error', 
                  error: fileError instanceof Error ? fileError.message : 'Upload failed'
                }
              : item
          ));
        }
      }

      // Check if all uploads completed successfully
      const hasErrors = uploadProgress.some(item => item.status === 'error');
      if (!hasErrors) {
        // Redirect to dashboard after successful upload
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      }

    } catch (error) {
      console.error('Upload error:', error);
      setError(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Upload Files</h1>
            <p className="mt-1 text-sm text-gray-600">
              Upload your documents to the repository. Files will be stored securely in Google Drive.
            </p>
          </div>

          <div className="p-6">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            <FileUpload
              onUpload={handleUpload}
              disabled={uploading}
              maxFiles={5}
              maxFileSize={10 * 1024 * 1024} // 10MB
              allowedFileTypes={['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar']}
            />

            {/* Upload Progress */}
            {uploadProgress.length > 0 && (
              <div className="mt-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Progress</h3>
                <div className="space-y-4">
                  {uploadProgress.map((item) => (
                    <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-900">
                          {item.filename}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          item.status === 'completed' 
                            ? 'bg-green-100 text-green-800'
                            : item.status === 'error'
                            ? 'bg-red-100 text-red-800'
                            : item.status === 'uploading'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {item.status === 'completed' && 'Completed'}
                          {item.status === 'error' && 'Error'}
                          {item.status === 'uploading' && 'Uploading...'}
                          {item.status === 'pending' && 'Pending'}
                        </span>
                      </div>

                      {item.status === 'uploading' && (
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${item.progress}%` }}
                          />
                        </div>
                      )}

                      {item.status === 'completed' && (
                        <div className="flex items-center text-green-600">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-sm">Upload completed successfully</span>
                        </div>
                      )}

                      {item.status === 'error' && item.error && (
                        <div className="flex items-center text-red-600">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                          <span className="text-sm">{item.error}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {uploadProgress.every(item => item.status === 'completed') && (
                  <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-green-700">
                        All files uploaded successfully! Redirecting to dashboard...
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
