import { createServiceRoleClient } from './supabase';
import { FileRecord, User, FilePermission, AuditLog, PaginationParams, PaginatedResponse } from '@/types';

export class DatabaseService {
  private supabase = createServiceRoleClient();

  // User operations
  async createUser(userData: {
    id: string;
    email: string;
    full_name?: string;
    role?: 'admin' | 'user';
  }): Promise<User> {
    const { data, error } = await this.supabase
      .from('users')
      .insert(userData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserById(id: string): Promise<User | null> {
    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const { data, error } = await this.supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getAllUsers(params: PaginationParams): Promise<PaginatedResponse<User>> {
    let query = this.supabase
      .from('users')
      .select('*', { count: 'exact' });

    if (params.search) {
      query = query.or(`email.ilike.%${params.search}%,full_name.ilike.%${params.search}%`);
    }

    if (params.sortBy) {
      query = query.order(params.sortBy, { ascending: params.sortOrder === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const from = (params.page - 1) * params.limit;
    const to = from + params.limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) throw error;

    const totalPages = Math.ceil((count || 0) / params.limit);

    return {
      data: data || [],
      pagination: {
        page: params.page,
        limit: params.limit,
        total: count || 0,
        totalPages,
        hasNext: params.page < totalPages,
        hasPrev: params.page > 1,
      },
    };
  }

  // File operations
  async createFile(fileData: Omit<FileRecord, 'id' | 'uploaded_at' | 'updated_at'>): Promise<FileRecord> {
    const { data, error } = await this.supabase
      .from('files')
      .insert(fileData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getFileById(id: string): Promise<FileRecord | null> {
    const { data, error } = await this.supabase
      .from('files')
      .select('*')
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  async updateFile(id: string, updates: Partial<FileRecord>): Promise<FileRecord> {
    const { data, error } = await this.supabase
      .from('files')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteFile(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('files')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  async getFilesByUser(userId: string, params: PaginationParams): Promise<PaginatedResponse<FileRecord>> {
    let query = this.supabase
      .from('files')
      .select('*', { count: 'exact' })
      .eq('uploaded_by', userId);

    if (params.search) {
      query = query.or(`filename.ilike.%${params.search}%,original_filename.ilike.%${params.search}%,description.ilike.%${params.search}%`);
    }

    if (params.filter?.fileType) {
      query = query.eq('file_type', params.filter.fileType);
    }

    if (params.filter?.isPublic !== undefined) {
      query = query.eq('is_public', params.filter.isPublic);
    }

    if (params.filter?.tags && params.filter.tags.length > 0) {
      query = query.overlaps('tags', params.filter.tags);
    }

    if (params.sortBy) {
      query = query.order(params.sortBy, { ascending: params.sortOrder === 'asc' });
    } else {
      query = query.order('uploaded_at', { ascending: false });
    }

    const from = (params.page - 1) * params.limit;
    const to = from + params.limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) throw error;

    const totalPages = Math.ceil((count || 0) / params.limit);

    return {
      data: data || [],
      pagination: {
        page: params.page,
        limit: params.limit,
        total: count || 0,
        totalPages,
        hasNext: params.page < totalPages,
        hasPrev: params.page > 1,
      },
    };
  }

  async getAllFiles(params: PaginationParams): Promise<PaginatedResponse<FileRecord>> {
    let query = this.supabase
      .from('files')
      .select(`
        *,
        users!files_uploaded_by_fkey(email, full_name)
      `, { count: 'exact' });

    if (params.search) {
      query = query.or(`filename.ilike.%${params.search}%,original_filename.ilike.%${params.search}%,description.ilike.%${params.search}%`);
    }

    if (params.filter?.fileType) {
      query = query.eq('file_type', params.filter.fileType);
    }

    if (params.filter?.uploadedBy) {
      query = query.eq('uploaded_by', params.filter.uploadedBy);
    }

    if (params.filter?.isPublic !== undefined) {
      query = query.eq('is_public', params.filter.isPublic);
    }

    if (params.filter?.tags && params.filter.tags.length > 0) {
      query = query.overlaps('tags', params.filter.tags);
    }

    if (params.sortBy) {
      query = query.order(params.sortBy, { ascending: params.sortOrder === 'asc' });
    } else {
      query = query.order('uploaded_at', { ascending: false });
    }

    const from = (params.page - 1) * params.limit;
    const to = from + params.limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) throw error;

    const totalPages = Math.ceil((count || 0) / params.limit);

    return {
      data: data || [],
      pagination: {
        page: params.page,
        limit: params.limit,
        total: count || 0,
        totalPages,
        hasNext: params.page < totalPages,
        hasPrev: params.page > 1,
      },
    };
  }

  async incrementDownloadCount(fileId: string): Promise<void> {
    const { error } = await this.supabase
      .rpc('increment_download_count', { file_id: fileId });

    if (error) throw error;
  }

  // File permissions operations
  async createFilePermission(permissionData: Omit<FilePermission, 'id' | 'granted_at'>): Promise<FilePermission> {
    const { data, error } = await this.supabase
      .from('file_permissions')
      .insert(permissionData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getFilePermissions(fileId: string): Promise<FilePermission[]> {
    const { data, error } = await this.supabase
      .from('file_permissions')
      .select(`
        *,
        users!file_permissions_user_id_fkey(email, full_name)
      `)
      .eq('file_id', fileId);

    if (error) throw error;
    return data || [];
  }

  async deleteFilePermission(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('file_permissions')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Audit log operations
  async createAuditLog(logData: Omit<AuditLog, 'id' | 'created_at'>): Promise<AuditLog> {
    const { data, error } = await this.supabase
      .from('audit_logs')
      .insert(logData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getAuditLogs(params: PaginationParams): Promise<PaginatedResponse<AuditLog>> {
    let query = this.supabase
      .from('audit_logs')
      .select(`
        *,
        users!audit_logs_user_id_fkey(email, full_name)
      `, { count: 'exact' });

    if (params.filter?.userId) {
      query = query.eq('user_id', params.filter.userId);
    }

    if (params.filter?.action) {
      query = query.eq('action', params.filter.action);
    }

    if (params.filter?.resourceType) {
      query = query.eq('resource_type', params.filter.resourceType);
    }

    if (params.sortBy) {
      query = query.order(params.sortBy, { ascending: params.sortOrder === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const from = (params.page - 1) * params.limit;
    const to = from + params.limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) throw error;

    const totalPages = Math.ceil((count || 0) / params.limit);

    return {
      data: data || [],
      pagination: {
        page: params.page,
        limit: params.limit,
        total: count || 0,
        totalPages,
        hasNext: params.page < totalPages,
        hasPrev: params.page > 1,
      },
    };
  }

  // Dashboard statistics
  async getDashboardStats(): Promise<any> {
    const [filesCount, usersCount, recentUploads] = await Promise.all([
      this.supabase.from('files').select('*', { count: 'exact', head: true }),
      this.supabase.from('users').select('*', { count: 'exact', head: true }),
      this.supabase
        .from('files')
        .select('*')
        .gte('uploaded_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('uploaded_at', { ascending: false })
        .limit(10),
    ]);

    return {
      totalFiles: filesCount.count || 0,
      totalUsers: usersCount.count || 0,
      recentUploads: recentUploads.data || [],
    };
  }
}

export const databaseService = new DatabaseService();
