'use client';

import { useSearchParams, Suspense } from 'next/navigation';

function CallbackContent() {
  const searchParams = useSearchParams();
  const code = searchParams.get('code');
  const error = searchParams.get('error');

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Authorization Error</h1>
          <p className="text-gray-600">Error: {error}</p>
        </div>
      </div>
    );
  }

  if (code) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-2xl w-full bg-white p-8 rounded-lg shadow">
          <h1 className="text-2xl font-bold text-green-600 mb-4">✅ Authorization Successful!</h1>
          <div className="bg-gray-100 p-4 rounded-lg mb-4">
            <h2 className="font-semibold mb-2">Your Authorization Code:</h2>
            <code className="block bg-gray-800 text-green-400 p-3 rounded text-sm break-all">
              {code}
            </code>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">Next Steps:</h3>
            <ol className="list-decimal list-inside text-blue-700 space-y-1">
              <li>Copy the authorization code above</li>
              <li>Open your terminal in the project directory</li>
              <li>Run: <code className="bg-blue-200 px-1 rounded">node get-refresh-token.js [PASTE_CODE_HERE]</code></li>
              <li>Copy the refresh token to your .env.local file</li>
            </ol>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Processing authorization...</p>
      </div>
    </div>
  );
}

export default function OAuthCallback() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CallbackContent />
    </Suspense>
  );
}
