# Setup Guide

This guide will walk you through setting up the Document Repository application from scratch.

## Prerequisites

Before you begin, make sure you have:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Git** - [Download here](https://git-scm.com/)
- **A code editor** - VS Code recommended
- **A Supabase account** - [Sign up here](https://supabase.com)
- **A Google Cloud account** - [Sign up here](https://cloud.google.com)

## Step 1: Clone and Install

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd document-repository
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Copy environment file**
   ```bash
   cp .env.local.example .env.local
   ```

## Step 2: Supabase Setup

### Create a New Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: Document Repository
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
5. Click "Create new project"

### Get API Keys

1. Go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://abcdefgh.supabase.co`)
   - **anon public** key
   - **service_role** key (keep this secret!)

### Set Up Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Create a new query
3. Copy the entire contents of `supabase-schema.sql` from the project
4. Run the query to create all tables and functions

### Configure Authentication

1. Go to **Authentication** > **Settings**
2. Configure **Site URL**: `http://localhost:3000` (for development)
3. Add **Redirect URLs**: `http://localhost:3000/api/auth/callback`
4. Enable **Email confirmations** if desired
5. Configure **OAuth providers** (optional):
   - Enable Google OAuth if you want social login
   - Add your Google OAuth credentials

## Step 3: Google Drive API Setup

### Enable the API

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select an existing one
3. Enable the **Google Drive API**:
   - Go to **APIs & Services** > **Library**
   - Search for "Google Drive API"
   - Click on it and press "Enable"

### Create OAuth Credentials

1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth client ID**
3. Configure the OAuth consent screen first if prompted:
   - Choose **External** user type
   - Fill in required fields:
     - **App name**: Document Repository
     - **User support email**: Your email
     - **Developer contact**: Your email
   - Add scopes: `https://www.googleapis.com/auth/drive.file`
4. Create OAuth client ID:
   - **Application type**: Web application
   - **Name**: Document Repository
   - **Authorized redirect URIs**: 
     - `http://localhost:3000/api/auth/callback` (development)
     - `https://your-domain.com/api/auth/callback` (production)

### Get Refresh Token

You'll need to get a refresh token for server-side access:

1. **Create a test script** (`get-refresh-token.js`):
   ```javascript
   const { google } = require('googleapis');
   
   const oauth2Client = new google.auth.OAuth2(
     'YOUR_CLIENT_ID',
     'YOUR_CLIENT_SECRET',
     'http://localhost:3000/api/auth/callback'
   );
   
   const scopes = ['https://www.googleapis.com/auth/drive.file'];
   const url = oauth2Client.generateAuthUrl({
     access_type: 'offline',
     scope: scopes,
   });
   
   console.log('Visit this URL:', url);
   ```

2. **Run the script** and visit the URL
3. **Authorize the application** and copy the code from the callback URL
4. **Exchange the code for tokens**:
   ```javascript
   oauth2Client.getToken('AUTHORIZATION_CODE', (err, token) => {
     if (err) return console.error('Error:', err);
     console.log('Refresh token:', token.refresh_token);
   });
   ```

### Create Drive Folder

1. Create a folder in Google Drive for storing uploaded files
2. Copy the folder ID from the URL (e.g., `1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms`)
3. Make sure the folder is accessible by your service account

## Step 4: Environment Configuration

Edit your `.env.local` file with the values you collected:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Google Drive API Configuration
GOOGLE_DRIVE_CLIENT_ID=your_client_id_here
GOOGLE_DRIVE_CLIENT_SECRET=your_client_secret_here
GOOGLE_DRIVE_REDIRECT_URI=http://localhost:3000/api/auth/callback
GOOGLE_DRIVE_REFRESH_TOKEN=your_refresh_token_here
GOOGLE_DRIVE_FOLDER_ID=your_folder_id_here

# Application Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_random_secret_here

# File Upload Configuration (optional)
MAX_FILE_SIZE=********  # 10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif,zip,rar

# Security Configuration (optional)
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000
```

### Generate Secrets

For `NEXTAUTH_SECRET`, generate a random string:
```bash
openssl rand -base64 32
```

## Step 5: Test the Setup

1. **Start the development server**
   ```bash
   npm run dev
   ```

2. **Open your browser** to `http://localhost:3000`

3. **Test user registration**:
   - Click "Get Started" or "Sign Up"
   - Create a new account
   - Check your email for verification (if enabled)

4. **Test file upload**:
   - Sign in to your account
   - Go to the Upload page
   - Try uploading a small file
   - Check that it appears in your Google Drive folder

5. **Test admin functionality**:
   - Manually set your user role to 'admin' in Supabase:
     ```sql
     UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
     ```
   - Refresh the page and check for admin panel access

## Step 6: Troubleshooting

### Common Issues

**1. Supabase Connection Error**
- Check your project URL and API keys
- Ensure your IP is not blocked by Supabase
- Verify the database schema was applied correctly

**2. Google Drive API Error**
- Check your OAuth credentials
- Verify the redirect URI matches exactly
- Ensure the Drive API is enabled
- Check your refresh token is valid

**3. File Upload Fails**
- Check file size limits
- Verify allowed file types
- Check Google Drive folder permissions
- Look at browser console for errors

**4. Authentication Issues**
- Check Supabase auth settings
- Verify redirect URLs are correct
- Check for CORS issues

### Debug Mode

Enable debug logging by adding to your `.env.local`:
```env
NODE_ENV=development
DEBUG=true
```

### Check Logs

- **Browser Console**: For client-side errors
- **Terminal**: For server-side errors
- **Supabase Dashboard**: For database errors
- **Google Cloud Console**: For API errors

## Step 7: Development Workflow

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm run test:coverage
```

### Code Quality

```bash
# Run linting
npm run lint

# Fix linting issues
npm run lint -- --fix
```

### Database Changes

When making database changes:

1. Update `supabase-schema.sql`
2. Apply changes in Supabase SQL Editor
3. Test thoroughly
4. Update TypeScript types if needed

### Adding New Features

1. Create feature branch: `git checkout -b feature-name`
2. Make your changes
3. Add tests for new functionality
4. Update documentation if needed
5. Submit pull request

## Step 8: Production Preparation

Before deploying to production:

1. **Update environment variables** for production
2. **Test the build process**: `npm run build`
3. **Run all tests**: `npm run test`
4. **Update Supabase settings** for production domain
5. **Update Google OAuth settings** for production domain
6. **Set up monitoring and error tracking**
7. **Configure backup strategies**

## Getting Help

If you encounter issues:

1. **Check the logs** in your terminal and browser console
2. **Review the troubleshooting section** above
3. **Check the GitHub issues** for similar problems
4. **Create a new issue** with detailed error information

## Next Steps

Once your setup is complete:

1. **Customize the UI** to match your brand
2. **Configure additional security settings**
3. **Set up monitoring and analytics**
4. **Plan your deployment strategy**
5. **Create user documentation**

## Security Checklist

Before going live:

- [ ] All secrets are properly configured
- [ ] Database has proper access controls
- [ ] File upload limits are appropriate
- [ ] Rate limiting is configured
- [ ] HTTPS is enabled (handled by deployment platform)
- [ ] Error messages don't leak sensitive information
- [ ] Audit logging is working
- [ ] Backup procedures are in place

Congratulations! Your Document Repository should now be up and running. 🎉
