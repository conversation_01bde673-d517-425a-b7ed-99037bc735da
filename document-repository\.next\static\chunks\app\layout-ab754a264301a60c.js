(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{24:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(2115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', '<PERSON>eist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},2099:(e,t,r)=>{"use strict";r.d(t,{i7:()=>i});var a=r(822),s=r(9509);let n=s.env.NEXT_PUBLIC_SUPABASE_URL,l=s.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;s.env.SUPABASE_SERVICE_ROLE_KEY;let i=()=>(0,a.createBrowserClient)(n,l)},5017:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2093,23)),Promise.resolve().then(r.t.bind(r,7735,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,6916))},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6916:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var a=r(5155),s=r(2115),n=r(6874),l=r.n(n),i=r(5695),o=r(9064),c=r(7338);let d=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),m=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}),u=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))});var h=r(24);let x=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});var f=r(2099);function g(){let[e,t]=(0,s.useState)(null),[r,n]=(0,s.useState)(!0),[g,v]=(0,s.useState)(!1),b=(0,i.useRouter)(),p=(0,i.usePathname)(),w=(0,f.i7)();(0,s.useEffect)(()=>{j()},[]);let j=async()=>{try{let{data:{user:e}}=await w.auth.getUser();if(e){let r=await fetch("/api/users/".concat(e.id));if(r.ok){let e=await r.json();t(e.data)}}}catch(e){console.error("Error loading user:",e)}finally{n(!1)}},y=async()=>{try{await fetch("/api/auth/signout",{method:"POST"}),await w.auth.signOut(),b.push("/auth/login")}catch(e){console.error("Error signing out:",e)}},N=[{name:"Dashboard",href:"/dashboard",icon:o.A},{name:"Upload",href:"/upload",icon:c.A},...(null==e?void 0:e.role)==="admin"?[{name:"Admin Panel",href:"/admin",icon:d}]:[]];return r?(0,a.jsx)("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-between h-16",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("div",{className:"animate-pulse bg-gray-300 h-8 w-32 rounded"})})})})}):(null==p?void 0:p.startsWith("/auth/"))?null:(0,a.jsxs)("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)(l(),{href:"/dashboard",className:"text-xl font-bold text-indigo-600",children:"DocRepository"})}),(0,a.jsx)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:N.map(e=>{let t=p===e.href;return(0,a.jsxs)(l(),{href:e.href,className:"inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ".concat(t?"border-indigo-500 text-gray-900":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"),children:[(0,a.jsx)(e.icon,{className:"w-4 h-4 mr-2"}),e.name]},e.name)})})]}),(0,a.jsx)("div",{className:"hidden sm:ml-6 sm:flex sm:items-center",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m,{className:"w-5 h-5 text-gray-400 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:(null==e?void 0:e.full_name)||(null==e?void 0:e.email)||"User"}),(null==e?void 0:e.role)==="admin"&&(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:"Admin"})]}),(0,a.jsxs)("button",{onClick:y,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[(0,a.jsx)(u,{className:"w-4 h-4 mr-2"}),"Sign out"]})]})}),(0,a.jsx)("div",{className:"sm:hidden flex items-center",children:(0,a.jsx)("button",{onClick:()=>v(!g),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500",children:g?(0,a.jsx)(h.A,{className:"block h-6 w-6"}):(0,a.jsx)(x,{className:"block h-6 w-6"})})})]})}),g&&(0,a.jsxs)("div",{className:"sm:hidden",children:[(0,a.jsx)("div",{className:"pt-2 pb-3 space-y-1",children:N.map(e=>{let t=p===e.href;return(0,a.jsx)(l(),{href:e.href,className:"block pl-3 pr-4 py-2 border-l-4 text-base font-medium ".concat(t?"bg-indigo-50 border-indigo-500 text-indigo-700":"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700"),onClick:()=>v(!1),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(e.icon,{className:"w-5 h-5 mr-3"}),e.name]})},e.name)})}),(0,a.jsxs)("div",{className:"pt-4 pb-3 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center px-4",children:[(0,a.jsx)(m,{className:"w-8 h-8 text-gray-400"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-base font-medium text-gray-800",children:(null==e?void 0:e.full_name)||"User"}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:null==e?void 0:e.email}),(null==e?void 0:e.role)==="admin"&&(0,a.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full",children:"Admin"})]})]}),(0,a.jsx)("div",{className:"mt-3 space-y-1",children:(0,a.jsx)("button",{onClick:y,className:"block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u,{className:"w-5 h-5 mr-3"}),"Sign out"]})})})]})]})]})}},7338:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(2115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9064:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(2115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})}},e=>{e.O(0,[360,874,822,441,964,358],()=>e(e.s=5017)),_N_E=e.O()}]);