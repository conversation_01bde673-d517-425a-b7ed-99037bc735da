import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { googleDriveService } from '@/lib/google-drive';
import { databaseService } from '@/lib/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Get file record
    const fileRecord = await databaseService.getFileById(params.id);
    if (!fileRecord) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const canAccess = 
      fileRecord.is_public || 
      fileRecord.uploaded_by === user.id || 
      userProfile.role === 'admin';

    if (!canAccess) {
      // Check if user has explicit permission
      const permissions = await databaseService.getFilePermissions(fileRecord.id);
      const hasPermission = permissions.some(p => p.user_id === user.id);
      
      if (!hasPermission) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      data: fileRecord
    });

  } catch (error) {
    console.error('Get file error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to get file' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Get file record
    const fileRecord = await databaseService.getFileById(params.id);
    if (!fileRecord) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    // Check permissions (only owner or admin can update)
    const canUpdate = 
      fileRecord.uploaded_by === user.id || 
      userProfile.role === 'admin';

    if (!canUpdate) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { description, tags, is_public } = body;

    // Prepare updates
    const updates: any = {};
    if (description !== undefined) updates.description = description;
    if (tags !== undefined) updates.tags = Array.isArray(tags) ? tags : tags.split(',').map((tag: string) => tag.trim()).filter(Boolean);
    if (is_public !== undefined) {
      updates.is_public = is_public;
      
      // Update Google Drive permissions
      try {
        await googleDriveService.updateFilePermissions(fileRecord.google_drive_id, is_public);
      } catch (driveError) {
        console.error('Failed to update Google Drive permissions:', driveError);
        // Continue with database update even if Drive update fails
      }
    }

    // Update file record
    const updatedFile = await databaseService.updateFile(params.id, updates);

    // Log audit event
    await databaseService.createAuditLog({
      user_id: user.id,
      action: 'update',
      resource_type: 'file',
      resource_id: fileRecord.id,
      details: {
        filename: fileRecord.original_filename,
        updates
      },
      ip_address: request.ip || null,
      user_agent: request.headers.get('user-agent') || null
    });

    return NextResponse.json({
      success: true,
      data: updatedFile,
      message: 'File updated successfully'
    });

  } catch (error) {
    console.error('Update file error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update file' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Get file record
    const fileRecord = await databaseService.getFileById(params.id);
    if (!fileRecord) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    // Check permissions (only owner or admin can delete)
    const canDelete = 
      fileRecord.uploaded_by === user.id || 
      userProfile.role === 'admin';

    if (!canDelete) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    try {
      // Delete from Google Drive
      await googleDriveService.deleteFile(fileRecord.google_drive_id);
    } catch (driveError) {
      console.error('Failed to delete from Google Drive:', driveError);
      // Continue with database deletion even if Drive deletion fails
    }

    // Delete from database
    await databaseService.deleteFile(params.id);

    // Log audit event
    await databaseService.createAuditLog({
      user_id: user.id,
      action: 'delete',
      resource_type: 'file',
      resource_id: fileRecord.id,
      details: {
        filename: fileRecord.original_filename,
        file_size: fileRecord.file_size
      },
      ip_address: request.ip || null,
      user_agent: request.headers.get('user-agent') || null
    });

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('Delete file error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to delete file' 
      },
      { status: 500 }
    );
  }
}
