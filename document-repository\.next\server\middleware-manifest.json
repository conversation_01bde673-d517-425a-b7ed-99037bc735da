{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bu9X+oy2JcssisSfj68NiK5/9zhWdAlozmhsdcA/F8E=", "__NEXT_PREVIEW_MODE_ID": "b4a915bf0e45f982543bc294ab346984", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6b0036f97e32b4c43ec14fdb9509f67ac09a7991ed5b21efb0be85218b8d77c9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0e27518bb2da240182c57de731f8c7e9ee1b2c95a211b9c5927497dda497a996"}}}, "functions": {}, "sortedMiddleware": ["/"]}