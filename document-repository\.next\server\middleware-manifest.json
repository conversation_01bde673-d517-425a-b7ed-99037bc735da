{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bu9X+oy2JcssisSfj68NiK5/9zhWdAlozmhsdcA/F8E=", "__NEXT_PREVIEW_MODE_ID": "4430ced63e932334c04641f426f9c4ec", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0f5c48375a417d390c66d41a6b7064ba66153fc70c78a2d3389122e56f21fa1b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "db4d23829714230aa64fbcee7d350ed1f1f7db9fb1af6d2260fc27b0951cde22"}}}, "functions": {}, "sortedMiddleware": ["/"]}