import '@testing-library/jest-dom'

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
process.env.GOOGLE_DRIVE_CLIENT_ID = 'test-client-id'
process.env.GOOGLE_DRIVE_CLIENT_SECRET = 'test-client-secret'
process.env.GOOGLE_DRIVE_REDIRECT_URI = 'http://localhost:3000/api/auth/callback'
process.env.GOOGLE_DRIVE_REFRESH_TOKEN = 'test-refresh-token'
process.env.GOOGLE_DRIVE_FOLDER_ID = 'test-folder-id'

// Mock fetch globally
global.fetch = jest.fn()

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
  usePathname: () => '/test-path',
}))

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  createClientComponentClient: () => ({
    auth: {
      getUser: jest.fn(),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
      signInWithOAuth: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(),
          })),
        })),
      })),
      delete: jest.fn(() => ({
        eq: jest.fn(),
      })),
    })),
  }),
  createServerComponentClient: () => ({
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
    })),
  }),
}))

// Mock Google Drive service
jest.mock('@/lib/google-drive', () => ({
  googleDriveService: {
    uploadFile: jest.fn(),
    downloadFile: jest.fn(),
    deleteFile: jest.fn(),
    getFileMetadata: jest.fn(),
    generateDirectDownloadLink: jest.fn(),
  },
}))

// Mock database service
jest.mock('@/lib/database', () => ({
  databaseService: {
    createUser: jest.fn(),
    getUserById: jest.fn(),
    getUserByEmail: jest.fn(),
    updateUser: jest.fn(),
    getAllUsers: jest.fn(),
    createFile: jest.fn(),
    getFileById: jest.fn(),
    updateFile: jest.fn(),
    deleteFile: jest.fn(),
    getFilesByUser: jest.fn(),
    getAllFiles: jest.fn(),
    createAuditLog: jest.fn(),
    getDashboardStats: jest.fn(),
  },
}))

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks()
})
