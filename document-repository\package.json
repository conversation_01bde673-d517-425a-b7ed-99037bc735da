{"name": "document-repository", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.2", "@supabase/supabase-js": "^2.52.0", "@supabase/ssr": "^0.6.1", "googleapis": "^153.0.0", "multer": "^2.0.2", "lucide-react": "^0.525.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "react-dropzone": "^14.3.8", "date-fns": "^4.1.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.2", "@eslint/eslintrc": "^3", "jest": "^30.0.4", "@testing-library/react": "^16.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "jest-environment-jsdom": "^30.0.4", "@types/jest": "^30.0.0", "@types/multer": "^2.0.0", "@types/react-dropzone": "^4.2.2"}}