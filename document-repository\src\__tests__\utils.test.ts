import {
  formatFileSize,
  formatDate,
  getFileIcon,
  validateFileType,
  validateFileSize,
  generateUniqueFileName,
  sanitizeFileName,
  extractFileExtension,
  getMimeTypeFromExtension,
  debounce,
  throttle,
  isValidEmail,
  truncateText,
  capitalizeFirstLetter,
} from '@/lib/utils';

describe('Utils', () => {
  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });
  });

  describe('formatDate', () => {
    it('should format date string correctly', () => {
      const dateString = '2023-12-25T10:30:00Z';
      const formatted = formatDate(dateString);
      expect(formatted).toMatch(/Dec 25, 2023/);
    });
  });

  describe('getFileIcon', () => {
    it('should return correct icons for different mime types', () => {
      expect(getFileIcon('image/jpeg')).toBe('🖼️');
      expect(getFileIcon('video/mp4')).toBe('🎥');
      expect(getFileIcon('audio/mp3')).toBe('🎵');
      expect(getFileIcon('application/pdf')).toBe('📄');
      expect(getFileIcon('application/unknown')).toBe('📁');
    });
  });

  describe('validateFileType', () => {
    it('should validate file types correctly', () => {
      const pdfFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      const exeFile = new File(['content'], 'test.exe', { type: 'application/exe' });
      
      expect(validateFileType(pdfFile, ['pdf', 'doc'])).toBe(true);
      expect(validateFileType(exeFile, ['pdf', 'doc'])).toBe(false);
    });
  });

  describe('validateFileSize', () => {
    it('should validate file sizes correctly', () => {
      const smallFile = new File(['small'], 'small.txt');
      const largeFile = new File([new ArrayBuffer(10 * 1024 * 1024)], 'large.txt');
      
      expect(validateFileSize(smallFile, 1024 * 1024)).toBe(true);
      expect(validateFileSize(largeFile, 1024 * 1024)).toBe(false);
    });
  });

  describe('generateUniqueFileName', () => {
    it('should generate unique filenames', () => {
      const original = 'test.pdf';
      const unique1 = generateUniqueFileName(original);
      const unique2 = generateUniqueFileName(original);
      
      expect(unique1).not.toBe(unique2);
      expect(unique1).toMatch(/test_\d+_[a-z0-9]+\.pdf/);
    });
  });

  describe('sanitizeFileName', () => {
    it('should sanitize filenames correctly', () => {
      expect(sanitizeFileName('Test File.pdf')).toBe('test_file.pdf');
      expect(sanitizeFileName('file<>:"/\\|?*.txt')).toBe('file_________.txt');
    });
  });

  describe('extractFileExtension', () => {
    it('should extract file extensions correctly', () => {
      expect(extractFileExtension('test.pdf')).toBe('pdf');
      expect(extractFileExtension('file.name.with.dots.txt')).toBe('txt');
      expect(extractFileExtension('noextension')).toBe('');
    });
  });

  describe('getMimeTypeFromExtension', () => {
    it('should return correct MIME types', () => {
      expect(getMimeTypeFromExtension('pdf')).toBe('application/pdf');
      expect(getMimeTypeFromExtension('jpg')).toBe('image/jpeg');
      expect(getMimeTypeFromExtension('unknown')).toBe('application/octet-stream');
    });
  });

  describe('debounce', () => {
    jest.useFakeTimers();
    
    it('should debounce function calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 100);
      
      debouncedFn();
      debouncedFn();
      debouncedFn();
      
      expect(mockFn).not.toHaveBeenCalled();
      
      jest.advanceTimersByTime(100);
      
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
    
    afterEach(() => {
      jest.clearAllTimers();
    });
  });

  describe('throttle', () => {
    jest.useFakeTimers();
    
    it('should throttle function calls', () => {
      const mockFn = jest.fn();
      const throttledFn = throttle(mockFn, 100);
      
      throttledFn();
      throttledFn();
      throttledFn();
      
      expect(mockFn).toHaveBeenCalledTimes(1);
      
      jest.advanceTimersByTime(100);
      
      throttledFn();
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
    
    afterEach(() => {
      jest.clearAllTimers();
    });
  });

  describe('isValidEmail', () => {
    it('should validate email addresses correctly', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
    });
  });

  describe('truncateText', () => {
    it('should truncate text correctly', () => {
      expect(truncateText('Hello World', 5)).toBe('Hello...');
      expect(truncateText('Short', 10)).toBe('Short');
    });
  });

  describe('capitalizeFirstLetter', () => {
    it('should capitalize first letter', () => {
      expect(capitalizeFirstLetter('hello')).toBe('Hello');
      expect(capitalizeFirstLetter('WORLD')).toBe('WORLD');
      expect(capitalizeFirstLetter('')).toBe('');
    });
  });
});
