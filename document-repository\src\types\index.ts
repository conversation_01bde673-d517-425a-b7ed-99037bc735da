export interface User {
  id: string;
  email: string;
  full_name?: string;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

export interface FileRecord {
  id: string;
  filename: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  google_drive_id: string;
  google_drive_url: string;
  uploaded_by: string;
  uploaded_at: string;
  updated_at: string;
  description?: string;
  tags?: string[];
  is_public: boolean;
  download_count: number;
  status: 'uploading' | 'completed' | 'failed' | 'deleted';
}

export interface FileUploadProgress {
  id: string;
  filename: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface FilePermission {
  id: string;
  file_id: string;
  user_id: string;
  permission_type: 'read' | 'write' | 'admin';
  granted_by: string;
  granted_at: string;
}

export interface AuditLog {
  id: string;
  user_id: string;
  action: 'upload' | 'download' | 'delete' | 'share' | 'update';
  resource_type: 'file' | 'user' | 'permission';
  resource_id: string;
  details?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface GoogleDriveConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  refreshToken: string;
  folderId: string;
}

export interface UploadConfig {
  maxFileSize: number;
  allowedFileTypes: string[];
  maxFilesPerUpload: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  filter?: Record<string, any>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface FileFilters {
  fileType?: string;
  uploadedBy?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  tags?: string[];
  isPublic?: boolean;
}

export interface DashboardStats {
  totalFiles: number;
  totalUsers: number;
  totalStorage: number;
  recentUploads: number;
  popularFiles: FileRecord[];
  storageByType: Record<string, number>;
}
