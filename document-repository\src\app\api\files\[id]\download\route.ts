import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { googleDriveService } from '@/lib/google-drive';
import { databaseService } from '@/lib/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Get file record
    const fileRecord = await databaseService.getFileById(params.id);
    if (!fileRecord) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const canAccess = 
      fileRecord.is_public || 
      fileRecord.uploaded_by === user.id || 
      userProfile.role === 'admin';

    if (!canAccess) {
      // Check if user has explicit permission
      const permissions = await databaseService.getFilePermissions(fileRecord.id);
      const hasPermission = permissions.some(p => p.user_id === user.id);
      
      if (!hasPermission) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        );
      }
    }

    // Check if file is available
    if (fileRecord.status !== 'completed') {
      return NextResponse.json(
        { success: false, error: 'File is not available for download' },
        { status: 400 }
      );
    }

    try {
      // Get download URL from Google Drive
      const downloadUrl = googleDriveService.generateDirectDownloadLink(fileRecord.google_drive_id);

      // Increment download count
      await databaseService.incrementDownloadCount(fileRecord.id);

      // Log audit event
      await databaseService.createAuditLog({
        user_id: user.id,
        action: 'download',
        resource_type: 'file',
        resource_id: fileRecord.id,
        details: {
          filename: fileRecord.original_filename,
          file_size: fileRecord.file_size
        },
        ip_address: request.ip || null,
        user_agent: request.headers.get('user-agent') || null
      });

      // Return download URL
      return NextResponse.json({
        success: true,
        data: {
          downloadUrl,
          filename: fileRecord.original_filename,
          fileSize: fileRecord.file_size,
          mimeType: fileRecord.mime_type
        }
      });

    } catch (driveError) {
      console.error('Google Drive download error:', driveError);
      return NextResponse.json(
        { success: false, error: 'Failed to generate download link' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('File download error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to download file' 
      },
      { status: 500 }
    );
  }
}

// Alternative direct download endpoint
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Get file record
    const fileRecord = await databaseService.getFileById(params.id);
    if (!fileRecord) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const canAccess = 
      fileRecord.is_public || 
      fileRecord.uploaded_by === user.id || 
      userProfile.role === 'admin';

    if (!canAccess) {
      // Check if user has explicit permission
      const permissions = await databaseService.getFilePermissions(fileRecord.id);
      const hasPermission = permissions.some(p => p.user_id === user.id);
      
      if (!hasPermission) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        );
      }
    }

    // Check if file is available
    if (fileRecord.status !== 'completed') {
      return NextResponse.json(
        { success: false, error: 'File is not available for download' },
        { status: 400 }
      );
    }

    try {
      // Download file from Google Drive
      const fileBuffer = await googleDriveService.downloadFile(fileRecord.google_drive_id);

      // Increment download count
      await databaseService.incrementDownloadCount(fileRecord.id);

      // Log audit event
      await databaseService.createAuditLog({
        user_id: user.id,
        action: 'download',
        resource_type: 'file',
        resource_id: fileRecord.id,
        details: {
          filename: fileRecord.original_filename,
          file_size: fileRecord.file_size
        },
        ip_address: request.ip || null,
        user_agent: request.headers.get('user-agent') || null
      });

      // Return file as response
      return new NextResponse(fileBuffer, {
        status: 200,
        headers: {
          'Content-Type': fileRecord.mime_type,
          'Content-Disposition': `attachment; filename="${fileRecord.original_filename}"`,
          'Content-Length': fileRecord.file_size.toString(),
        },
      });

    } catch (driveError) {
      console.error('Google Drive download error:', driveError);
      return NextResponse.json(
        { success: false, error: 'Failed to download file from Google Drive' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('File download error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to download file' 
      },
      { status: 500 }
    );
  }
}
