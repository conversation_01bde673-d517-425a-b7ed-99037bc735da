import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { databaseService } from '@/lib/database';
import { parseSearchParams } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile and check admin role
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile || userProfile.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const params = parseSearchParams(searchParams);
    
    const paginationParams = {
      page: params.page || 1,
      limit: params.limit || 20,
      search: params.search,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder || 'desc',
    };

    // Get all users
    const users = await databaseService.getAllUsers(paginationParams);

    return NextResponse.json({
      success: true,
      data: users.data,
      pagination: users.pagination
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to get users' 
      },
      { status: 500 }
    );
  }
}
