import {
  validateFileType,
  validateFileSize,
  validateFileName,
  validateFileContent,
  checkRateLimit,
  generateCSRFToken,
  validateCSRFToken,
  sanitizeInput,
  sanitizeFileName,
  getClientIP,
  generateFileHash,
  scanFileForViruses,
  validateFile,
  ALLOWED_MIME_TYPES,
  DANGEROUS_EXTENSIONS,
  MAX_FILE_SIZE,
} from '@/lib/security';
import { NextRequest } from 'next/server';

describe('Security', () => {
  describe('validateFileType', () => {
    it('should validate allowed file types', () => {
      const pdfFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      const result = validateFileType(pdfFile);
      expect(result.valid).toBe(true);
    });

    it('should reject dangerous file types', () => {
      const exeFile = new File(['content'], 'malware.exe', { type: 'application/exe' });
      const result = validateFileType(exeFile);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('not allowed for security reasons');
    });

    it('should reject disallowed MIME types', () => {
      const unknownFile = new File(['content'], 'test.unknown', { type: 'application/unknown' });
      const result = validateFileType(unknownFile);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('MIME type');
    });
  });

  describe('validateFileSize', () => {
    it('should validate file size within limits', () => {
      const smallFile = new File(['small content'], 'small.txt');
      const result = validateFileSize(smallFile);
      expect(result.valid).toBe(true);
    });

    it('should reject files that are too large', () => {
      // Create a mock file that appears large
      const largeFile = {
        name: 'large.txt',
        size: MAX_FILE_SIZE + 1,
        type: 'text/plain'
      } as File;
      
      const result = validateFileSize(largeFile);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('exceeds maximum limit');
    });
  });

  describe('validateFileName', () => {
    it('should validate normal filenames', () => {
      const result = validateFileName('document.pdf');
      expect(result.valid).toBe(true);
    });

    it('should reject filenames with dangerous characters', () => {
      const result = validateFileName('file\0name.txt');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('invalid characters');
    });

    it('should reject filenames with path traversal', () => {
      const result = validateFileName('../../../etc/passwd');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('invalid characters');
    });

    it('should reject empty filenames', () => {
      const result = validateFileName('   ');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('cannot be empty');
    });

    it('should reject filenames that are too long', () => {
      const longName = 'a'.repeat(256) + '.txt';
      const result = validateFileName(longName);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('too long');
    });
  });

  describe('validateFileContent', () => {
    it('should validate PDF file content', async () => {
      // Create a mock PDF file with correct magic number
      const pdfContent = new Uint8Array([0x25, 0x50, 0x44, 0x46, 0x2D]); // %PDF-
      const pdfFile = new File([pdfContent], 'test.pdf', { type: 'application/pdf' });
      
      const result = await validateFileContent(pdfFile);
      expect(result.valid).toBe(true);
    });

    it('should reject files with mismatched content', async () => {
      // Create a file claiming to be PDF but with wrong content
      const fakeContent = new Uint8Array([0x00, 0x01, 0x02, 0x03]);
      const fakeFile = new File([fakeContent], 'fake.pdf', { type: 'application/pdf' });
      
      const result = await validateFileContent(fakeFile);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('does not match');
    });
  });

  describe('checkRateLimit', () => {
    beforeEach(() => {
      // Clear any existing rate limit data
      jest.clearAllMocks();
    });

    it('should allow requests within rate limit', () => {
      const result = checkRateLimit('test-user', 10, 60000);
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(9);
    });

    it('should block requests exceeding rate limit', () => {
      // Make multiple requests to exceed limit
      for (let i = 0; i < 10; i++) {
        checkRateLimit('test-user-2', 10, 60000);
      }
      
      const result = checkRateLimit('test-user-2', 10, 60000);
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });
  });

  describe('CSRF protection', () => {
    it('should generate CSRF tokens', () => {
      const token = generateCSRFToken();
      expect(token).toHaveLength(64); // 32 bytes = 64 hex chars
      expect(token).toMatch(/^[a-f0-9]+$/);
    });

    it('should validate CSRF tokens correctly', () => {
      const token = generateCSRFToken();
      expect(validateCSRFToken(token, token)).toBe(true);
      expect(validateCSRFToken(token, 'different-token')).toBe(false);
    });
  });

  describe('sanitizeInput', () => {
    it('should sanitize dangerous input', () => {
      const dangerous = '<script>alert("xss")</script>';
      const sanitized = sanitizeInput(dangerous);
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('</script>');
    });

    it('should remove quotes and trim whitespace', () => {
      const input = '  "test\'s input"  ';
      const sanitized = sanitizeInput(input);
      expect(sanitized).toBe('tests input');
    });
  });

  describe('sanitizeFileName', () => {
    it('should sanitize dangerous filename characters', () => {
      const dangerous = 'file<>:"/\\|?*.txt';
      const sanitized = sanitizeFileName(dangerous);
      expect(sanitized).toBe('file_________.txt');
    });

    it('should handle spaces and multiple underscores', () => {
      const spaced = 'My   Document   File.pdf';
      const sanitized = sanitizeFileName(spaced);
      expect(sanitized).toBe('my_document_file.pdf');
    });
  });

  describe('getClientIP', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const request = {
        headers: {
          get: (name: string) => {
            if (name === 'x-forwarded-for') return '***********, ********';
            return null;
          }
        }
      } as NextRequest;
      
      const ip = getClientIP(request);
      expect(ip).toBe('***********');
    });

    it('should fallback to default IP', () => {
      const request = {
        headers: {
          get: () => null
        }
      } as NextRequest;
      
      const ip = getClientIP(request);
      expect(ip).toBe('127.0.0.1');
    });
  });

  describe('generateFileHash', () => {
    it('should generate consistent hashes', async () => {
      const content = new TextEncoder().encode('test content');
      const hash1 = await generateFileHash(content.buffer);
      const hash2 = await generateFileHash(content.buffer);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64); // SHA-256 = 64 hex chars
    });
  });

  describe('scanFileForViruses', () => {
    it('should detect EICAR test file', async () => {
      const eicarString = 'X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*';
      const eicarContent = new TextEncoder().encode(eicarString);
      
      const result = await scanFileForViruses(eicarContent.buffer);
      expect(result.clean).toBe(false);
      expect(result.threat).toContain('EICAR');
    });

    it('should pass clean files', async () => {
      const cleanContent = new TextEncoder().encode('This is a clean file');
      
      const result = await scanFileForViruses(cleanContent.buffer);
      expect(result.clean).toBe(true);
    });
  });

  describe('validateFile', () => {
    it('should validate a good file', async () => {
      const goodFile = new File(['clean content'], 'document.txt', { type: 'text/plain' });
      
      const result = await validateFile(goodFile);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should collect multiple validation errors', async () => {
      // Create a file with multiple issues
      const badFile = new File(['content'], 'bad<>file.exe', { type: 'application/exe' });
      
      const result = await validateFile(badFile);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});
