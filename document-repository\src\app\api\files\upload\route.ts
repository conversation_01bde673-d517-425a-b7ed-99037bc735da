import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { googleDriveService } from '@/lib/google-drive';
import { databaseService } from '@/lib/database';
import { 
  validateFileType, 
  validateFileSize, 
  generateUniqueFileName, 
  sanitizeFileName,
  getMimeTypeFromExtension,
  extractFileExtension 
} from '@/lib/utils';

const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB
const ALLOWED_FILE_TYPES = (process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,txt,jpg,jpeg,png,gif,zip,rar').split(',');

export async function POST(request: NextRequest) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const description = formData.get('description') as string;
    const tags = formData.get('tags') as string;
    const isPublic = formData.get('isPublic') === 'true';

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!validateFileType(file, ALLOWED_FILE_TYPES)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `File type not allowed. Allowed types: ${ALLOWED_FILE_TYPES.join(', ')}` 
        },
        { status: 400 }
      );
    }

    // Validate file size
    if (!validateFileSize(file, MAX_FILE_SIZE)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `File size too large. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB` 
        },
        { status: 400 }
      );
    }

    // Generate unique filename
    const sanitizedOriginalName = sanitizeFileName(file.name);
    const uniqueFileName = generateUniqueFileName(sanitizedOriginalName);
    const fileExtension = extractFileExtension(file.name);
    const mimeType = file.type || getMimeTypeFromExtension(fileExtension);

    // Convert file to buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // Upload to Google Drive
    const driveResult = await googleDriveService.uploadFile(
      fileBuffer,
      uniqueFileName,
      mimeType
    );

    // Create file record in database
    const fileRecord = await databaseService.createFile({
      filename: uniqueFileName,
      original_filename: file.name,
      file_size: file.size,
      file_type: fileExtension,
      mime_type: mimeType,
      google_drive_id: driveResult.id,
      google_drive_url: driveResult.webViewLink,
      uploaded_by: user.id,
      description: description || null,
      tags: tags ? tags.split(',').map(tag => tag.trim()).filter(Boolean) : null,
      is_public: isPublic,
      download_count: 0,
      status: 'completed'
    });

    // Log audit event
    await databaseService.createAuditLog({
      user_id: user.id,
      action: 'upload',
      resource_type: 'file',
      resource_id: fileRecord.id,
      details: {
        filename: file.name,
        file_size: file.size,
        file_type: fileExtension,
        is_public: isPublic
      },
      ip_address: request.ip || null,
      user_agent: request.headers.get('user-agent') || null
    });

    return NextResponse.json({
      success: true,
      data: fileRecord,
      message: 'File uploaded successfully'
    });

  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to upload file' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const response = new NextResponse();
    const supabase = createRouteHandlerClient(request, response);

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const userProfile = await databaseService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || undefined;
    const sortBy = searchParams.get('sortBy') || undefined;
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';
    const fileType = searchParams.get('fileType') || undefined;
    const isPublic = searchParams.get('isPublic') ? searchParams.get('isPublic') === 'true' : undefined;
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || undefined;

    // Get files based on user role
    let files;
    if (userProfile.role === 'admin') {
      files = await databaseService.getAllFiles({
        page,
        limit,
        search,
        sortBy,
        sortOrder,
        filter: {
          fileType,
          isPublic,
          tags
        }
      });
    } else {
      files = await databaseService.getFilesByUser(user.id, {
        page,
        limit,
        search,
        sortBy,
        sortOrder,
        filter: {
          fileType,
          isPublic,
          tags
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: files.data,
      pagination: files.pagination
    });

  } catch (error) {
    console.error('Get files error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to get files' 
      },
      { status: 500 }
    );
  }
}
