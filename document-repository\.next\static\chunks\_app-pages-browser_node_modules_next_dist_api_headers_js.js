"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_next_dist_api_headers_js"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/headers.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/api/headers.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __esModule: () => (/* reexport safe */ _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__.__esModule),\n/* harmony export */   cookies: () => (/* reexport safe */ _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__.cookies),\n/* harmony export */   draftMode: () => (/* reexport safe */ _server_request_draft_mode__WEBPACK_IMPORTED_MODULE_2__.draftMode),\n/* harmony export */   headers: () => (/* reexport safe */ _server_request_headers__WEBPACK_IMPORTED_MODULE_1__.headers)\n/* harmony export */ });\n/* harmony import */ var _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../server/request/cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/request/cookies.js\");\n/* harmony import */ var _server_request_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/request/headers */ \"(app-pages-browser)/./node_modules/next/dist/server/request/headers.js\");\n/* harmony import */ var _server_request_draft_mode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/request/draft-mode */ \"(app-pages-browser)/./node_modules/next/dist/server/request/draft-mode.js\");\n\n\n\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2hlYWRlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQztBQUNBO0FBQ0c7O0FBRTdDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxuZXdcXGRvY3VtZW50LXJlcG9zaXRvcnlcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxoZWFkZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL3NlcnZlci9yZXF1ZXN0L2Nvb2tpZXMnO1xuZXhwb3J0ICogZnJvbSAnLi4vc2VydmVyL3JlcXVlc3QvaGVhZGVycyc7XG5leHBvcnQgKiBmcm9tICcuLi9zZXJ2ZXIvcmVxdWVzdC9kcmFmdC1tb2RlJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aGVhZGVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/hooks-server-context.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DynamicServerError: function() {\n        return DynamicServerError;\n    },\n    isDynamicServerError: function() {\n        return isDynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE';\nclass DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description), this.description = description, this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nfunction isDynamicServerError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err) || typeof err.digest !== 'string') {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaG9va3Mtc2VydmVyLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBRWFBLGtCQUFrQjtlQUFsQkE7O0lBUUdDLG9CQUFvQjtlQUFwQkE7OztBQVZoQixNQUFNQyxxQkFBcUI7QUFFcEIsTUFBTUYsMkJBQTJCRztJQUd0Q0MsWUFBNEJDLFdBQW1CLENBQUU7UUFDL0MsS0FBSyxDQUFFLDJCQUF3QkEsY0FBQUEsSUFBQUEsQ0FETEEsV0FBQUEsR0FBQUEsYUFBQUEsSUFBQUEsQ0FGNUJDLE1BQUFBLEdBQW9DSjtJQUlwQztBQUNGO0FBRU8sU0FBU0QscUJBQXFCTSxHQUFZO0lBQy9DLElBQ0UsT0FBT0EsUUFBUSxZQUNmQSxRQUFRLFFBQ1IsQ0FBRSxhQUFZQSxHQUFBQSxDQUFFLElBQ2hCLE9BQU9BLElBQUlELE1BQU0sS0FBSyxVQUN0QjtRQUNBLE9BQU87SUFDVDtJQUVBLE9BQU9DLElBQUlELE1BQU0sS0FBS0o7QUFDeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXGhvb2tzLXNlcnZlci1jb250ZXh0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IERZTkFNSUNfRVJST1JfQ09ERSA9ICdEWU5BTUlDX1NFUlZFUl9VU0FHRSdcblxuZXhwb3J0IGNsYXNzIER5bmFtaWNTZXJ2ZXJFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgZGlnZXN0OiB0eXBlb2YgRFlOQU1JQ19FUlJPUl9DT0RFID0gRFlOQU1JQ19FUlJPUl9DT0RFXG5cbiAgY29uc3RydWN0b3IocHVibGljIHJlYWRvbmx5IGRlc2NyaXB0aW9uOiBzdHJpbmcpIHtcbiAgICBzdXBlcihgRHluYW1pYyBzZXJ2ZXIgdXNhZ2U6ICR7ZGVzY3JpcHRpb259YClcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNEeW5hbWljU2VydmVyRXJyb3IoZXJyOiB1bmtub3duKTogZXJyIGlzIER5bmFtaWNTZXJ2ZXJFcnJvciB7XG4gIGlmIChcbiAgICB0eXBlb2YgZXJyICE9PSAnb2JqZWN0JyB8fFxuICAgIGVyciA9PT0gbnVsbCB8fFxuICAgICEoJ2RpZ2VzdCcgaW4gZXJyKSB8fFxuICAgIHR5cGVvZiBlcnIuZGlnZXN0ICE9PSAnc3RyaW5nJ1xuICApIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHJldHVybiBlcnIuZGlnZXN0ID09PSBEWU5BTUlDX0VSUk9SX0NPREVcbn1cbiJdLCJuYW1lcyI6WyJEeW5hbWljU2VydmVyRXJyb3IiLCJpc0R5bmFtaWNTZXJ2ZXJFcnJvciIsIkRZTkFNSUNfRVJST1JfQ09ERSIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJkZXNjcmlwdGlvbiIsImRpZ2VzdCIsImVyciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \*******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    StaticGenBailoutError: function() {\n        return StaticGenBailoutError;\n    },\n    isStaticGenBailoutError: function() {\n        return isStaticGenBailoutError;\n    }\n});\nconst NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT';\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args), this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nfunction isStaticGenBailoutError(error) {\n    if (typeof error !== 'object' || error === null || !('code' in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFFYUEscUJBQXFCO2VBQXJCQTs7SUFJR0MsdUJBQXVCO2VBQXZCQTs7O0FBTmhCLE1BQU1DLDBCQUEwQjtBQUV6QixNQUFNRiw4QkFBOEJHOztRQUFwQyxxQkFDV0MsSUFBQUEsR0FBT0Y7O0FBQ3pCO0FBRU8sU0FBU0Qsd0JBQ2RJLEtBQWM7SUFFZCxJQUFJLE9BQU9BLFVBQVUsWUFBWUEsVUFBVSxRQUFRLENBQUUsV0FBVUEsS0FBQUEsQ0FBSSxFQUFJO1FBQ3JFLE9BQU87SUFDVDtJQUVBLE9BQU9BLE1BQU1ELElBQUksS0FBS0Y7QUFDeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHN0YXRpYy1nZW5lcmF0aW9uLWJhaWxvdXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVQgPSAnTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVQnXG5cbmV4cG9ydCBjbGFzcyBTdGF0aWNHZW5CYWlsb3V0RXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIHB1YmxpYyByZWFkb25seSBjb2RlID0gTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzU3RhdGljR2VuQmFpbG91dEVycm9yKFxuICBlcnJvcjogdW5rbm93blxuKTogZXJyb3IgaXMgU3RhdGljR2VuQmFpbG91dEVycm9yIHtcbiAgaWYgKHR5cGVvZiBlcnJvciAhPT0gJ29iamVjdCcgfHwgZXJyb3IgPT09IG51bGwgfHwgISgnY29kZScgaW4gZXJyb3IpKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICByZXR1cm4gZXJyb3IuY29kZSA9PT0gTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVRcbn1cbiJdLCJuYW1lcyI6WyJTdGF0aWNHZW5CYWlsb3V0RXJyb3IiLCJpc1N0YXRpY0dlbkJhaWxvdXRFcnJvciIsIk5FWFRfU1RBVElDX0dFTl9CQUlMT1VUIiwiRXJyb3IiLCJjb2RlIiwiZXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  RequestCookies: () => RequestCookies,\n  ResponseCookies: () => ResponseCookies,\n  parseCookie: () => parseCookie,\n  parseSetCookie: () => parseSetCookie,\n  stringifyCookie: () => stringifyCookie\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/serialize.ts\nfunction stringifyCookie(c) {\n  var _a;\n  const attrs = [\n    \"path\" in c && c.path && `Path=${c.path}`,\n    \"expires\" in c && (c.expires || c.expires === 0) && `Expires=${(typeof c.expires === \"number\" ? new Date(c.expires) : c.expires).toUTCString()}`,\n    \"maxAge\" in c && typeof c.maxAge === \"number\" && `Max-Age=${c.maxAge}`,\n    \"domain\" in c && c.domain && `Domain=${c.domain}`,\n    \"secure\" in c && c.secure && \"Secure\",\n    \"httpOnly\" in c && c.httpOnly && \"HttpOnly\",\n    \"sameSite\" in c && c.sameSite && `SameSite=${c.sameSite}`,\n    \"partitioned\" in c && c.partitioned && \"Partitioned\",\n    \"priority\" in c && c.priority && `Priority=${c.priority}`\n  ].filter(Boolean);\n  const stringified = `${c.name}=${encodeURIComponent((_a = c.value) != null ? _a : \"\")}`;\n  return attrs.length === 0 ? stringified : `${stringified}; ${attrs.join(\"; \")}`;\n}\nfunction parseCookie(cookie) {\n  const map = /* @__PURE__ */ new Map();\n  for (const pair of cookie.split(/; */)) {\n    if (!pair)\n      continue;\n    const splitAt = pair.indexOf(\"=\");\n    if (splitAt === -1) {\n      map.set(pair, \"true\");\n      continue;\n    }\n    const [key, value] = [pair.slice(0, splitAt), pair.slice(splitAt + 1)];\n    try {\n      map.set(key, decodeURIComponent(value != null ? value : \"true\"));\n    } catch {\n    }\n  }\n  return map;\n}\nfunction parseSetCookie(setCookie) {\n  if (!setCookie) {\n    return void 0;\n  }\n  const [[name, value], ...attributes] = parseCookie(setCookie);\n  const {\n    domain,\n    expires,\n    httponly,\n    maxage,\n    path,\n    samesite,\n    secure,\n    partitioned,\n    priority\n  } = Object.fromEntries(\n    attributes.map(([key, value2]) => [\n      key.toLowerCase().replace(/-/g, \"\"),\n      value2\n    ])\n  );\n  const cookie = {\n    name,\n    value: decodeURIComponent(value),\n    domain,\n    ...expires && { expires: new Date(expires) },\n    ...httponly && { httpOnly: true },\n    ...typeof maxage === \"string\" && { maxAge: Number(maxage) },\n    path,\n    ...samesite && { sameSite: parseSameSite(samesite) },\n    ...secure && { secure: true },\n    ...priority && { priority: parsePriority(priority) },\n    ...partitioned && { partitioned: true }\n  };\n  return compact(cookie);\n}\nfunction compact(t) {\n  const newT = {};\n  for (const key in t) {\n    if (t[key]) {\n      newT[key] = t[key];\n    }\n  }\n  return newT;\n}\nvar SAME_SITE = [\"strict\", \"lax\", \"none\"];\nfunction parseSameSite(string) {\n  string = string.toLowerCase();\n  return SAME_SITE.includes(string) ? string : void 0;\n}\nvar PRIORITY = [\"low\", \"medium\", \"high\"];\nfunction parsePriority(string) {\n  string = string.toLowerCase();\n  return PRIORITY.includes(string) ? string : void 0;\n}\nfunction splitCookiesString(cookiesString) {\n  if (!cookiesString)\n    return [];\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\n// src/request-cookies.ts\nvar RequestCookies = class {\n  constructor(requestHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    this._headers = requestHeaders;\n    const header = requestHeaders.get(\"cookie\");\n    if (header) {\n      const parsed = parseCookie(header);\n      for (const [name, value] of parsed) {\n        this._parsed.set(name, { name, value });\n      }\n    }\n  }\n  [Symbol.iterator]() {\n    return this._parsed[Symbol.iterator]();\n  }\n  /**\n   * The amount of cookies received from the client\n   */\n  get size() {\n    return this._parsed.size;\n  }\n  get(...args) {\n    const name = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(name);\n  }\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed);\n    if (!args.length) {\n      return all.map(([_, value]) => value);\n    }\n    const name = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter(([n]) => n === name).map(([_, value]) => value);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  set(...args) {\n    const [name, value] = args.length === 1 ? [args[0].name, args[0].value] : args;\n    const map = this._parsed;\n    map.set(name, { name, value });\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value2]) => stringifyCookie(value2)).join(\"; \")\n    );\n    return this;\n  }\n  /**\n   * Delete the cookies matching the passed name or names in the request.\n   */\n  delete(names) {\n    const map = this._parsed;\n    const result = !Array.isArray(names) ? map.delete(names) : names.map((name) => map.delete(name));\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value]) => stringifyCookie(value)).join(\"; \")\n    );\n    return result;\n  }\n  /**\n   * Delete all the cookies in the cookies in the request.\n   */\n  clear() {\n    this.delete(Array.from(this._parsed.keys()));\n    return this;\n  }\n  /**\n   * Format the cookies in the request as a string for logging\n   */\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map((v) => `${v.name}=${encodeURIComponent(v.value)}`).join(\"; \");\n  }\n};\n\n// src/response-cookies.ts\nvar ResponseCookies = class {\n  constructor(responseHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    var _a, _b, _c;\n    this._headers = responseHeaders;\n    const setCookie = (_c = (_b = (_a = responseHeaders.getSetCookie) == null ? void 0 : _a.call(responseHeaders)) != null ? _b : responseHeaders.get(\"set-cookie\")) != null ? _c : [];\n    const cookieStrings = Array.isArray(setCookie) ? setCookie : splitCookiesString(setCookie);\n    for (const cookieString of cookieStrings) {\n      const parsed = parseSetCookie(cookieString);\n      if (parsed)\n        this._parsed.set(parsed.name, parsed);\n    }\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-get CookieStore#get} without the Promise.\n   */\n  get(...args) {\n    const key = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(key);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-getAll CookieStore#getAll} without the Promise.\n   */\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed.values());\n    if (!args.length) {\n      return all;\n    }\n    const key = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter((c) => c.name === key);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-set CookieStore#set} without the Promise.\n   */\n  set(...args) {\n    const [name, value, cookie] = args.length === 1 ? [args[0].name, args[0].value, args[0]] : args;\n    const map = this._parsed;\n    map.set(name, normalizeCookie({ name, value, ...cookie }));\n    replace(map, this._headers);\n    return this;\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-delete CookieStore#delete} without the Promise.\n   */\n  delete(...args) {\n    const [name, options] = typeof args[0] === \"string\" ? [args[0]] : [args[0].name, args[0]];\n    return this.set({ ...options, name, value: \"\", expires: /* @__PURE__ */ new Date(0) });\n  }\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map(stringifyCookie).join(\"; \");\n  }\n};\nfunction replace(bag, headers) {\n  headers.delete(\"set-cookie\");\n  for (const [, value] of bag) {\n    const serialized = stringifyCookie(value);\n    headers.append(\"set-cookie\", serialized);\n  }\n}\nfunction normalizeCookie(cookie = { name: \"\", value: \"\" }) {\n  if (typeof cookie.expires === \"number\") {\n    cookie.expires = new Date(cookie.expires);\n  }\n  if (cookie.maxAge) {\n    cookie.expires = new Date(Date.now() + cookie.maxAge * 1e3);\n  }\n  if (cookie.path === null || cookie.path === void 0) {\n    cookie.path = \"/\";\n  }\n  return cookie;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/lib/metadata/metadata-constants.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    METADATA_BOUNDARY_NAME: function() {\n        return METADATA_BOUNDARY_NAME;\n    },\n    OUTLET_BOUNDARY_NAME: function() {\n        return OUTLET_BOUNDARY_NAME;\n    },\n    VIEWPORT_BOUNDARY_NAME: function() {\n        return VIEWPORT_BOUNDARY_NAME;\n    }\n});\nconst METADATA_BOUNDARY_NAME = '__next_metadata_boundary__';\nconst VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__';\nconst OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__';\n\n//# sourceMappingURL=metadata-constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvbGliL21ldGFkYXRhL21ldGFkYXRhLWNvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLE1BQU0sQ0FJTDtBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcbmV3XFxkb2N1bWVudC1yZXBvc2l0b3J5XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGxpYlxcbWV0YWRhdGFcXG1ldGFkYXRhLWNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbjAgJiYgKG1vZHVsZS5leHBvcnRzID0ge1xuICAgIE1FVEFEQVRBX0JPVU5EQVJZX05BTUU6IG51bGwsXG4gICAgT1VUTEVUX0JPVU5EQVJZX05BTUU6IG51bGwsXG4gICAgVklFV1BPUlRfQk9VTkRBUllfTkFNRTogbnVsbFxufSk7XG5mdW5jdGlvbiBfZXhwb3J0KHRhcmdldCwgYWxsKSB7XG4gICAgZm9yKHZhciBuYW1lIGluIGFsbClPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBuYW1lLCB7XG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIGdldDogYWxsW25hbWVdXG4gICAgfSk7XG59XG5fZXhwb3J0KGV4cG9ydHMsIHtcbiAgICBNRVRBREFUQV9CT1VOREFSWV9OQU1FOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIE1FVEFEQVRBX0JPVU5EQVJZX05BTUU7XG4gICAgfSxcbiAgICBPVVRMRVRfQk9VTkRBUllfTkFNRTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBPVVRMRVRfQk9VTkRBUllfTkFNRTtcbiAgICB9LFxuICAgIFZJRVdQT1JUX0JPVU5EQVJZX05BTUU6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gVklFV1BPUlRfQk9VTkRBUllfTkFNRTtcbiAgICB9XG59KTtcbmNvbnN0IE1FVEFEQVRBX0JPVU5EQVJZX05BTUUgPSAnX19uZXh0X21ldGFkYXRhX2JvdW5kYXJ5X18nO1xuY29uc3QgVklFV1BPUlRfQk9VTkRBUllfTkFNRSA9ICdfX25leHRfdmlld3BvcnRfYm91bmRhcnlfXyc7XG5jb25zdCBPVVRMRVRfQk9VTkRBUllfTkFNRSA9ICdfX25leHRfb3V0bGV0X2JvdW5kYXJ5X18nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXRhZGF0YS1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/lib/scheduler.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    atLeastOneTask: function() {\n        return atLeastOneTask;\n    },\n    scheduleImmediate: function() {\n        return scheduleImmediate;\n    },\n    scheduleOnNextTick: function() {\n        return scheduleOnNextTick;\n    },\n    waitAtLeastOneReactRenderTask: function() {\n        return waitAtLeastOneReactRenderTask;\n    }\n});\nconst scheduleOnNextTick = (cb)=>{\n    // We use Promise.resolve().then() here so that the operation is scheduled at\n    // the end of the promise job queue, we then add it to the next process tick\n    // to ensure it's evaluated afterwards.\n    //\n    // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n    //\n    Promise.resolve().then(()=>{\n        if (false) {} else {\n            process.nextTick(cb);\n        }\n    });\n};\nconst scheduleImmediate = (cb)=>{\n    if (false) {} else {\n        setImmediate(cb);\n    }\n};\nfunction atLeastOneTask() {\n    return new Promise((resolve)=>scheduleImmediate(resolve));\n}\nfunction waitAtLeastOneReactRenderTask() {\n    if (false) {} else {\n        return new Promise((r)=>setImmediate(r));\n    }\n}\n\n//# sourceMappingURL=scheduler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvbGliL3NjaGVkdWxlci5qcyIsIm1hcHBpbmdzIjoiO0FBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixNQUFNLENBS0w7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLEtBQW1DLEVBQUUsRUFFeEMsQ0FBQztBQUNWLFlBQVksT0FBTztBQUNuQjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsUUFBUSxLQUFtQyxFQUFFLEVBRXhDLENBQUM7QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsS0FBbUMsRUFBRSxFQUV4QyxDQUFDO0FBQ047QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxuZXdcXGRvY3VtZW50LXJlcG9zaXRvcnlcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcbGliXFxzY2hlZHVsZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG4wICYmIChtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBhdExlYXN0T25lVGFzazogbnVsbCxcbiAgICBzY2hlZHVsZUltbWVkaWF0ZTogbnVsbCxcbiAgICBzY2hlZHVsZU9uTmV4dFRpY2s6IG51bGwsXG4gICAgd2FpdEF0TGVhc3RPbmVSZWFjdFJlbmRlclRhc2s6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgYXRMZWFzdE9uZVRhc2s6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gYXRMZWFzdE9uZVRhc2s7XG4gICAgfSxcbiAgICBzY2hlZHVsZUltbWVkaWF0ZTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBzY2hlZHVsZUltbWVkaWF0ZTtcbiAgICB9LFxuICAgIHNjaGVkdWxlT25OZXh0VGljazogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBzY2hlZHVsZU9uTmV4dFRpY2s7XG4gICAgfSxcbiAgICB3YWl0QXRMZWFzdE9uZVJlYWN0UmVuZGVyVGFzazogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3YWl0QXRMZWFzdE9uZVJlYWN0UmVuZGVyVGFzaztcbiAgICB9XG59KTtcbmNvbnN0IHNjaGVkdWxlT25OZXh0VGljayA9IChjYik9PntcbiAgICAvLyBXZSB1c2UgUHJvbWlzZS5yZXNvbHZlKCkudGhlbigpIGhlcmUgc28gdGhhdCB0aGUgb3BlcmF0aW9uIGlzIHNjaGVkdWxlZCBhdFxuICAgIC8vIHRoZSBlbmQgb2YgdGhlIHByb21pc2Ugam9iIHF1ZXVlLCB3ZSB0aGVuIGFkZCBpdCB0byB0aGUgbmV4dCBwcm9jZXNzIHRpY2tcbiAgICAvLyB0byBlbnN1cmUgaXQncyBldmFsdWF0ZWQgYWZ0ZXJ3YXJkcy5cbiAgICAvL1xuICAgIC8vIFRoaXMgd2FzIGluc3BpcmVkIGJ5IHRoZSBpbXBsZW1lbnRhdGlvbiBvZiB0aGUgRGF0YUxvYWRlciBpbnRlcmZhY2U6IGh0dHBzOi8vZ2l0aHViLmNvbS9ncmFwaHFsL2RhdGFsb2FkZXIvYmxvYi9kMzM2YmQxNTI4MjY2NGUwYmU0YjRhNjU3Y2I3OTZmMDliYWZiYzZiL3NyYy9pbmRleC5qcyNMMjEzLUwyNTVcbiAgICAvL1xuICAgIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oKCk9PntcbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSA9PT0gJ2VkZ2UnKSB7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KGNiLCAwKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHByb2Nlc3MubmV4dFRpY2soY2IpO1xuICAgICAgICB9XG4gICAgfSk7XG59O1xuY29uc3Qgc2NoZWR1bGVJbW1lZGlhdGUgPSAoY2IpPT57XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSA9PT0gJ2VkZ2UnKSB7XG4gICAgICAgIHNldFRpbWVvdXQoY2IsIDApO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHNldEltbWVkaWF0ZShjYik7XG4gICAgfVxufTtcbmZ1bmN0aW9uIGF0TGVhc3RPbmVUYXNrKCkge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSk9PnNjaGVkdWxlSW1tZWRpYXRlKHJlc29sdmUpKTtcbn1cbmZ1bmN0aW9uIHdhaXRBdExlYXN0T25lUmVhY3RSZW5kZXJUYXNrKCkge1xuICAgIGlmIChwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgPT09ICdlZGdlJykge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHIpPT5zZXRUaW1lb3V0KHIsIDApKTtcbiAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHIpPT5zZXRJbW1lZGlhdGUocikpO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2NoZWR1bGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return afterTaskAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst afterTaskAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=after-task-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsaUVBQWdFO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsMkJBQTJCLG1CQUFPLENBQUMsb0hBQXVCO0FBQzFEOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxuZXdcXGRvY3VtZW50LXJlcG9zaXRvcnlcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2VydmVyXFxhcHAtcmVuZGVyXFxhZnRlci10YXNrLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJhZnRlclRhc2tBc3luY1N0b3JhZ2VJbnN0YW5jZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gYWZ0ZXJUYXNrQXN5bmNTdG9yYWdlSW5zdGFuY2U7XG4gICAgfVxufSk7XG5jb25zdCBfYXN5bmNsb2NhbHN0b3JhZ2UgPSByZXF1aXJlKFwiLi9hc3luYy1sb2NhbC1zdG9yYWdlXCIpO1xuY29uc3QgYWZ0ZXJUYXNrQXN5bmNTdG9yYWdlSW5zdGFuY2UgPSAoMCwgX2FzeW5jbG9jYWxzdG9yYWdlLmNyZWF0ZUFzeW5jTG9jYWxTdG9yYWdlKSgpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hZnRlci10YXNrLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/after-task-async-storage.external.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _aftertaskasyncstorageinstance.afterTaskAsyncStorageInstance;\n    }\n}));\nconst _aftertaskasyncstorageinstance = __webpack_require__(/*! ./after-task-async-storage-instance */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\");\n\n//# sourceMappingURL=after-task-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YseURBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsdUNBQXVDLG1CQUFPLENBQUMsZ0pBQXFDOztBQUVwRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcbmV3XFxkb2N1bWVudC1yZXBvc2l0b3J5XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiYWZ0ZXJUYXNrQXN5bmNTdG9yYWdlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfYWZ0ZXJ0YXNrYXN5bmNzdG9yYWdlaW5zdGFuY2UuYWZ0ZXJUYXNrQXN5bmNTdG9yYWdlSW5zdGFuY2U7XG4gICAgfVxufSk7XG5jb25zdCBfYWZ0ZXJ0YXNrYXN5bmNzdG9yYWdlaW5zdGFuY2UgPSByZXF1aXJlKFwiLi9hZnRlci10YXNrLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2VcIik7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/dynamic-rendering.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Postpone: function() {\n        return Postpone;\n    },\n    PreludeState: function() {\n        return PreludeState;\n    },\n    abortAndThrowOnSynchronousRequestDataAccess: function() {\n        return abortAndThrowOnSynchronousRequestDataAccess;\n    },\n    abortOnSynchronousPlatformIOAccess: function() {\n        return abortOnSynchronousPlatformIOAccess;\n    },\n    accessedDynamicData: function() {\n        return accessedDynamicData;\n    },\n    annotateDynamicAccess: function() {\n        return annotateDynamicAccess;\n    },\n    consumeDynamicAccess: function() {\n        return consumeDynamicAccess;\n    },\n    createDynamicTrackingState: function() {\n        return createDynamicTrackingState;\n    },\n    createDynamicValidationState: function() {\n        return createDynamicValidationState;\n    },\n    createHangingInputAbortSignal: function() {\n        return createHangingInputAbortSignal;\n    },\n    createPostponedAbortSignal: function() {\n        return createPostponedAbortSignal;\n    },\n    formatDynamicAPIAccesses: function() {\n        return formatDynamicAPIAccesses;\n    },\n    getFirstDynamicReason: function() {\n        return getFirstDynamicReason;\n    },\n    isDynamicPostpone: function() {\n        return isDynamicPostpone;\n    },\n    isPrerenderInterruptedError: function() {\n        return isPrerenderInterruptedError;\n    },\n    markCurrentScopeAsDynamic: function() {\n        return markCurrentScopeAsDynamic;\n    },\n    postponeWithTracking: function() {\n        return postponeWithTracking;\n    },\n    throwIfDisallowedDynamic: function() {\n        return throwIfDisallowedDynamic;\n    },\n    throwToInterruptStaticGeneration: function() {\n        return throwToInterruptStaticGeneration;\n    },\n    trackAllowedDynamicAccess: function() {\n        return trackAllowedDynamicAccess;\n    },\n    trackDynamicDataInDynamicRender: function() {\n        return trackDynamicDataInDynamicRender;\n    },\n    trackFallbackParamAccessed: function() {\n        return trackFallbackParamAccessed;\n    },\n    trackSynchronousPlatformIOAccessInDev: function() {\n        return trackSynchronousPlatformIOAccessInDev;\n    },\n    trackSynchronousRequestDataAccessInDev: function() {\n        return trackSynchronousRequestDataAccessInDev;\n    },\n    useDynamicRouteParams: function() {\n        return useDynamicRouteParams;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ./work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _metadataconstants = __webpack_require__(/*! ../../lib/metadata/metadata-constants */ \"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst hasPostpone = typeof _react.default.unstable_postpone === 'function';\nfunction createDynamicTrackingState(isDebugDynamicAccesses) {\n    return {\n        isDebugDynamicAccesses,\n        dynamicAccesses: [],\n        syncDynamicErrorWithStack: null\n    };\n}\nfunction createDynamicValidationState() {\n    return {\n        hasSuspenseAboveBody: false,\n        hasDynamicMetadata: false,\n        hasDynamicViewport: false,\n        hasAllowedDynamic: false,\n        dynamicErrors: []\n    };\n}\nfunction getFirstDynamicReason(trackingState) {\n    var _trackingState_dynamicAccesses_;\n    return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\nfunction markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n    }\n    // If we're forcing dynamic rendering or we're forcing static rendering, we\n    // don't need to do anything here because the entire page is already dynamic\n    // or it's static and it should not throw or postpone here.\n    if (store.forceDynamic || store.forceStatic) return;\n    if (store.dynamicShouldError) {\n        throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(\"Route \".concat(store.route, ' with `dynamic = \"error\"` couldn\\'t be rendered statically because it used `').concat(expression, \"`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\")), \"__NEXT_ERROR_CODE\", {\n            value: \"E553\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (workUnitStore) {\n        if (workUnitStore.type === 'prerender-ppr') {\n            postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n        } else if (workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(\"Route \".concat(store.route, \" couldn't be rendered statically because it used \").concat(expression, \". See more info here: https://nextjs.org/docs/messages/dynamic-server-error\")), \"__NEXT_ERROR_CODE\", {\n                value: \"E550\",\n                enumerable: false,\n                configurable: true\n            });\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        } else if ( true && workUnitStore && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\nfunction trackFallbackParamAccessed(store, expression) {\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return;\n    postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking);\n}\nfunction throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n    // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n    const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(\"Route \".concat(store.route, \" couldn't be rendered statically because it used `\").concat(expression, \"`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\")), \"__NEXT_ERROR_CODE\", {\n        value: \"E558\",\n        enumerable: false,\n        configurable: true\n    });\n    prerenderStore.revalidate = 0;\n    store.dynamicUsageDescription = expression;\n    store.dynamicUsageStack = err.stack;\n    throw err;\n}\nfunction trackDynamicDataInDynamicRender(_store, workUnitStore) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n        // TODO: it makes no sense to have these work unit store types during a dev render.\n        if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-client' || workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n        }\n        if ( true && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n    const reason = \"Route \".concat(route, \" needs to bail out of prerendering at this point because it used \").concat(expression, \".\");\n    const error = createPrerenderInterruptedError(reason);\n    prerenderStore.controller.abort(error);\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        }\n    }\n}\nfunction trackSynchronousPlatformIOAccessInDev(requestStore) {\n    // We don't actually have a controller to abort but we do the semantic equivalent by\n    // advancing the request store out of prerender mode\n    requestStore.prerenderPhase = false;\n}\nfunction abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n    const prerenderSignal = prerenderStore.controller.signal;\n    if (prerenderSignal.aborted === false) {\n        // TODO it would be better to move this aborted check into the callsite so we can avoid making\n        // the error object when it isn't relevant to the aborting of the prerender however\n        // since we need the throw semantics regardless of whether we abort it is easier to land\n        // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n        // to ideal implementation\n        abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n        // It is important that we set this tracking value after aborting. Aborts are executed\n        // synchronously except for the case where you abort during render itself. By setting this\n        // value late we can use it to determine if any of the aborted tasks are the task that\n        // called the sync IO expression in the first place.\n        const dynamicTracking = prerenderStore.dynamicTracking;\n        if (dynamicTracking) {\n            if (dynamicTracking.syncDynamicErrorWithStack === null) {\n                dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n            }\n        }\n    }\n    throw createPrerenderInterruptedError(\"Route \".concat(route, \" needs to bail out of prerendering at this point because it used \").concat(expression, \".\"));\n}\nconst trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nfunction Postpone(param) {\n    let { reason, route } = param;\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n    postponeWithTracking(route, reason, dynamicTracking);\n}\n_c = Postpone;\nfunction postponeWithTracking(route, expression, dynamicTracking) {\n    assertPostpone();\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n    _react.default.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n    return \"Route \".concat(route, \" needs to bail out of prerendering at this point because it used \").concat(expression, \". \") + \"React throws this special object to indicate where. It should not be caught by \" + \"your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error\";\n}\nfunction isDynamicPostpone(err) {\n    if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n        return isDynamicPostponeReason(err.message);\n    }\n    return false;\n}\nfunction isDynamicPostponeReason(reason) {\n    return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n    throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n        value: \"E296\",\n        enumerable: false,\n        configurable: true\n    });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = NEXT_PRERENDER_INTERRUPTED;\n    return error;\n}\nfunction isPrerenderInterruptedError(error) {\n    return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nfunction accessedDynamicData(dynamicAccesses) {\n    return dynamicAccesses.length > 0;\n}\nfunction consumeDynamicAccess(serverDynamic, clientDynamic) {\n    // We mutate because we only call this once we are no longer writing\n    // to the dynamicTrackingState and it's more efficient than creating a new\n    // array.\n    serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n    return serverDynamic.dynamicAccesses;\n}\nfunction formatDynamicAPIAccesses(dynamicAccesses) {\n    return dynamicAccesses.filter((access)=>typeof access.stack === 'string' && access.stack.length > 0).map((param)=>{\n        let { expression, stack } = param;\n        stack = stack.split('\\n') // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes('node_modules/next/')) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(' (<anonymous>)')) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(' (node:')) {\n                return false;\n            }\n            return true;\n        }).join('\\n');\n        return \"Dynamic API Usage Debug - \".concat(expression, \":\\n\").concat(stack);\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw Object.defineProperty(new Error(\"Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E224\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\nfunction createPostponedAbortSignal(reason) {\n    assertPostpone();\n    const controller = new AbortController();\n    // We get our hands on a postpone instance by calling postpone and catching the throw\n    try {\n        _react.default.unstable_postpone(reason);\n    } catch (x) {\n        controller.abort(x);\n    }\n    return controller.signal;\n}\nfunction createHangingInputAbortSignal(workUnitStore) {\n    const controller = new AbortController();\n    if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If the input\n        // we're waiting on is coming from another cache, we do want to wait for it so that\n        // we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(()=>{\n            controller.abort();\n        });\n    } else {\n        // Otherwise we're in the final render and we should already have all our caches\n        // filled. We might still be waiting on some microtasks so we wait one tick before\n        // giving up. When we give up, we still want to render the content of this cache\n        // as deeply as we can so that we can suspend as deeply as possible in the tree\n        // or not at all if we don't end up waiting for the input.\n        (0, _scheduler.scheduleOnNextTick)(()=>controller.abort());\n    }\n    return controller.signal;\n}\nfunction annotateDynamicAccess(expression, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction useDynamicRouteParams(expression) {\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    if (workStore && workStore.isStaticGeneration && workStore.fallbackRouteParams && workStore.fallbackRouteParams.size > 0) {\n        // There are fallback route params, we should track these as dynamic\n        // accesses.\n        const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n        if (workUnitStore) {\n            // We're prerendering with dynamicIO or PPR or both\n            if (workUnitStore.type === 'prerender-client') {\n                // We are in a prerender with dynamicIO semantics\n                // We are going to hang here and never resolve. This will cause the currently\n                // rendering component to effectively be a dynamic hole\n                _react.default.use((0, _dynamicrenderingutils.makeHangingPromise)(workUnitStore.renderSignal, expression));\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // We're prerendering with PPR\n                postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                throwToInterruptStaticGeneration(expression, workStore, workUnitStore);\n            }\n        }\n    }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasSuspenseAfterBodyOrHtmlRegex = /\\n\\s+at (?:body|html) \\(<anonymous>\\)[\\s\\S]*?\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasMetadataRegex = new RegExp(\"\\\\n\\\\s+at \".concat(_metadataconstants.METADATA_BOUNDARY_NAME, \"[\\\\n\\\\s]\"));\nconst hasViewportRegex = new RegExp(\"\\\\n\\\\s+at \".concat(_metadataconstants.VIEWPORT_BOUNDARY_NAME, \"[\\\\n\\\\s]\"));\nconst hasOutletRegex = new RegExp(\"\\\\n\\\\s+at \".concat(_metadataconstants.OUTLET_BOUNDARY_NAME, \"[\\\\n\\\\s]\"));\nfunction trackAllowedDynamicAccess(workStore, componentStack, dynamicValidation, clientDynamic) {\n    if (hasOutletRegex.test(componentStack)) {\n        // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n        return;\n    } else if (hasMetadataRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicMetadata = true;\n        return;\n    } else if (hasViewportRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicViewport = true;\n        return;\n    } else if (hasSuspenseAfterBodyOrHtmlRegex.test(componentStack)) {\n        // This prerender has a Suspense boundary above the body which\n        // effectively opts the page into allowing 100% dynamic rendering\n        dynamicValidation.hasAllowedDynamic = true;\n        dynamicValidation.hasSuspenseAboveBody = true;\n        return;\n    } else if (hasSuspenseRegex.test(componentStack)) {\n        // this error had a Suspense boundary above it so we don't need to report it as a source\n        // of disallowed\n        dynamicValidation.hasAllowedDynamic = true;\n        return;\n    } else if (clientDynamic.syncDynamicErrorWithStack) {\n        // This task was the task that called the sync error.\n        dynamicValidation.dynamicErrors.push(clientDynamic.syncDynamicErrorWithStack);\n        return;\n    } else {\n        const message = 'Route \"'.concat(workStore.route, '\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense');\n        const error = createErrorWithComponentOrOwnerStack(message, componentStack);\n        dynamicValidation.dynamicErrors.push(error);\n        return;\n    }\n}\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */ function createErrorWithComponentOrOwnerStack(message, componentStack) {\n    const ownerStack =  true && _react.default.captureOwnerStack ? _react.default.captureOwnerStack() : null;\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.stack = error.name + ': ' + message + (ownerStack !== null && ownerStack !== void 0 ? ownerStack : componentStack);\n    return error;\n}\nvar PreludeState = /*#__PURE__*/ function(PreludeState) {\n    PreludeState[PreludeState[\"Full\"] = 0] = \"Full\";\n    PreludeState[PreludeState[\"Empty\"] = 1] = \"Empty\";\n    PreludeState[PreludeState[\"Errored\"] = 2] = \"Errored\";\n    return PreludeState;\n}({});\nfunction logDisallowedDynamicError(workStore, error) {\n    console.error(error);\n    if (!workStore.dev) {\n        if (workStore.hasReadableErrorStacks) {\n            console.error('To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running `next dev`, then open \"'.concat(workStore.route, '\" in your browser to investigate the error.'));\n        } else {\n            console.error('To get a more detailed stack trace and pinpoint the issue, try one of the following:\\n  - Start the app in development mode by running `next dev`, then open \"'.concat(workStore.route, '\" in your browser to investigate the error.\\n  - Rerun the production build with `next build --debug-prerender` to generate better stack traces.'));\n        }\n    }\n}\nfunction throwIfDisallowedDynamic(workStore, prelude, dynamicValidation, serverDynamic) {\n    if (workStore.invalidDynamicUsageError) {\n        logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError);\n        throw new _staticgenerationbailout.StaticGenBailoutError();\n    }\n    if (prelude !== 0) {\n        if (dynamicValidation.hasSuspenseAboveBody) {\n            // This route has opted into allowing fully dynamic rendering\n            // by including a Suspense boundary above the body. In this case\n            // a lack of a shell is not considered disallowed so we simply return\n            return;\n        }\n        if (serverDynamic.syncDynamicErrorWithStack) {\n            // There is no shell and the server did something sync dynamic likely\n            // leading to an early termination of the prerender before the shell\n            // could be completed. We terminate the build/validating render.\n            logDisallowedDynamicError(workStore, serverDynamic.syncDynamicErrorWithStack);\n            throw new _staticgenerationbailout.StaticGenBailoutError();\n        }\n        // We didn't have any sync bailouts but there may be user code which\n        // blocked the root. We would have captured these during the prerender\n        // and can log them here and then terminate the build/validating render\n        const dynamicErrors = dynamicValidation.dynamicErrors;\n        if (dynamicErrors.length > 0) {\n            for(let i = 0; i < dynamicErrors.length; i++){\n                logDisallowedDynamicError(workStore, dynamicErrors[i]);\n            }\n            throw new _staticgenerationbailout.StaticGenBailoutError();\n        }\n        // If we got this far then the only other thing that could be blocking\n        // the root is dynamic Viewport. If this is dynamic then\n        // you need to opt into that by adding a Suspense boundary above the body\n        // to indicate your are ok with fully dynamic rendering.\n        if (dynamicValidation.hasDynamicViewport) {\n            console.error('Route \"'.concat(workStore.route, '\" has a `generateViewport` that depends on Request data (`cookies()`, etc...) or uncached external data (`fetch(...)`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport'));\n            throw new _staticgenerationbailout.StaticGenBailoutError();\n        }\n        if (prelude === 1) {\n            // If we ever get this far then we messed up the tracking of invalid dynamic.\n            // We still adhere to the constraint that you must produce a shell but invite the\n            // user to report this as a bug in Next.js.\n            console.error('Route \"'.concat(workStore.route, '\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.'));\n            throw new _staticgenerationbailout.StaticGenBailoutError();\n        }\n    } else {\n        if (dynamicValidation.hasAllowedDynamic === false && dynamicValidation.hasDynamicMetadata) {\n            console.error('Route \"'.concat(workStore.route, '\" has a `generateMetadata` that depends on Request data (`cookies()`, etc...) or uncached external data (`fetch(...)`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata'));\n            throw new _staticgenerationbailout.StaticGenBailoutError();\n        }\n    }\n} //# sourceMappingURL=dynamic-rendering.js.map\nvar _c;\n$RefreshReg$(_c, \"Postpone\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZHluYW1pYy1yZW5kZXJpbmcuanMiLCJtYXBwaW5ncyI6IkFBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBb0JDOzs7Ozs7Ozs7Ozs7SUFnVmVBLFFBQVE7ZUFBUkE7O0lBMlRKQyxZQUFZO2VBQVpBOztJQXBXSUMsMkNBQTJDO2VBQTNDQTs7SUFyQ0FDLGtDQUFrQztlQUFsQ0E7O0lBd0tBQyxtQkFBbUI7ZUFBbkJBOztJQTRHQUMscUJBQXFCO2VBQXJCQTs7SUF0R0FDLG9CQUFvQjtlQUFwQkE7O0lBL1dBQywwQkFBMEI7ZUFBMUJBOztJQVVBQyw0QkFBNEI7ZUFBNUJBOztJQW1iQUMsNkJBQTZCO2VBQTdCQTs7SUFqQkFDLDBCQUEwQjtlQUExQkE7O0lBbERBQyx3QkFBd0I7ZUFBeEJBOztJQXRXQUMscUJBQXFCO2VBQXJCQTs7SUFnU0FDLGlCQUFpQjtlQUFqQkE7O0lBd0NBQywyQkFBMkI7ZUFBM0JBOztJQTNUQUMseUJBQXlCO2VBQXpCQTs7SUF1UEFDLG9CQUFvQjtlQUFwQkE7O0lBd1VBQyx3QkFBd0I7ZUFBeEJBOztJQS9lQUMsZ0NBQWdDO2VBQWhDQTs7SUErWkFDLHlCQUF5QjtlQUF6QkE7O0lBdFlBQywrQkFBK0I7ZUFBL0JBOztJQXpDQUMsMEJBQTBCO2VBQTFCQTs7SUFtSEFDLHFDQUFxQztlQUFyQ0E7O0lBaURIQyxzQ0FBc0M7ZUFBdENBOztJQStOR0MscUJBQXFCO2VBQXJCQTs7OzBFQTFoQkU7Z0RBRWlCO3FEQUNHOzBEQUNEO3NEQUNKO21EQUNFOytDQUs1Qjt1Q0FDNEI7Ozs7OztBQUVuQyxNQUFNQyxjQUFjLE9BQU9DLE9BQUFBLE9BQUssQ0FBQ0MsaUJBQWlCLEtBQUs7QUF3Q2hELFNBQVNwQiwyQkFDZHFCLHNCQUEyQztJQUUzQyxPQUFPO1FBQ0xBO1FBQ0FDLGlCQUFpQixFQUFFO1FBQ25CQywyQkFBMkI7SUFDN0I7QUFDRjtBQUVPLFNBQVN0QjtJQUNkLE9BQU87UUFDTHVCLHNCQUFzQjtRQUN0QkMsb0JBQW9CO1FBQ3BCQyxvQkFBb0I7UUFDcEJDLG1CQUFtQjtRQUNuQkMsZUFBZSxFQUFFO0lBQ25CO0FBQ0Y7QUFFTyxTQUFTdkIsc0JBQ2R3QixhQUFtQztRQUU1QkE7SUFBUCxRQUFPQSxrQ0FBQUEsY0FBY1AsZUFBZSxDQUFDLE9BQUUsZ0JBQWhDTyxnQ0FBa0NDLFVBQVU7QUFDckQ7QUFTTyxTQUFTdEIsMEJBQ2R1QixLQUFnQixFQUNoQkMsYUFBdUUsRUFDdkVGLFVBQWtCO0lBRWxCLElBQUlFLGVBQWU7UUFDakIsSUFDRUEsY0FBY0MsSUFBSSxLQUFLLFdBQ3ZCRCxjQUFjQyxJQUFJLEtBQUssa0JBQ3ZCO1lBQ0EsNkZBQTZGO1lBQzdGLGlHQUFpRztZQUNqRyxrQ0FBa0M7WUFDbEM7UUFDRjtJQUNGO0lBRUEsMkVBQTJFO0lBQzNFLDRFQUE0RTtJQUM1RSwyREFBMkQ7SUFDM0QsSUFBSUYsTUFBTUcsWUFBWSxJQUFJSCxNQUFNSSxXQUFXLEVBQUU7SUFFN0MsSUFBSUosTUFBTUssa0JBQWtCLEVBQUU7UUFDNUIsTUFBTSxxQkFFTCxDQUZLLElBQUlDLHlCQUFBQSxxQkFBcUIsQ0FDN0IsTUFBTyxVQUFFTixNQUFNTyxLQUFLLEVBQUMsOEVBQThFLFNBQUVSLFlBQVcsNEhBQTRILENBQUMsSUFEek87bUJBQUE7d0JBQUE7MEJBQUE7UUFFTjtJQUNGO0lBRUEsSUFBSUUsZUFBZTtRQUNqQixJQUFJQSxjQUFjQyxJQUFJLEtBQUssaUJBQWlCO1lBQzFDeEIscUJBQ0VzQixNQUFNTyxLQUFLLEVBQ1hSLFlBQ0FFLGNBQWNPLGVBQWU7UUFFakMsT0FBTyxJQUFJUCxjQUFjQyxJQUFJLEtBQUssb0JBQW9CO1lBQ3BERCxjQUFjUSxVQUFVLEdBQUc7WUFFM0IsdUdBQXVHO1lBQ3ZHLE1BQU1DLE1BQU0scUJBRVgsQ0FGVyxJQUFJQyxvQkFBQUEsa0JBQWtCLENBQ2hDLE1BQU8sVUFBRVgsTUFBTU8sS0FBSyxFQUFDLGlEQUFpRCxJQUFhLE9BQVhSLG9FQUFzRixDQUFDLHdCQURySjt1QkFBQTs0QkFBQTs4QkFBQTtZQUVaO1lBQ0FDLE1BQU1ZLHVCQUF1QixHQUFHYjtZQUNoQ0MsTUFBTWEsaUJBQWlCLEdBQUdILElBQUlJLEtBQUs7WUFFbkMsTUFBTUo7UUFDUixPQUFPLElBQ0xLLEtBQW9CLElBQ3BCZCxpQkFDQUEsY0FBY0MsSUFBSSxLQUFLLFdBQ3ZCO1lBQ0FELGNBQWNpQixXQUFXLEdBQUc7UUFDOUI7SUFDRjtBQUNGO0FBVU8sU0FBU25DLDJCQUNkaUIsS0FBZ0IsRUFDaEJELFVBQWtCO0lBRWxCLE1BQU1vQixpQkFBaUJDLDhCQUFBQSxvQkFBb0IsQ0FBQ0MsUUFBUTtJQUNwRCxJQUFJLENBQUNGLGtCQUFrQkEsZUFBZWpCLElBQUksS0FBSyxpQkFBaUI7SUFFaEV4QixxQkFBcUJzQixNQUFNTyxLQUFLLEVBQUVSLFlBQVlvQixlQUFlWCxlQUFlO0FBQzlFO0FBUU8sU0FBUzVCLGlDQUNkbUIsVUFBa0IsRUFDbEJDLEtBQWdCLEVBQ2hCbUIsY0FBb0M7SUFFcEMsdUdBQXVHO0lBQ3ZHLE1BQU1ULE1BQU0scUJBRVgsQ0FGVyxJQUFJQyxvQkFBQUEsa0JBQWtCLENBQ2hDLE1BQU8sR0FBbUVaLE9BQWpFQyxNQUFNTyxLQUFLLEVBQUMsbURBQW1ELHNCQUFhLDZFQUE2RSxDQUFDLElBRHpKO2VBQUE7b0JBQUE7c0JBQUE7SUFFWjtJQUVBWSxlQUFlVixVQUFVLEdBQUc7SUFFNUJULE1BQU1ZLHVCQUF1QixHQUFHYjtJQUNoQ0MsTUFBTWEsaUJBQWlCLEdBQUdILElBQUlJLEtBQUs7SUFFbkMsTUFBTUo7QUFDUjtBQVNPLFNBQVM1QixnQ0FDZHdDLE1BQWlCLEVBQ2pCckIsYUFBbUM7SUFFbkMsSUFBSUEsZUFBZTtRQUNqQixJQUNFQSxjQUFjQyxJQUFJLEtBQUssV0FDdkJELGNBQWNDLElBQUksS0FBSyxrQkFDdkI7WUFDQSw2RkFBNkY7WUFDN0YsaUdBQWlHO1lBQ2pHLGtDQUFrQztZQUNsQztRQUNGO1FBQ0EsbUZBQW1GO1FBQ25GLElBQ0VELGNBQWNDLElBQUksS0FBSyxlQUN2QkQsY0FBY0MsSUFBSSxLQUFLLHNCQUN2QkQsY0FBY0MsSUFBSSxLQUFLLG9CQUN2QjtZQUNBRCxjQUFjUSxVQUFVLEdBQUc7UUFDN0I7UUFDQSxJQUNFTSxLQUFvQixJQUNwQmQsY0FBY0MsSUFBSSxLQUFLLFdBQ3ZCO1lBQ0FELGNBQWNpQixXQUFXLEdBQUc7UUFDOUI7SUFDRjtBQUNGO0FBRUEsU0FBU0ssb0NBQ1BoQixLQUFhLEVBQ2JSLFVBQWtCLEVBQ2xCb0IsY0FBb0M7SUFFcEMsTUFBTUssU0FBUyxNQUFPLEdBQTJFekIsT0FBekVRLE9BQU0saUVBQWlFLHVCQUFhLENBQUMsQ0FBQztJQUU5RyxNQUFNa0IsUUFBUUMsZ0NBQWdDRjtJQUU5Q0wsZUFBZVEsVUFBVSxDQUFDQyxLQUFLLENBQUNIO0lBRWhDLE1BQU1qQixrQkFBa0JXLGVBQWVYLGVBQWU7SUFDdEQsSUFBSUEsaUJBQWlCO1FBQ25CQSxnQkFBZ0JqQixlQUFlLENBQUNzQyxJQUFJLENBQUM7WUFDbkMsMEVBQTBFO1lBQzFFLGVBQWU7WUFDZmYsT0FBT04sZ0JBQWdCbEIsc0JBQXNCLEdBQ3pDLElBQUl3QyxRQUFRaEIsS0FBSyxHQUNqQmlCO1lBQ0poQztRQUNGO0lBQ0Y7QUFDRjtBQUVPLFNBQVNsQyxtQ0FDZDBDLEtBQWEsRUFDYlIsVUFBa0IsRUFDbEJpQyxjQUFxQixFQUNyQmIsY0FBb0M7SUFFcEMsTUFBTVgsa0JBQWtCVyxlQUFlWCxlQUFlO0lBQ3REZSxvQ0FBb0NoQixPQUFPUixZQUFZb0I7SUFDdkQsc0ZBQXNGO0lBQ3RGLDBGQUEwRjtJQUMxRixzRkFBc0Y7SUFDdEYsb0RBQW9EO0lBQ3BELElBQUlYLGlCQUFpQjtRQUNuQixJQUFJQSxnQkFBZ0JoQix5QkFBeUIsS0FBSyxNQUFNO1lBQ3REZ0IsZ0JBQWdCaEIseUJBQXlCLEdBQUd3QztRQUM5QztJQUNGO0FBQ0Y7QUFFTyxTQUFTaEQsc0NBQ2RpRCxZQUEwQjtJQUUxQixvRkFBb0Y7SUFDcEYsb0RBQW9EO0lBQ3BEQSxhQUFhQyxjQUFjLEdBQUc7QUFDaEM7QUFZTyxTQUFTdEUsNENBQ2QyQyxLQUFhLEVBQ2JSLFVBQWtCLEVBQ2xCaUMsY0FBcUIsRUFDckJiLGNBQW9DO0lBRXBDLE1BQU1nQixrQkFBa0JoQixlQUFlUSxVQUFVLENBQUNTLE1BQU07SUFDeEQsSUFBSUQsZ0JBQWdCRSxPQUFPLEtBQUssT0FBTztRQUNyQyw4RkFBOEY7UUFDOUYsbUZBQW1GO1FBQ25GLHdGQUF3RjtRQUN4Riw0RkFBNEY7UUFDNUYsMEJBQTBCO1FBQzFCZCxvQ0FBb0NoQixPQUFPUixZQUFZb0I7UUFDdkQsc0ZBQXNGO1FBQ3RGLDBGQUEwRjtRQUMxRixzRkFBc0Y7UUFDdEYsb0RBQW9EO1FBQ3BELE1BQU1YLGtCQUFrQlcsZUFBZVgsZUFBZTtRQUN0RCxJQUFJQSxpQkFBaUI7WUFDbkIsSUFBSUEsZ0JBQWdCaEIseUJBQXlCLEtBQUssTUFBTTtnQkFDdERnQixnQkFBZ0JoQix5QkFBeUIsR0FBR3dDO1lBQzlDO1FBQ0Y7SUFDRjtJQUNBLE1BQU1OLGdDQUNKLE1BQU8sVUFBRW5CLE9BQU0saUVBQWlFLElBQWEsQ0FBQyxDQUFDLEtBQWJSO0FBRXRGO0FBR08sTUFBTWQseUNBQ1hEO0FBU0s7VUFBb0J3QyxNQUFNLEVBQUVqQixLQUFLLEVBQWlCLEdBQWhDO0lBQ3ZCLE1BQU1ZLGlCQUFpQkMsOEJBQUFBLG9CQUFvQixDQUFDQyxRQUFRO0lBQ3BELE1BQU1iLGtCQUNKVyxrQkFBa0JBLGVBQWVqQixJQUFJLEtBQUssa0JBQ3RDaUIsZUFBZVgsZUFBZSxHQUM5QjtJQUNOOUIscUJBQXFCNkIsT0FBT2lCLFFBQVFoQjtBQUN0QztLQVBnQjlDO0FBU1QsU0FBU2dCLHFCQUNkNkIsS0FBYSxFQUNiUixVQUFrQixFQUNsQlMsZUFBNEM7SUFFNUM4QjtJQUNBLElBQUk5QixpQkFBaUI7UUFDbkJBLGdCQUFnQmpCLGVBQWUsQ0FBQ3NDLElBQUksQ0FBQztZQUNuQywwRUFBMEU7WUFDMUUsZUFBZTtZQUNmZixPQUFPTixnQkFBZ0JsQixzQkFBc0IsR0FDekMsSUFBSXdDLFFBQVFoQixLQUFLLEdBQ2pCaUI7WUFDSmhDO1FBQ0Y7SUFDRjtJQUVBWCxPQUFBQSxPQUFLLENBQUNDLGlCQUFpQixDQUFDa0QscUJBQXFCaEMsT0FBT1I7QUFDdEQ7QUFFQSxTQUFTd0MscUJBQXFCaEMsS0FBYSxFQUFFUixVQUFrQjtJQUM3RCxPQUNFLE1BQU8sVUFBRVEsT0FBTSxpRUFBaUUsV0FBRVIsWUFBVyxFQUFFLENBQUMsR0FDaEcsRUFBQywrRUFBK0UsQ0FBQyxHQUNqRixDQUFDLGlGQUFpRixDQUFDO0FBRXZGO0FBRU8sU0FBU3hCLGtCQUFrQm1DLEdBQVk7SUFDNUMsSUFDRSxPQUFPQSxRQUFRLFlBQ2ZBLFFBQVEsUUFDUixPQUFRQSxJQUFZOEIsT0FBTyxLQUFLLFVBQ2hDO1FBQ0EsT0FBT0Msd0JBQXlCL0IsSUFBWThCLE9BQU87SUFDckQ7SUFDQSxPQUFPO0FBQ1Q7QUFFQSxTQUFTQyx3QkFBd0JqQixNQUFjO0lBQzdDLE9BQ0VBLE9BQU9rQixRQUFRLENBQ2Isc0VBRUZsQixPQUFPa0IsUUFBUSxDQUNiO0FBR047QUFFQSxJQUFJRCx3QkFBd0JGLHFCQUFxQixPQUFPLFlBQVksT0FBTztJQUN6RSxNQUFNLHFCQUVMLENBRkssSUFBSVQsTUFDUiwyRkFESTtlQUFBO29CQUFBO3NCQUFBO0lBRU47QUFDRjtBQUVBLE1BQU1hLDZCQUE2QjtBQUVuQyxTQUFTakIsZ0NBQWdDYyxPQUFlO0lBQ3RELE1BQU1mLFFBQVEscUJBQWtCLENBQWxCLElBQUlLLE1BQU1VLFVBQVY7ZUFBQTtvQkFBQTtzQkFBQTtJQUFpQjtJQUM3QmYsTUFBY21CLE1BQU0sR0FBR0Q7SUFDekIsT0FBT2xCO0FBQ1Q7QUFNTyxTQUFTakQsNEJBQ2RpRCxLQUFjO0lBRWQsT0FDRSxPQUFPQSxVQUFVLFlBQ2pCQSxVQUFVLFFBQ1RBLE1BQWNtQixNQUFNLEtBQUtELDhCQUMxQixVQUFVbEIsU0FDVixhQUFhQSxTQUNiQSxpQkFBaUJLO0FBRXJCO0FBRU8sU0FBU2hFLG9CQUNkeUIsZUFBcUM7SUFFckMsT0FBT0EsZ0JBQWdCc0QsTUFBTSxHQUFHO0FBQ2xDO0FBRU8sU0FBUzdFLHFCQUNkOEUsYUFBbUMsRUFDbkNDLGFBQW1DO0lBRW5DLG9FQUFvRTtJQUNwRSwwRUFBMEU7SUFDMUUsU0FBUztJQUNURCxjQUFjdkQsZUFBZSxDQUFDc0MsSUFBSSxJQUFJa0IsY0FBY3hELGVBQWU7SUFDbkUsT0FBT3VELGNBQWN2RCxlQUFlO0FBQ3RDO0FBRU8sU0FBU2xCLHlCQUNka0IsZUFBcUM7SUFFckMsT0FBT0EsZ0JBQ0p5RCxNQUFNLENBQ0wsQ0FBQ0MsU0FDQyxPQUFPQSxPQUFPbkMsS0FBSyxLQUFLLFlBQVltQyxPQUFPbkMsS0FBSyxDQUFDK0IsTUFBTSxHQUFHLEdBRTdESyxHQUFHLENBQUM7WUFBQyxFQUFFbkQsVUFBVSxFQUFFZSxLQUFLLEVBQUU7UUFDekJBLFFBQVFBLE1BQ0xxQyxLQUFLLENBQUMsTUFDUCx3RUFBd0U7UUFDeEUscUVBQXFFO1FBQ3JFLHVEQUF1RDtTQUN0REMsS0FBSyxDQUFDLEdBQ05KLE1BQU0sQ0FBQyxDQUFDSztZQUNQLGtEQUFrRDtZQUNsRCxJQUFJQSxLQUFLWCxRQUFRLENBQUMsdUJBQXVCO2dCQUN2QyxPQUFPO1lBQ1Q7WUFFQSxvREFBb0Q7WUFDcEQsSUFBSVcsS0FBS1gsUUFBUSxDQUFDLG1CQUFtQjtnQkFDbkMsT0FBTztZQUNUO1lBRUEsa0RBQWtEO1lBQ2xELElBQUlXLEtBQUtYLFFBQVEsQ0FBQyxZQUFZO2dCQUM1QixPQUFPO1lBQ1Q7WUFFQSxPQUFPO1FBQ1QsR0FDQ1ksSUFBSSxDQUFDO1FBQ1IsT0FBTywwQkFBMkIsR0FBa0J4QyxPQUFoQmYsWUFBVyxHQUFHLElBQUVlLENBQU87SUFDN0Q7QUFDSjtBQUVBLFNBQVN3QjtJQUNQLElBQUksQ0FBQ25ELGFBQWE7UUFDaEIsTUFBTSxxQkFFTCxDQUZLLElBQUkyQyxNQUNSLGdJQUFpSSxDQUFDLElBRDlIO21CQUFBO3dCQUFBOzBCQUFBO1FBRU47SUFDRjtBQUNGO0FBTU8sU0FBUzFELDJCQUEyQm9ELE1BQWM7SUFDdkRjO0lBQ0EsTUFBTVgsYUFBYSxJQUFJNEI7SUFDdkIscUZBQXFGO0lBQ3JGLElBQUk7UUFDRm5FLE9BQUFBLE9BQUssQ0FBQ0MsaUJBQWlCLENBQUNtQztJQUMxQixFQUFFLE9BQU9nQyxHQUFZO1FBQ25CN0IsV0FBV0MsS0FBSyxDQUFDNEI7SUFDbkI7SUFDQSxPQUFPN0IsV0FBV1MsTUFBTTtBQUMxQjtBQU9PLFNBQVNqRSw4QkFDZDhCLGFBQW1DO0lBRW5DLE1BQU0wQixhQUFhLElBQUk0QjtJQUV2QixJQUFJdEQsY0FBY3dELFdBQVcsRUFBRTtRQUM3QixnRkFBZ0Y7UUFDaEYsbUZBQW1GO1FBQ25GLHVDQUF1QztRQUN2Q3hELGNBQWN3RCxXQUFXLENBQUNDLFVBQVUsR0FBR0MsSUFBSSxDQUFDO1lBQzFDaEMsV0FBV0MsS0FBSztRQUNsQjtJQUNGLE9BQU87UUFDTCxnRkFBZ0Y7UUFDaEYsa0ZBQWtGO1FBQ2xGLGdGQUFnRjtRQUNoRiwrRUFBK0U7UUFDL0UsMERBQTBEO1FBQzFEZ0MsQ0FBQUEsR0FBQUEsV0FBQUEsa0JBQUFBLEVBQW1CLElBQU1qQyxXQUFXQyxLQUFLO0lBQzNDO0lBRUEsT0FBT0QsV0FBV1MsTUFBTTtBQUMxQjtBQUVPLFNBQVNyRSxzQkFDZGdDLFVBQWtCLEVBQ2xCb0IsY0FBb0M7SUFFcEMsTUFBTVgsa0JBQWtCVyxlQUFlWCxlQUFlO0lBQ3RELElBQUlBLGlCQUFpQjtRQUNuQkEsZ0JBQWdCakIsZUFBZSxDQUFDc0MsSUFBSSxDQUFDO1lBQ25DZixPQUFPTixnQkFBZ0JsQixzQkFBc0IsR0FDekMsSUFBSXdDLFFBQVFoQixLQUFLLEdBQ2pCaUI7WUFDSmhDO1FBQ0Y7SUFDRjtBQUNGO0FBRU8sU0FBU2Isc0JBQXNCYSxVQUFrQjtJQUN0RCxNQUFNOEQsWUFBWUMsMEJBQUFBLGdCQUFnQixDQUFDekMsUUFBUTtJQUUzQyxJQUNFd0MsYUFDQUEsVUFBVUUsa0JBQWtCLElBQzVCRixVQUFVRyxtQkFBbUIsSUFDN0JILFVBQVVHLG1CQUFtQixDQUFDQyxJQUFJLEdBQUcsR0FDckM7UUFDQSxvRUFBb0U7UUFDcEUsWUFBWTtRQUNaLE1BQU1oRSxnQkFBZ0JtQiw4QkFBQUEsb0JBQW9CLENBQUNDLFFBQVE7UUFDbkQsSUFBSXBCLGVBQWU7WUFDakIsbURBQW1EO1lBQ25ELElBQUlBLGNBQWNDLElBQUksS0FBSyxvQkFBb0I7Z0JBQzdDLGlEQUFpRDtnQkFDakQsNkVBQTZFO2dCQUM3RSx1REFBdUQ7Z0JBQ3ZEZCxPQUFBQSxPQUFLLENBQUM4RSxHQUFHLENBQUNDLENBQUFBLEdBQUFBLHVCQUFBQSxrQkFBQUEsRUFBbUJsRSxjQUFjbUUsWUFBWSxFQUFFckU7WUFDM0QsT0FBTyxJQUFJRSxjQUFjQyxJQUFJLEtBQUssaUJBQWlCO2dCQUNqRCw4QkFBOEI7Z0JBQzlCeEIscUJBQ0VtRixVQUFVdEQsS0FBSyxFQUNmUixZQUNBRSxjQUFjTyxlQUFlO1lBRWpDLE9BQU8sSUFBSVAsY0FBY0MsSUFBSSxLQUFLLG9CQUFvQjtnQkFDcER0QixpQ0FBaUNtQixZQUFZOEQsV0FBVzVEO1lBQzFEO1FBQ0Y7SUFDRjtBQUNGO0FBRUEsTUFBTW9FLG1CQUFtQjtBQUN6QixNQUFNQyxrQ0FDSjtBQUNGLE1BQU1DLG1CQUFtQixJQUFJQyxPQUMxQixVQUFVLFVBQUVDLG1CQUFBQSxzQkFBc0IsRUFBQyxRQUFRLENBQUM7QUFFL0MsTUFBTUMsbUJBQW1CLElBQUlGLE9BQzNCLFVBQVcsVUFBRUcsbUJBQUFBLHNCQUFzQixFQUFDLFFBQVEsQ0FBQztBQUUvQyxNQUFNQyxpQkFBaUIsSUFBSUosT0FBTyxVQUFXLEdBQXVCLE9BQXJCSyxDQUE2QixDQUFDLGlCQUE5QkEsb0JBQW9CO0FBRTVELFNBQVNoRywwQkFDZGdGLFNBQW9CLEVBQ3BCaUIsY0FBc0IsRUFDdEJDLGlCQUF5QyxFQUN6Q2hDLGFBQW1DO0lBRW5DLElBQUk2QixlQUFlSSxJQUFJLENBQUNGLGlCQUFpQjtRQUN2QyxrR0FBa0c7UUFDbEc7SUFDRixPQUFPLElBQUlQLGlCQUFpQlMsSUFBSSxDQUFDRixpQkFBaUI7UUFDaERDLGtCQUFrQnJGLGtCQUFrQixHQUFHO1FBQ3ZDO0lBQ0YsT0FBTyxJQUFJZ0YsaUJBQWlCTSxJQUFJLENBQUNGLGlCQUFpQjtRQUNoREMsa0JBQWtCcEYsa0JBQWtCLEdBQUc7UUFDdkM7SUFDRixPQUFPLElBQUkyRSxnQ0FBZ0NVLElBQUksQ0FBQ0YsaUJBQWlCO1FBQy9ELDhEQUE4RDtRQUM5RCxpRUFBaUU7UUFDakVDLGtCQUFrQm5GLGlCQUFpQixHQUFHO1FBQ3RDbUYsa0JBQWtCdEYsb0JBQW9CLEdBQUc7UUFDekM7SUFDRixPQUFPLElBQUk0RSxpQkFBaUJXLElBQUksQ0FBQ0YsaUJBQWlCO1FBQ2hELHdGQUF3RjtRQUN4RixnQkFBZ0I7UUFDaEJDLGtCQUFrQm5GLGlCQUFpQixHQUFHO1FBQ3RDO0lBQ0YsT0FBTyxJQUFJbUQsY0FBY3ZELHlCQUF5QixFQUFFO1FBQ2xELHFEQUFxRDtRQUNyRHVGLGtCQUFrQmxGLGFBQWEsQ0FBQ2dDLElBQUksQ0FDbENrQixjQUFjdkQseUJBQXlCO1FBRXpDO0lBQ0YsT0FBTztRQUNMLE1BQU1nRCxVQUFXLE9BQU8sVUFBRXFCLFVBQVV0RCxLQUFLLEVBQUMsMk5BQTJOLENBQUM7UUFDdFEsTUFBTWtCLFFBQVF3RCxxQ0FBcUN6QyxTQUFTc0M7UUFDNURDLGtCQUFrQmxGLGFBQWEsQ0FBQ2dDLElBQUksQ0FBQ0o7UUFDckM7SUFDRjtBQUNGO0FBRUE7OztDQUdDLEdBQ0QsU0FBU3dELHFDQUNQekMsT0FBZSxFQUNmc0MsY0FBc0I7SUFFdEIsTUFBTUksYUFDSm5FLEtBQW9CLElBQXFCM0IsT0FBQUEsT0FBSyxDQUFDK0YsaUJBQWlCLEdBQzVEL0YsT0FBQUEsT0FBSyxDQUFDK0YsaUJBQWlCLEtBQ3ZCO0lBRU4sTUFBTTFELFFBQVEscUJBQWtCLENBQWxCLElBQUlLLE1BQU1VLFVBQVY7ZUFBQTtvQkFBQTtzQkFBQTtJQUFpQjtJQUMvQmYsTUFBTVgsS0FBSyxHQUFHVyxNQUFNMkQsSUFBSSxHQUFHLE9BQU81QyxVQUFXMEMsZ0RBQUFBLGFBQWNKLGNBQUFBLENBQWE7SUFDeEUsT0FBT3JEO0FBQ1Q7QUFFTyxJQUFLOUQsZUFBQUEsV0FBQUEsR0FBQUEsU0FBQUEsWUFBQUE7Ozs7V0FBQUE7O0FBTVosU0FBUzBILDBCQUEwQnhCLFNBQW9CLEVBQUVwQyxLQUFZO0lBQ25FNkQsUUFBUTdELEtBQUssQ0FBQ0E7SUFFZCxJQUFJLENBQUNvQyxVQUFVMEIsR0FBRyxFQUFFO1FBQ2xCLElBQUkxQixVQUFVMkIsc0JBQXNCLEVBQUU7WUFDcENGLFFBQVE3RCxLQUFLLENBQ1gsaUlBQWtJLFFBQUVvQyxVQUFVdEQsS0FBSyxFQUFDLDJDQUEyQyxDQUFDO1FBRXBNLE9BQU87WUFDTCtFLFFBQVE3RCxLQUFLLENBQUMsd0tBQ3dEb0MsVUFBVXRELEtBQUssRUFBQztRQUV4RjtJQUNGO0FBQ0Y7QUFFTyxTQUFTNUIseUJBQ2RrRixTQUFvQixFQUNwQjRCLE9BQXFCLEVBQ3JCVixpQkFBeUMsRUFDekNqQyxhQUFtQztJQUVuQyxJQUFJZSxVQUFVNkIsd0JBQXdCLEVBQUU7UUFDdENMLDBCQUEwQnhCLFdBQVdBLFVBQVU2Qix3QkFBd0I7UUFDdkUsTUFBTSxJQUFJcEYseUJBQUFBLHFCQUFxQjtJQUNqQztJQUVBLElBQUltRixZQUFBQSxHQUErQjtRQUNqQyxJQUFJVixrQkFBa0J0RixvQkFBb0IsRUFBRTtZQUMxQyw2REFBNkQ7WUFDN0QsZ0VBQWdFO1lBQ2hFLHFFQUFxRTtZQUNyRTtRQUNGO1FBRUEsSUFBSXFELGNBQWN0RCx5QkFBeUIsRUFBRTtZQUMzQyxxRUFBcUU7WUFDckUsb0VBQW9FO1lBQ3BFLGdFQUFnRTtZQUNoRTZGLDBCQUNFeEIsV0FDQWYsY0FBY3RELHlCQUF5QjtZQUV6QyxNQUFNLElBQUljLHlCQUFBQSxxQkFBcUI7UUFDakM7UUFFQSxvRUFBb0U7UUFDcEUsc0VBQXNFO1FBQ3RFLHVFQUF1RTtRQUN2RSxNQUFNVCxnQkFBZ0JrRixrQkFBa0JsRixhQUFhO1FBQ3JELElBQUlBLGNBQWNnRCxNQUFNLEdBQUcsR0FBRztZQUM1QixJQUFLLElBQUk4QyxJQUFJLEdBQUdBLElBQUk5RixjQUFjZ0QsTUFBTSxFQUFFOEMsSUFBSztnQkFDN0NOLDBCQUEwQnhCLFdBQVdoRSxhQUFhLENBQUM4RixFQUFFO1lBQ3ZEO1lBRUEsTUFBTSxJQUFJckYseUJBQUFBLHFCQUFxQjtRQUNqQztRQUVBLHNFQUFzRTtRQUN0RSx3REFBd0Q7UUFDeEQseUVBQXlFO1FBQ3pFLHdEQUF3RDtRQUN4RCxJQUFJeUUsa0JBQWtCcEYsa0JBQWtCLEVBQUU7WUFDeEMyRixRQUFRN0QsS0FBSyxDQUNYLE9BQVEsVUFBRW9DLFVBQVV0RCxLQUFLLEVBQUM7WUFFNUIsTUFBTSxJQUFJRCx5QkFBQUEscUJBQXFCO1FBQ2pDO1FBRUEsSUFBSW1GLFlBQUFBLEdBQWdDO1lBQ2xDLDZFQUE2RTtZQUM3RSxpRkFBaUY7WUFDakYsMkNBQTJDO1lBQzNDSCxRQUFRN0QsS0FBSyxDQUNYLE9BQVEsVUFBRW9DLFVBQVV0RCxLQUFLLEVBQUMsd0dBQXdHLENBQUM7WUFFckksTUFBTSxJQUFJRCx5QkFBQUEscUJBQXFCO1FBQ2pDO0lBQ0YsT0FBTztRQUNMLElBQ0V5RSxrQkFBa0JuRixpQkFBaUIsS0FBSyxTQUN4Q21GLGtCQUFrQnJGLGtCQUFrQixFQUNwQztZQUNBNEYsUUFBUTdELEtBQUssQ0FDWCxPQUFRLFVBQUVvQyxVQUFVdEQsS0FBSyxFQUFDO1lBRTVCLE1BQU0sSUFBSUQseUJBQUFBLHFCQUFxQjtRQUNqQztJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcc3JjXFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXGR5bmFtaWMtcmVuZGVyaW5nLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhlIGZ1bmN0aW9ucyBwcm92aWRlZCBieSB0aGlzIG1vZHVsZSBhcmUgdXNlZCB0byBjb21tdW5pY2F0ZSBjZXJ0YWluIHByb3BlcnRpZXNcbiAqIGFib3V0IHRoZSBjdXJyZW50bHkgcnVubmluZyBjb2RlIHNvIHRoYXQgTmV4dC5qcyBjYW4gbWFrZSBkZWNpc2lvbnMgb24gaG93IHRvIGhhbmRsZVxuICogdGhlIGN1cnJlbnQgZXhlY3V0aW9uIGluIGRpZmZlcmVudCByZW5kZXJpbmcgbW9kZXMgc3VjaCBhcyBwcmUtcmVuZGVyaW5nLCByZXN1bWluZywgYW5kIFNTUi5cbiAqXG4gKiBUb2RheSBOZXh0LmpzIHRyZWF0cyBhbGwgY29kZSBhcyBwb3RlbnRpYWxseSBzdGF0aWMuIENlcnRhaW4gQVBJcyBtYXkgb25seSBtYWtlIHNlbnNlIHdoZW4gZHluYW1pY2FsbHkgcmVuZGVyaW5nLlxuICogVHJhZGl0aW9uYWxseSB0aGlzIG1lYW50IGRlb3B0aW5nIHRoZSBlbnRpcmUgcmVuZGVyIHRvIGR5bmFtaWMgaG93ZXZlciB3aXRoIFBQUiB3ZSBjYW4gbm93IGRlb3B0IHBhcnRzXG4gKiBvZiBhIFJlYWN0IHRyZWUgYXMgZHluYW1pYyB3aGlsZSBzdGlsbCBrZWVwaW5nIG90aGVyIHBhcnRzIHN0YXRpYy4gVGhlcmUgYXJlIHJlYWxseSB0d28gZGlmZmVyZW50IGtpbmRzIG9mXG4gKiBEeW5hbWljIGluZGljYXRpb25zLlxuICpcbiAqIFRoZSBmaXJzdCBpcyBzaW1wbHkgYW4gaW50ZW50aW9uIHRvIGJlIGR5bmFtaWMuIHVuc3RhYmxlX25vU3RvcmUgaXMgYW4gZXhhbXBsZSBvZiB0aGlzIHdoZXJlXG4gKiB0aGUgY3VycmVudGx5IGV4ZWN1dGluZyBjb2RlIHNpbXBseSBkZWNsYXJlcyB0aGF0IHRoZSBjdXJyZW50IHNjb3BlIGlzIGR5bmFtaWMgYnV0IGlmIHlvdSB1c2UgaXRcbiAqIGluc2lkZSB1bnN0YWJsZV9jYWNoZSBpdCBjYW4gc3RpbGwgYmUgY2FjaGVkLiBUaGlzIHR5cGUgb2YgaW5kaWNhdGlvbiBjYW4gYmUgcmVtb3ZlZCBpZiB3ZSBldmVyXG4gKiBtYWtlIHRoZSBkZWZhdWx0IGR5bmFtaWMgdG8gYmVnaW4gd2l0aCBiZWNhdXNlIHRoZSBvbmx5IHdheSB5b3Ugd291bGQgZXZlciBiZSBzdGF0aWMgaXMgaW5zaWRlXG4gKiBhIGNhY2hlIHNjb3BlIHdoaWNoIHRoaXMgaW5kaWNhdGlvbiBkb2VzIG5vdCBhZmZlY3QuXG4gKlxuICogVGhlIHNlY29uZCBpcyBhbiBpbmRpY2F0aW9uIHRoYXQgYSBkeW5hbWljIGRhdGEgc291cmNlIHdhcyByZWFkLiBUaGlzIGlzIGEgc3Ryb25nZXIgZm9ybSBvZiBkeW5hbWljXG4gKiBiZWNhdXNlIGl0IG1lYW5zIHRoYXQgaXQgaXMgaW5hcHByb3ByaWF0ZSB0byBjYWNoZSB0aGlzIGF0IGFsbC4gdXNpbmcgYSBkeW5hbWljIGRhdGEgc291cmNlIGluc2lkZVxuICogdW5zdGFibGVfY2FjaGUgc2hvdWxkIGVycm9yLiBJZiB5b3Ugd2FudCB0byB1c2Ugc29tZSBkeW5hbWljIGRhdGEgaW5zaWRlIHVuc3RhYmxlX2NhY2hlIHlvdSBzaG91bGRcbiAqIHJlYWQgdGhhdCBkYXRhIG91dHNpZGUgdGhlIGNhY2hlIGFuZCBwYXNzIGl0IGluIGFzIGFuIGFyZ3VtZW50IHRvIHRoZSBjYWNoZWQgZnVuY3Rpb24uXG4gKi9cblxuaW1wb3J0IHR5cGUgeyBXb3JrU3RvcmUgfSBmcm9tICcuLi9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbCdcbmltcG9ydCB0eXBlIHtcbiAgV29ya1VuaXRTdG9yZSxcbiAgUmVxdWVzdFN0b3JlLFxuICBQcmVyZW5kZXJTdG9yZUxlZ2FjeSxcbiAgUHJlcmVuZGVyU3RvcmVNb2Rlcm4sXG59IGZyb20gJy4uL2FwcC1yZW5kZXIvd29yay11bml0LWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwnXG5cbi8vIE9uY2UgcG9zdHBvbmUgaXMgaW4gc3RhYmxlIHdlIHNob3VsZCBzd2l0Y2ggdG8gaW1wb3J0aW5nIHRoZSBwb3N0cG9uZSBleHBvcnQgZGlyZWN0bHlcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuaW1wb3J0IHsgRHluYW1pY1NlcnZlckVycm9yIH0gZnJvbSAnLi4vLi4vY2xpZW50L2NvbXBvbmVudHMvaG9va3Mtc2VydmVyLWNvbnRleHQnXG5pbXBvcnQgeyBTdGF0aWNHZW5CYWlsb3V0RXJyb3IgfSBmcm9tICcuLi8uLi9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1iYWlsb3V0J1xuaW1wb3J0IHsgd29ya1VuaXRBc3luY1N0b3JhZ2UgfSBmcm9tICcuL3dvcmstdW5pdC1hc3luYy1zdG9yYWdlLmV4dGVybmFsJ1xuaW1wb3J0IHsgd29ya0FzeW5jU3RvcmFnZSB9IGZyb20gJy4uL2FwcC1yZW5kZXIvd29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsJ1xuaW1wb3J0IHsgbWFrZUhhbmdpbmdQcm9taXNlIH0gZnJvbSAnLi4vZHluYW1pYy1yZW5kZXJpbmctdXRpbHMnXG5pbXBvcnQge1xuICBNRVRBREFUQV9CT1VOREFSWV9OQU1FLFxuICBWSUVXUE9SVF9CT1VOREFSWV9OQU1FLFxuICBPVVRMRVRfQk9VTkRBUllfTkFNRSxcbn0gZnJvbSAnLi4vLi4vbGliL21ldGFkYXRhL21ldGFkYXRhLWNvbnN0YW50cydcbmltcG9ydCB7IHNjaGVkdWxlT25OZXh0VGljayB9IGZyb20gJy4uLy4uL2xpYi9zY2hlZHVsZXInXG5cbmNvbnN0IGhhc1Bvc3Rwb25lID0gdHlwZW9mIFJlYWN0LnVuc3RhYmxlX3Bvc3Rwb25lID09PSAnZnVuY3Rpb24nXG5cbmV4cG9ydCB0eXBlIER5bmFtaWNBY2Nlc3MgPSB7XG4gIC8qKlxuICAgKiBJZiBkZWJ1Z2dpbmcsIHRoaXMgd2lsbCBjb250YWluIHRoZSBzdGFjayB0cmFjZSBvZiB3aGVyZSB0aGUgZHluYW1pYyBhY2Nlc3NcbiAgICogb2NjdXJyZWQuIFRoaXMgaXMgdXNlZCB0byBwcm92aWRlIG1vcmUgaW5mb3JtYXRpb24gdG8gdGhlIHVzZXIgYWJvdXQgd2h5XG4gICAqIHRoZWlyIHBhZ2UgaXMgYmVpbmcgcmVuZGVyZWQgZHluYW1pY2FsbHkuXG4gICAqL1xuICBzdGFjaz86IHN0cmluZ1xuXG4gIC8qKlxuICAgKiBUaGUgZXhwcmVzc2lvbiB0aGF0IHdhcyBhY2Nlc3NlZCBkeW5hbWljYWxseS5cbiAgICovXG4gIGV4cHJlc3Npb246IHN0cmluZ1xufVxuXG4vLyBTdG9yZXMgZHluYW1pYyByZWFzb25zIHVzZWQgZHVyaW5nIGFuIFJTQyByZW5kZXIuXG5leHBvcnQgdHlwZSBEeW5hbWljVHJhY2tpbmdTdGF0ZSA9IHtcbiAgLyoqXG4gICAqIFdoZW4gdHJ1ZSwgc3RhY2sgaW5mb3JtYXRpb24gd2lsbCBhbHNvIGJlIHRyYWNrZWQgZHVyaW5nIGR5bmFtaWMgYWNjZXNzLlxuICAgKi9cbiAgcmVhZG9ubHkgaXNEZWJ1Z0R5bmFtaWNBY2Nlc3NlczogYm9vbGVhbiB8IHVuZGVmaW5lZFxuXG4gIC8qKlxuICAgKiBUaGUgZHluYW1pYyBhY2Nlc3NlcyB0aGF0IG9jY3VycmVkIGR1cmluZyB0aGUgcmVuZGVyLlxuICAgKi9cbiAgcmVhZG9ubHkgZHluYW1pY0FjY2Vzc2VzOiBBcnJheTxEeW5hbWljQWNjZXNzPlxuXG4gIHN5bmNEeW5hbWljRXJyb3JXaXRoU3RhY2s6IG51bGwgfCBFcnJvclxufVxuXG4vLyBTdG9yZXMgZHluYW1pYyByZWFzb25zIHVzZWQgZHVyaW5nIGFuIFNTUiByZW5kZXIuXG5leHBvcnQgdHlwZSBEeW5hbWljVmFsaWRhdGlvblN0YXRlID0ge1xuICBoYXNTdXNwZW5zZUFib3ZlQm9keTogYm9vbGVhblxuICBoYXNEeW5hbWljTWV0YWRhdGE6IGJvb2xlYW5cbiAgaGFzRHluYW1pY1ZpZXdwb3J0OiBib29sZWFuXG4gIGhhc0FsbG93ZWREeW5hbWljOiBib29sZWFuXG4gIGR5bmFtaWNFcnJvcnM6IEFycmF5PEVycm9yPlxufVxuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlRHluYW1pY1RyYWNraW5nU3RhdGUoXG4gIGlzRGVidWdEeW5hbWljQWNjZXNzZXM6IGJvb2xlYW4gfCB1bmRlZmluZWRcbik6IER5bmFtaWNUcmFja2luZ1N0YXRlIHtcbiAgcmV0dXJuIHtcbiAgICBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzLFxuICAgIGR5bmFtaWNBY2Nlc3NlczogW10sXG4gICAgc3luY0R5bmFtaWNFcnJvcldpdGhTdGFjazogbnVsbCxcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlRHluYW1pY1ZhbGlkYXRpb25TdGF0ZSgpOiBEeW5hbWljVmFsaWRhdGlvblN0YXRlIHtcbiAgcmV0dXJuIHtcbiAgICBoYXNTdXNwZW5zZUFib3ZlQm9keTogZmFsc2UsXG4gICAgaGFzRHluYW1pY01ldGFkYXRhOiBmYWxzZSxcbiAgICBoYXNEeW5hbWljVmlld3BvcnQ6IGZhbHNlLFxuICAgIGhhc0FsbG93ZWREeW5hbWljOiBmYWxzZSxcbiAgICBkeW5hbWljRXJyb3JzOiBbXSxcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0Rmlyc3REeW5hbWljUmVhc29uKFxuICB0cmFja2luZ1N0YXRlOiBEeW5hbWljVHJhY2tpbmdTdGF0ZVxuKTogdW5kZWZpbmVkIHwgc3RyaW5nIHtcbiAgcmV0dXJuIHRyYWNraW5nU3RhdGUuZHluYW1pY0FjY2Vzc2VzWzBdPy5leHByZXNzaW9uXG59XG5cbi8qKlxuICogVGhpcyBmdW5jdGlvbiBjb21tdW5pY2F0ZXMgdGhhdCB0aGUgY3VycmVudCBzY29wZSBzaG91bGQgYmUgdHJlYXRlZCBhcyBkeW5hbWljLlxuICpcbiAqIEluIG1vc3QgY2FzZXMgdGhpcyBmdW5jdGlvbiBpcyBhIG5vLW9wIGJ1dCBpZiBjYWxsZWQgZHVyaW5nXG4gKiBhIFBQUiBwcmVyZW5kZXIgaXQgd2lsbCBwb3N0cG9uZSB0aGUgY3VycmVudCBzdWItdHJlZSBhbmQgY2FsbGluZ1xuICogaXQgZHVyaW5nIGEgbm9ybWFsIHByZXJlbmRlciB3aWxsIGNhdXNlIHRoZSBlbnRpcmUgcHJlcmVuZGVyIHRvIGFib3J0XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtYXJrQ3VycmVudFNjb3BlQXNEeW5hbWljKFxuICBzdG9yZTogV29ya1N0b3JlLFxuICB3b3JrVW5pdFN0b3JlOiB1bmRlZmluZWQgfCBFeGNsdWRlPFdvcmtVbml0U3RvcmUsIFByZXJlbmRlclN0b3JlTW9kZXJuPixcbiAgZXhwcmVzc2lvbjogc3RyaW5nXG4pOiB2b2lkIHtcbiAgaWYgKHdvcmtVbml0U3RvcmUpIHtcbiAgICBpZiAoXG4gICAgICB3b3JrVW5pdFN0b3JlLnR5cGUgPT09ICdjYWNoZScgfHxcbiAgICAgIHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ3Vuc3RhYmxlLWNhY2hlJ1xuICAgICkge1xuICAgICAgLy8gaW5zaWRlIGNhY2hlIHNjb3BlcyBtYXJraW5nIGEgc2NvcGUgYXMgZHluYW1pYyBoYXMgbm8gZWZmZWN0IGJlY2F1c2UgdGhlIG91dGVyIGNhY2hlIHNjb3BlXG4gICAgICAvLyBjcmVhdGVzIGEgY2FjaGUgYm91bmRhcnkuIFRoaXMgaXMgc3VidGx5IGRpZmZlcmVudCBmcm9tIHJlYWRpbmcgYSBkeW5hbWljIGRhdGEgc291cmNlIHdoaWNoIGlzXG4gICAgICAvLyBmb3JiaWRkZW4gaW5zaWRlIGEgY2FjaGUgc2NvcGUuXG4gICAgICByZXR1cm5cbiAgICB9XG4gIH1cblxuICAvLyBJZiB3ZSdyZSBmb3JjaW5nIGR5bmFtaWMgcmVuZGVyaW5nIG9yIHdlJ3JlIGZvcmNpbmcgc3RhdGljIHJlbmRlcmluZywgd2VcbiAgLy8gZG9uJ3QgbmVlZCB0byBkbyBhbnl0aGluZyBoZXJlIGJlY2F1c2UgdGhlIGVudGlyZSBwYWdlIGlzIGFscmVhZHkgZHluYW1pY1xuICAvLyBvciBpdCdzIHN0YXRpYyBhbmQgaXQgc2hvdWxkIG5vdCB0aHJvdyBvciBwb3N0cG9uZSBoZXJlLlxuICBpZiAoc3RvcmUuZm9yY2VEeW5hbWljIHx8IHN0b3JlLmZvcmNlU3RhdGljKSByZXR1cm5cblxuICBpZiAoc3RvcmUuZHluYW1pY1Nob3VsZEVycm9yKSB7XG4gICAgdGhyb3cgbmV3IFN0YXRpY0dlbkJhaWxvdXRFcnJvcihcbiAgICAgIGBSb3V0ZSAke3N0b3JlLnJvdXRlfSB3aXRoIFxcYGR5bmFtaWMgPSBcImVycm9yXCJcXGAgY291bGRuJ3QgYmUgcmVuZGVyZWQgc3RhdGljYWxseSBiZWNhdXNlIGl0IHVzZWQgXFxgJHtleHByZXNzaW9ufVxcYC4gU2VlIG1vcmUgaW5mbyBoZXJlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9yZW5kZXJpbmcvc3RhdGljLWFuZC1keW5hbWljI2R5bmFtaWMtcmVuZGVyaW5nYFxuICAgIClcbiAgfVxuXG4gIGlmICh3b3JrVW5pdFN0b3JlKSB7XG4gICAgaWYgKHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ3ByZXJlbmRlci1wcHInKSB7XG4gICAgICBwb3N0cG9uZVdpdGhUcmFja2luZyhcbiAgICAgICAgc3RvcmUucm91dGUsXG4gICAgICAgIGV4cHJlc3Npb24sXG4gICAgICAgIHdvcmtVbml0U3RvcmUuZHluYW1pY1RyYWNraW5nXG4gICAgICApXG4gICAgfSBlbHNlIGlmICh3b3JrVW5pdFN0b3JlLnR5cGUgPT09ICdwcmVyZW5kZXItbGVnYWN5Jykge1xuICAgICAgd29ya1VuaXRTdG9yZS5yZXZhbGlkYXRlID0gMFxuXG4gICAgICAvLyBXZSBhcmVuJ3QgcHJlcmVuZGVyaW5nIGJ1dCB3ZSBhcmUgZ2VuZXJhdGluZyBhIHN0YXRpYyBwYWdlLiBXZSBuZWVkIHRvIGJhaWwgb3V0IG9mIHN0YXRpYyBnZW5lcmF0aW9uXG4gICAgICBjb25zdCBlcnIgPSBuZXcgRHluYW1pY1NlcnZlckVycm9yKFxuICAgICAgICBgUm91dGUgJHtzdG9yZS5yb3V0ZX0gY291bGRuJ3QgYmUgcmVuZGVyZWQgc3RhdGljYWxseSBiZWNhdXNlIGl0IHVzZWQgJHtleHByZXNzaW9ufS4gU2VlIG1vcmUgaW5mbyBoZXJlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9keW5hbWljLXNlcnZlci1lcnJvcmBcbiAgICAgIClcbiAgICAgIHN0b3JlLmR5bmFtaWNVc2FnZURlc2NyaXB0aW9uID0gZXhwcmVzc2lvblxuICAgICAgc3RvcmUuZHluYW1pY1VzYWdlU3RhY2sgPSBlcnIuc3RhY2tcblxuICAgICAgdGhyb3cgZXJyXG4gICAgfSBlbHNlIGlmIChcbiAgICAgIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnICYmXG4gICAgICB3b3JrVW5pdFN0b3JlICYmXG4gICAgICB3b3JrVW5pdFN0b3JlLnR5cGUgPT09ICdyZXF1ZXN0J1xuICAgICkge1xuICAgICAgd29ya1VuaXRTdG9yZS51c2VkRHluYW1pYyA9IHRydWVcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBUaGlzIGZ1bmN0aW9uIGNvbW11bmljYXRlcyB0aGF0IHNvbWUgZHluYW1pYyBwYXRoIHBhcmFtZXRlciB3YXMgcmVhZC4gVGhpc1xuICogZGlmZmVycyBmcm9tIHRoZSBtb3JlIGdlbmVyYWwgYHRyYWNrRHluYW1pY0RhdGFBY2Nlc3NlZGAgaW4gdGhhdCBpdCBpcyB3aWxsXG4gKiBub3QgZXJyb3Igd2hlbiBgZHluYW1pYyA9IFwiZXJyb3JcImAgaXMgc2V0LlxuICpcbiAqIEBwYXJhbSBzdG9yZSBUaGUgc3RhdGljIGdlbmVyYXRpb24gc3RvcmVcbiAqIEBwYXJhbSBleHByZXNzaW9uIFRoZSBleHByZXNzaW9uIHRoYXQgd2FzIGFjY2Vzc2VkIGR5bmFtaWNhbGx5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0cmFja0ZhbGxiYWNrUGFyYW1BY2Nlc3NlZChcbiAgc3RvcmU6IFdvcmtTdG9yZSxcbiAgZXhwcmVzc2lvbjogc3RyaW5nXG4pOiB2b2lkIHtcbiAgY29uc3QgcHJlcmVuZGVyU3RvcmUgPSB3b3JrVW5pdEFzeW5jU3RvcmFnZS5nZXRTdG9yZSgpXG4gIGlmICghcHJlcmVuZGVyU3RvcmUgfHwgcHJlcmVuZGVyU3RvcmUudHlwZSAhPT0gJ3ByZXJlbmRlci1wcHInKSByZXR1cm5cblxuICBwb3N0cG9uZVdpdGhUcmFja2luZyhzdG9yZS5yb3V0ZSwgZXhwcmVzc2lvbiwgcHJlcmVuZGVyU3RvcmUuZHluYW1pY1RyYWNraW5nKVxufVxuXG4vKipcbiAqIFRoaXMgZnVuY3Rpb24gaXMgbWVhbnQgdG8gYmUgdXNlZCB3aGVuIHByZXJlbmRlcmluZyB3aXRob3V0IGR5bmFtaWNJTyBvciBQUFIuXG4gKiBXaGVuIGNhbGxlZCBkdXJpbmcgYSBidWlsZCBpdCB3aWxsIGNhdXNlIE5leHQuanMgdG8gY29uc2lkZXIgdGhlIHJvdXRlIGFzIGR5bmFtaWMuXG4gKlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0aHJvd1RvSW50ZXJydXB0U3RhdGljR2VuZXJhdGlvbihcbiAgZXhwcmVzc2lvbjogc3RyaW5nLFxuICBzdG9yZTogV29ya1N0b3JlLFxuICBwcmVyZW5kZXJTdG9yZTogUHJlcmVuZGVyU3RvcmVMZWdhY3lcbik6IG5ldmVyIHtcbiAgLy8gV2UgYXJlbid0IHByZXJlbmRlcmluZyBidXQgd2UgYXJlIGdlbmVyYXRpbmcgYSBzdGF0aWMgcGFnZS4gV2UgbmVlZCB0byBiYWlsIG91dCBvZiBzdGF0aWMgZ2VuZXJhdGlvblxuICBjb25zdCBlcnIgPSBuZXcgRHluYW1pY1NlcnZlckVycm9yKFxuICAgIGBSb3V0ZSAke3N0b3JlLnJvdXRlfSBjb3VsZG4ndCBiZSByZW5kZXJlZCBzdGF0aWNhbGx5IGJlY2F1c2UgaXQgdXNlZCBcXGAke2V4cHJlc3Npb259XFxgLiBTZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2R5bmFtaWMtc2VydmVyLWVycm9yYFxuICApXG5cbiAgcHJlcmVuZGVyU3RvcmUucmV2YWxpZGF0ZSA9IDBcblxuICBzdG9yZS5keW5hbWljVXNhZ2VEZXNjcmlwdGlvbiA9IGV4cHJlc3Npb25cbiAgc3RvcmUuZHluYW1pY1VzYWdlU3RhY2sgPSBlcnIuc3RhY2tcblxuICB0aHJvdyBlcnJcbn1cblxuLyoqXG4gKiBUaGlzIGZ1bmN0aW9uIHNob3VsZCBiZSB1c2VkIHRvIHRyYWNrIHdoZXRoZXIgc29tZXRoaW5nIGR5bmFtaWMgaGFwcGVuZWQgZXZlbiB3aGVuXG4gKiB3ZSBhcmUgaW4gYSBkeW5hbWljIHJlbmRlci4gVGhpcyBpcyB1c2VmdWwgZm9yIERldiB3aGVyZSBhbGwgcmVuZGVycyBhcmUgZHluYW1pYyBidXRcbiAqIHdlIHN0aWxsIHRyYWNrIHdoZXRoZXIgZHluYW1pYyBBUElzIHdlcmUgYWNjZXNzZWQgZm9yIGhlbHBmdWwgbWVzc2FnaW5nXG4gKlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0cmFja0R5bmFtaWNEYXRhSW5EeW5hbWljUmVuZGVyKFxuICBfc3RvcmU6IFdvcmtTdG9yZSxcbiAgd29ya1VuaXRTdG9yZTogdm9pZCB8IFdvcmtVbml0U3RvcmVcbikge1xuICBpZiAod29ya1VuaXRTdG9yZSkge1xuICAgIGlmIChcbiAgICAgIHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ2NhY2hlJyB8fFxuICAgICAgd29ya1VuaXRTdG9yZS50eXBlID09PSAndW5zdGFibGUtY2FjaGUnXG4gICAgKSB7XG4gICAgICAvLyBpbnNpZGUgY2FjaGUgc2NvcGVzIG1hcmtpbmcgYSBzY29wZSBhcyBkeW5hbWljIGhhcyBubyBlZmZlY3QgYmVjYXVzZSB0aGUgb3V0ZXIgY2FjaGUgc2NvcGVcbiAgICAgIC8vIGNyZWF0ZXMgYSBjYWNoZSBib3VuZGFyeS4gVGhpcyBpcyBzdWJ0bHkgZGlmZmVyZW50IGZyb20gcmVhZGluZyBhIGR5bmFtaWMgZGF0YSBzb3VyY2Ugd2hpY2ggaXNcbiAgICAgIC8vIGZvcmJpZGRlbiBpbnNpZGUgYSBjYWNoZSBzY29wZS5cbiAgICAgIHJldHVyblxuICAgIH1cbiAgICAvLyBUT0RPOiBpdCBtYWtlcyBubyBzZW5zZSB0byBoYXZlIHRoZXNlIHdvcmsgdW5pdCBzdG9yZSB0eXBlcyBkdXJpbmcgYSBkZXYgcmVuZGVyLlxuICAgIGlmIChcbiAgICAgIHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ3ByZXJlbmRlcicgfHxcbiAgICAgIHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ3ByZXJlbmRlci1jbGllbnQnIHx8XG4gICAgICB3b3JrVW5pdFN0b3JlLnR5cGUgPT09ICdwcmVyZW5kZXItbGVnYWN5J1xuICAgICkge1xuICAgICAgd29ya1VuaXRTdG9yZS5yZXZhbGlkYXRlID0gMFxuICAgIH1cbiAgICBpZiAoXG4gICAgICBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJlxuICAgICAgd29ya1VuaXRTdG9yZS50eXBlID09PSAncmVxdWVzdCdcbiAgICApIHtcbiAgICAgIHdvcmtVbml0U3RvcmUudXNlZER5bmFtaWMgPSB0cnVlXG4gICAgfVxuICB9XG59XG5cbmZ1bmN0aW9uIGFib3J0T25TeW5jaHJvbm91c0R5bmFtaWNEYXRhQWNjZXNzKFxuICByb3V0ZTogc3RyaW5nLFxuICBleHByZXNzaW9uOiBzdHJpbmcsXG4gIHByZXJlbmRlclN0b3JlOiBQcmVyZW5kZXJTdG9yZU1vZGVyblxuKTogdm9pZCB7XG4gIGNvbnN0IHJlYXNvbiA9IGBSb3V0ZSAke3JvdXRlfSBuZWVkcyB0byBiYWlsIG91dCBvZiBwcmVyZW5kZXJpbmcgYXQgdGhpcyBwb2ludCBiZWNhdXNlIGl0IHVzZWQgJHtleHByZXNzaW9ufS5gXG5cbiAgY29uc3QgZXJyb3IgPSBjcmVhdGVQcmVyZW5kZXJJbnRlcnJ1cHRlZEVycm9yKHJlYXNvbilcblxuICBwcmVyZW5kZXJTdG9yZS5jb250cm9sbGVyLmFib3J0KGVycm9yKVxuXG4gIGNvbnN0IGR5bmFtaWNUcmFja2luZyA9IHByZXJlbmRlclN0b3JlLmR5bmFtaWNUcmFja2luZ1xuICBpZiAoZHluYW1pY1RyYWNraW5nKSB7XG4gICAgZHluYW1pY1RyYWNraW5nLmR5bmFtaWNBY2Nlc3Nlcy5wdXNoKHtcbiAgICAgIC8vIFdoZW4gd2UgYXJlbid0IGRlYnVnZ2luZywgd2UgZG9uJ3QgbmVlZCB0byBjcmVhdGUgYW5vdGhlciBlcnJvciBmb3IgdGhlXG4gICAgICAvLyBzdGFjayB0cmFjZS5cbiAgICAgIHN0YWNrOiBkeW5hbWljVHJhY2tpbmcuaXNEZWJ1Z0R5bmFtaWNBY2Nlc3Nlc1xuICAgICAgICA/IG5ldyBFcnJvcigpLnN0YWNrXG4gICAgICAgIDogdW5kZWZpbmVkLFxuICAgICAgZXhwcmVzc2lvbixcbiAgICB9KVxuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBhYm9ydE9uU3luY2hyb25vdXNQbGF0Zm9ybUlPQWNjZXNzKFxuICByb3V0ZTogc3RyaW5nLFxuICBleHByZXNzaW9uOiBzdHJpbmcsXG4gIGVycm9yV2l0aFN0YWNrOiBFcnJvcixcbiAgcHJlcmVuZGVyU3RvcmU6IFByZXJlbmRlclN0b3JlTW9kZXJuXG4pOiB2b2lkIHtcbiAgY29uc3QgZHluYW1pY1RyYWNraW5nID0gcHJlcmVuZGVyU3RvcmUuZHluYW1pY1RyYWNraW5nXG4gIGFib3J0T25TeW5jaHJvbm91c0R5bmFtaWNEYXRhQWNjZXNzKHJvdXRlLCBleHByZXNzaW9uLCBwcmVyZW5kZXJTdG9yZSlcbiAgLy8gSXQgaXMgaW1wb3J0YW50IHRoYXQgd2Ugc2V0IHRoaXMgdHJhY2tpbmcgdmFsdWUgYWZ0ZXIgYWJvcnRpbmcuIEFib3J0cyBhcmUgZXhlY3V0ZWRcbiAgLy8gc3luY2hyb25vdXNseSBleGNlcHQgZm9yIHRoZSBjYXNlIHdoZXJlIHlvdSBhYm9ydCBkdXJpbmcgcmVuZGVyIGl0c2VsZi4gQnkgc2V0dGluZyB0aGlzXG4gIC8vIHZhbHVlIGxhdGUgd2UgY2FuIHVzZSBpdCB0byBkZXRlcm1pbmUgaWYgYW55IG9mIHRoZSBhYm9ydGVkIHRhc2tzIGFyZSB0aGUgdGFzayB0aGF0XG4gIC8vIGNhbGxlZCB0aGUgc3luYyBJTyBleHByZXNzaW9uIGluIHRoZSBmaXJzdCBwbGFjZS5cbiAgaWYgKGR5bmFtaWNUcmFja2luZykge1xuICAgIGlmIChkeW5hbWljVHJhY2tpbmcuc3luY0R5bmFtaWNFcnJvcldpdGhTdGFjayA9PT0gbnVsbCkge1xuICAgICAgZHluYW1pY1RyYWNraW5nLnN5bmNEeW5hbWljRXJyb3JXaXRoU3RhY2sgPSBlcnJvcldpdGhTdGFja1xuICAgIH1cbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdHJhY2tTeW5jaHJvbm91c1BsYXRmb3JtSU9BY2Nlc3NJbkRldihcbiAgcmVxdWVzdFN0b3JlOiBSZXF1ZXN0U3RvcmVcbik6IHZvaWQge1xuICAvLyBXZSBkb24ndCBhY3R1YWxseSBoYXZlIGEgY29udHJvbGxlciB0byBhYm9ydCBidXQgd2UgZG8gdGhlIHNlbWFudGljIGVxdWl2YWxlbnQgYnlcbiAgLy8gYWR2YW5jaW5nIHRoZSByZXF1ZXN0IHN0b3JlIG91dCBvZiBwcmVyZW5kZXIgbW9kZVxuICByZXF1ZXN0U3RvcmUucHJlcmVuZGVyUGhhc2UgPSBmYWxzZVxufVxuXG4vKipcbiAqIHVzZSB0aGlzIGZ1bmN0aW9uIHdoZW4gcHJlcmVuZGVyaW5nIHdpdGggZHluYW1pY0lPLiBJZiB3ZSBhcmUgZG9pbmcgYVxuICogcHJvc3BlY3RpdmUgcHJlcmVuZGVyIHdlIGRvbid0IGFjdHVhbGx5IGFib3J0IGJlY2F1c2Ugd2Ugd2FudCB0byBkaXNjb3ZlclxuICogYWxsIGNhY2hlcyBmb3IgdGhlIHNoZWxsLiBJZiB0aGlzIGlzIHRoZSBhY3R1YWwgcHJlcmVuZGVyIHdlIGRvIGFib3J0LlxuICpcbiAqIFRoaXMgZnVuY3Rpb24gYWNjZXB0cyBhIHByZXJlbmRlclN0b3JlIGJ1dCB0aGUgY2FsbGVyIHNob3VsZCBlbnN1cmUgd2UncmVcbiAqIGFjdHVhbGx5IHJ1bm5pbmcgaW4gZHluYW1pY0lPIG1vZGUuXG4gKlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBhYm9ydEFuZFRocm93T25TeW5jaHJvbm91c1JlcXVlc3REYXRhQWNjZXNzKFxuICByb3V0ZTogc3RyaW5nLFxuICBleHByZXNzaW9uOiBzdHJpbmcsXG4gIGVycm9yV2l0aFN0YWNrOiBFcnJvcixcbiAgcHJlcmVuZGVyU3RvcmU6IFByZXJlbmRlclN0b3JlTW9kZXJuXG4pOiBuZXZlciB7XG4gIGNvbnN0IHByZXJlbmRlclNpZ25hbCA9IHByZXJlbmRlclN0b3JlLmNvbnRyb2xsZXIuc2lnbmFsXG4gIGlmIChwcmVyZW5kZXJTaWduYWwuYWJvcnRlZCA9PT0gZmFsc2UpIHtcbiAgICAvLyBUT0RPIGl0IHdvdWxkIGJlIGJldHRlciB0byBtb3ZlIHRoaXMgYWJvcnRlZCBjaGVjayBpbnRvIHRoZSBjYWxsc2l0ZSBzbyB3ZSBjYW4gYXZvaWQgbWFraW5nXG4gICAgLy8gdGhlIGVycm9yIG9iamVjdCB3aGVuIGl0IGlzbid0IHJlbGV2YW50IHRvIHRoZSBhYm9ydGluZyBvZiB0aGUgcHJlcmVuZGVyIGhvd2V2ZXJcbiAgICAvLyBzaW5jZSB3ZSBuZWVkIHRoZSB0aHJvdyBzZW1hbnRpY3MgcmVnYXJkbGVzcyBvZiB3aGV0aGVyIHdlIGFib3J0IGl0IGlzIGVhc2llciB0byBsYW5kXG4gICAgLy8gdGhpcyB3YXkuIFNlZSBob3cgdGhpcyB3YXMgaGFuZGxlZCB3aXRoIGBhYm9ydE9uU3luY2hyb25vdXNQbGF0Zm9ybUlPQWNjZXNzYCBmb3IgYSBjbG9zZXJcbiAgICAvLyB0byBpZGVhbCBpbXBsZW1lbnRhdGlvblxuICAgIGFib3J0T25TeW5jaHJvbm91c0R5bmFtaWNEYXRhQWNjZXNzKHJvdXRlLCBleHByZXNzaW9uLCBwcmVyZW5kZXJTdG9yZSlcbiAgICAvLyBJdCBpcyBpbXBvcnRhbnQgdGhhdCB3ZSBzZXQgdGhpcyB0cmFja2luZyB2YWx1ZSBhZnRlciBhYm9ydGluZy4gQWJvcnRzIGFyZSBleGVjdXRlZFxuICAgIC8vIHN5bmNocm9ub3VzbHkgZXhjZXB0IGZvciB0aGUgY2FzZSB3aGVyZSB5b3UgYWJvcnQgZHVyaW5nIHJlbmRlciBpdHNlbGYuIEJ5IHNldHRpbmcgdGhpc1xuICAgIC8vIHZhbHVlIGxhdGUgd2UgY2FuIHVzZSBpdCB0byBkZXRlcm1pbmUgaWYgYW55IG9mIHRoZSBhYm9ydGVkIHRhc2tzIGFyZSB0aGUgdGFzayB0aGF0XG4gICAgLy8gY2FsbGVkIHRoZSBzeW5jIElPIGV4cHJlc3Npb24gaW4gdGhlIGZpcnN0IHBsYWNlLlxuICAgIGNvbnN0IGR5bmFtaWNUcmFja2luZyA9IHByZXJlbmRlclN0b3JlLmR5bmFtaWNUcmFja2luZ1xuICAgIGlmIChkeW5hbWljVHJhY2tpbmcpIHtcbiAgICAgIGlmIChkeW5hbWljVHJhY2tpbmcuc3luY0R5bmFtaWNFcnJvcldpdGhTdGFjayA9PT0gbnVsbCkge1xuICAgICAgICBkeW5hbWljVHJhY2tpbmcuc3luY0R5bmFtaWNFcnJvcldpdGhTdGFjayA9IGVycm9yV2l0aFN0YWNrXG4gICAgICB9XG4gICAgfVxuICB9XG4gIHRocm93IGNyZWF0ZVByZXJlbmRlckludGVycnVwdGVkRXJyb3IoXG4gICAgYFJvdXRlICR7cm91dGV9IG5lZWRzIHRvIGJhaWwgb3V0IG9mIHByZXJlbmRlcmluZyBhdCB0aGlzIHBvaW50IGJlY2F1c2UgaXQgdXNlZCAke2V4cHJlc3Npb259LmBcbiAgKVxufVxuXG4vLyBGb3Igbm93IHRoZXNlIGltcGxlbWVudGF0aW9ucyBhcmUgdGhlIHNhbWUgc28gd2UganVzdCByZWV4cG9ydFxuZXhwb3J0IGNvbnN0IHRyYWNrU3luY2hyb25vdXNSZXF1ZXN0RGF0YUFjY2Vzc0luRGV2ID1cbiAgdHJhY2tTeW5jaHJvbm91c1BsYXRmb3JtSU9BY2Nlc3NJbkRldlxuXG4vKipcbiAqIFRoaXMgY29tcG9uZW50IHdpbGwgY2FsbCBgUmVhY3QucG9zdHBvbmVgIHRoYXQgdGhyb3dzIHRoZSBwb3N0cG9uZWQgZXJyb3IuXG4gKi9cbnR5cGUgUG9zdHBvbmVQcm9wcyA9IHtcbiAgcmVhc29uOiBzdHJpbmdcbiAgcm91dGU6IHN0cmluZ1xufVxuZXhwb3J0IGZ1bmN0aW9uIFBvc3Rwb25lKHsgcmVhc29uLCByb3V0ZSB9OiBQb3N0cG9uZVByb3BzKTogbmV2ZXIge1xuICBjb25zdCBwcmVyZW5kZXJTdG9yZSA9IHdvcmtVbml0QXN5bmNTdG9yYWdlLmdldFN0b3JlKClcbiAgY29uc3QgZHluYW1pY1RyYWNraW5nID1cbiAgICBwcmVyZW5kZXJTdG9yZSAmJiBwcmVyZW5kZXJTdG9yZS50eXBlID09PSAncHJlcmVuZGVyLXBwcidcbiAgICAgID8gcHJlcmVuZGVyU3RvcmUuZHluYW1pY1RyYWNraW5nXG4gICAgICA6IG51bGxcbiAgcG9zdHBvbmVXaXRoVHJhY2tpbmcocm91dGUsIHJlYXNvbiwgZHluYW1pY1RyYWNraW5nKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gcG9zdHBvbmVXaXRoVHJhY2tpbmcoXG4gIHJvdXRlOiBzdHJpbmcsXG4gIGV4cHJlc3Npb246IHN0cmluZyxcbiAgZHluYW1pY1RyYWNraW5nOiBudWxsIHwgRHluYW1pY1RyYWNraW5nU3RhdGVcbik6IG5ldmVyIHtcbiAgYXNzZXJ0UG9zdHBvbmUoKVxuICBpZiAoZHluYW1pY1RyYWNraW5nKSB7XG4gICAgZHluYW1pY1RyYWNraW5nLmR5bmFtaWNBY2Nlc3Nlcy5wdXNoKHtcbiAgICAgIC8vIFdoZW4gd2UgYXJlbid0IGRlYnVnZ2luZywgd2UgZG9uJ3QgbmVlZCB0byBjcmVhdGUgYW5vdGhlciBlcnJvciBmb3IgdGhlXG4gICAgICAvLyBzdGFjayB0cmFjZS5cbiAgICAgIHN0YWNrOiBkeW5hbWljVHJhY2tpbmcuaXNEZWJ1Z0R5bmFtaWNBY2Nlc3Nlc1xuICAgICAgICA/IG5ldyBFcnJvcigpLnN0YWNrXG4gICAgICAgIDogdW5kZWZpbmVkLFxuICAgICAgZXhwcmVzc2lvbixcbiAgICB9KVxuICB9XG5cbiAgUmVhY3QudW5zdGFibGVfcG9zdHBvbmUoY3JlYXRlUG9zdHBvbmVSZWFzb24ocm91dGUsIGV4cHJlc3Npb24pKVxufVxuXG5mdW5jdGlvbiBjcmVhdGVQb3N0cG9uZVJlYXNvbihyb3V0ZTogc3RyaW5nLCBleHByZXNzaW9uOiBzdHJpbmcpIHtcbiAgcmV0dXJuIChcbiAgICBgUm91dGUgJHtyb3V0ZX0gbmVlZHMgdG8gYmFpbCBvdXQgb2YgcHJlcmVuZGVyaW5nIGF0IHRoaXMgcG9pbnQgYmVjYXVzZSBpdCB1c2VkICR7ZXhwcmVzc2lvbn0uIGAgK1xuICAgIGBSZWFjdCB0aHJvd3MgdGhpcyBzcGVjaWFsIG9iamVjdCB0byBpbmRpY2F0ZSB3aGVyZS4gSXQgc2hvdWxkIG5vdCBiZSBjYXVnaHQgYnkgYCArXG4gICAgYHlvdXIgb3duIHRyeS9jYXRjaC4gTGVhcm4gbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvcHByLWNhdWdodC1lcnJvcmBcbiAgKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNEeW5hbWljUG9zdHBvbmUoZXJyOiB1bmtub3duKSB7XG4gIGlmIChcbiAgICB0eXBlb2YgZXJyID09PSAnb2JqZWN0JyAmJlxuICAgIGVyciAhPT0gbnVsbCAmJlxuICAgIHR5cGVvZiAoZXJyIGFzIGFueSkubWVzc2FnZSA9PT0gJ3N0cmluZydcbiAgKSB7XG4gICAgcmV0dXJuIGlzRHluYW1pY1Bvc3Rwb25lUmVhc29uKChlcnIgYXMgYW55KS5tZXNzYWdlKVxuICB9XG4gIHJldHVybiBmYWxzZVxufVxuXG5mdW5jdGlvbiBpc0R5bmFtaWNQb3N0cG9uZVJlYXNvbihyZWFzb246IHN0cmluZykge1xuICByZXR1cm4gKFxuICAgIHJlYXNvbi5pbmNsdWRlcyhcbiAgICAgICduZWVkcyB0byBiYWlsIG91dCBvZiBwcmVyZW5kZXJpbmcgYXQgdGhpcyBwb2ludCBiZWNhdXNlIGl0IHVzZWQnXG4gICAgKSAmJlxuICAgIHJlYXNvbi5pbmNsdWRlcyhcbiAgICAgICdMZWFybiBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9wcHItY2F1Z2h0LWVycm9yJ1xuICAgIClcbiAgKVxufVxuXG5pZiAoaXNEeW5hbWljUG9zdHBvbmVSZWFzb24oY3JlYXRlUG9zdHBvbmVSZWFzb24oJyUlJScsICdeXl4nKSkgPT09IGZhbHNlKSB7XG4gIHRocm93IG5ldyBFcnJvcihcbiAgICAnSW52YXJpYW50OiBpc0R5bmFtaWNQb3N0cG9uZSBtaXNpZGVudGlmaWVkIGEgcG9zdHBvbmUgcmVhc29uLiBUaGlzIGlzIGEgYnVnIGluIE5leHQuanMnXG4gIClcbn1cblxuY29uc3QgTkVYVF9QUkVSRU5ERVJfSU5URVJSVVBURUQgPSAnTkVYVF9QUkVSRU5ERVJfSU5URVJSVVBURUQnXG5cbmZ1bmN0aW9uIGNyZWF0ZVByZXJlbmRlckludGVycnVwdGVkRXJyb3IobWVzc2FnZTogc3RyaW5nKTogRXJyb3Ige1xuICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcihtZXNzYWdlKVxuICA7KGVycm9yIGFzIGFueSkuZGlnZXN0ID0gTkVYVF9QUkVSRU5ERVJfSU5URVJSVVBURURcbiAgcmV0dXJuIGVycm9yXG59XG5cbnR5cGUgRGlnZXN0RXJyb3IgPSBFcnJvciAmIHtcbiAgZGlnZXN0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzUHJlcmVuZGVySW50ZXJydXB0ZWRFcnJvcihcbiAgZXJyb3I6IHVua25vd25cbik6IGVycm9yIGlzIERpZ2VzdEVycm9yIHtcbiAgcmV0dXJuIChcbiAgICB0eXBlb2YgZXJyb3IgPT09ICdvYmplY3QnICYmXG4gICAgZXJyb3IgIT09IG51bGwgJiZcbiAgICAoZXJyb3IgYXMgYW55KS5kaWdlc3QgPT09IE5FWFRfUFJFUkVOREVSX0lOVEVSUlVQVEVEICYmXG4gICAgJ25hbWUnIGluIGVycm9yICYmXG4gICAgJ21lc3NhZ2UnIGluIGVycm9yICYmXG4gICAgZXJyb3IgaW5zdGFuY2VvZiBFcnJvclxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBhY2Nlc3NlZER5bmFtaWNEYXRhKFxuICBkeW5hbWljQWNjZXNzZXM6IEFycmF5PER5bmFtaWNBY2Nlc3M+XG4pOiBib29sZWFuIHtcbiAgcmV0dXJuIGR5bmFtaWNBY2Nlc3Nlcy5sZW5ndGggPiAwXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjb25zdW1lRHluYW1pY0FjY2VzcyhcbiAgc2VydmVyRHluYW1pYzogRHluYW1pY1RyYWNraW5nU3RhdGUsXG4gIGNsaWVudER5bmFtaWM6IER5bmFtaWNUcmFja2luZ1N0YXRlXG4pOiBEeW5hbWljVHJhY2tpbmdTdGF0ZVsnZHluYW1pY0FjY2Vzc2VzJ10ge1xuICAvLyBXZSBtdXRhdGUgYmVjYXVzZSB3ZSBvbmx5IGNhbGwgdGhpcyBvbmNlIHdlIGFyZSBubyBsb25nZXIgd3JpdGluZ1xuICAvLyB0byB0aGUgZHluYW1pY1RyYWNraW5nU3RhdGUgYW5kIGl0J3MgbW9yZSBlZmZpY2llbnQgdGhhbiBjcmVhdGluZyBhIG5ld1xuICAvLyBhcnJheS5cbiAgc2VydmVyRHluYW1pYy5keW5hbWljQWNjZXNzZXMucHVzaCguLi5jbGllbnREeW5hbWljLmR5bmFtaWNBY2Nlc3NlcylcbiAgcmV0dXJuIHNlcnZlckR5bmFtaWMuZHluYW1pY0FjY2Vzc2VzXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXREeW5hbWljQVBJQWNjZXNzZXMoXG4gIGR5bmFtaWNBY2Nlc3NlczogQXJyYXk8RHluYW1pY0FjY2Vzcz5cbik6IHN0cmluZ1tdIHtcbiAgcmV0dXJuIGR5bmFtaWNBY2Nlc3Nlc1xuICAgIC5maWx0ZXIoXG4gICAgICAoYWNjZXNzKTogYWNjZXNzIGlzIFJlcXVpcmVkPER5bmFtaWNBY2Nlc3M+ID0+XG4gICAgICAgIHR5cGVvZiBhY2Nlc3Muc3RhY2sgPT09ICdzdHJpbmcnICYmIGFjY2Vzcy5zdGFjay5sZW5ndGggPiAwXG4gICAgKVxuICAgIC5tYXAoKHsgZXhwcmVzc2lvbiwgc3RhY2sgfSkgPT4ge1xuICAgICAgc3RhY2sgPSBzdGFja1xuICAgICAgICAuc3BsaXQoJ1xcbicpXG4gICAgICAgIC8vIFJlbW92ZSB0aGUgXCJFcnJvcjogXCIgcHJlZml4IGZyb20gdGhlIGZpcnN0IGxpbmUgb2YgdGhlIHN0YWNrIHRyYWNlIGFzXG4gICAgICAgIC8vIHdlbGwgYXMgdGhlIGZpcnN0IDQgbGluZXMgb2YgdGhlIHN0YWNrIHRyYWNlIHdoaWNoIGlzIHRoZSBkaXN0YW5jZVxuICAgICAgICAvLyBmcm9tIHRoZSB1c2VyIGNvZGUgYW5kIHRoZSBgbmV3IEVycm9yKCkuc3RhY2tgIGNhbGwuXG4gICAgICAgIC5zbGljZSg0KVxuICAgICAgICAuZmlsdGVyKChsaW5lKSA9PiB7XG4gICAgICAgICAgLy8gRXhjbHVkZSBOZXh0LmpzIGludGVybmFscyBmcm9tIHRoZSBzdGFjayB0cmFjZS5cbiAgICAgICAgICBpZiAobGluZS5pbmNsdWRlcygnbm9kZV9tb2R1bGVzL25leHQvJykpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIEV4Y2x1ZGUgYW5vbnltb3VzIGZ1bmN0aW9ucyBmcm9tIHRoZSBzdGFjayB0cmFjZS5cbiAgICAgICAgICBpZiAobGluZS5pbmNsdWRlcygnICg8YW5vbnltb3VzPiknKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gRXhjbHVkZSBOb2RlLmpzIGludGVybmFscyBmcm9tIHRoZSBzdGFjayB0cmFjZS5cbiAgICAgICAgICBpZiAobGluZS5pbmNsdWRlcygnIChub2RlOicpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgICAgICB9XG5cbiAgICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgICB9KVxuICAgICAgICAuam9pbignXFxuJylcbiAgICAgIHJldHVybiBgRHluYW1pYyBBUEkgVXNhZ2UgRGVidWcgLSAke2V4cHJlc3Npb259OlxcbiR7c3RhY2t9YFxuICAgIH0pXG59XG5cbmZ1bmN0aW9uIGFzc2VydFBvc3Rwb25lKCkge1xuICBpZiAoIWhhc1Bvc3Rwb25lKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgYEludmFyaWFudDogUmVhY3QudW5zdGFibGVfcG9zdHBvbmUgaXMgbm90IGRlZmluZWQuIFRoaXMgc3VnZ2VzdHMgdGhlIHdyb25nIHZlcnNpb24gb2YgUmVhY3Qgd2FzIGxvYWRlZC4gVGhpcyBpcyBhIGJ1ZyBpbiBOZXh0LmpzYFxuICAgIClcbiAgfVxufVxuXG4vKipcbiAqIFRoaXMgaXMgYSBiaXQgb2YgYSBoYWNrIHRvIGFsbG93IHVzIHRvIGFib3J0IGEgcmVuZGVyIHVzaW5nIGEgUG9zdHBvbmUgaW5zdGFuY2UgaW5zdGVhZCBvZiBhbiBFcnJvciB3aGljaCBjaGFuZ2VzIFJlYWN0J3NcbiAqIGFib3J0IHNlbWFudGljcyBzbGlnaHRseS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVBvc3Rwb25lZEFib3J0U2lnbmFsKHJlYXNvbjogc3RyaW5nKTogQWJvcnRTaWduYWwge1xuICBhc3NlcnRQb3N0cG9uZSgpXG4gIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKClcbiAgLy8gV2UgZ2V0IG91ciBoYW5kcyBvbiBhIHBvc3Rwb25lIGluc3RhbmNlIGJ5IGNhbGxpbmcgcG9zdHBvbmUgYW5kIGNhdGNoaW5nIHRoZSB0aHJvd1xuICB0cnkge1xuICAgIFJlYWN0LnVuc3RhYmxlX3Bvc3Rwb25lKHJlYXNvbilcbiAgfSBjYXRjaCAoeDogdW5rbm93bikge1xuICAgIGNvbnRyb2xsZXIuYWJvcnQoeClcbiAgfVxuICByZXR1cm4gY29udHJvbGxlci5zaWduYWxcbn1cblxuLyoqXG4gKiBJbiBhIHByZXJlbmRlciwgd2UgbWF5IGVuZCB1cCB3aXRoIGhhbmdpbmcgUHJvbWlzZXMgYXMgaW5wdXRzIGR1ZSB0aGVtXG4gKiBzdGFsbGluZyBvbiBjb25uZWN0aW9uKCkgb3IgYmVjYXVzZSB0aGV5J3JlIGxvYWRpbmcgZHluYW1pYyBkYXRhLiBJbiB0aGF0XG4gKiBjYXNlIHdlIG5lZWQgdG8gYWJvcnQgdGhlIGVuY29kaW5nIG9mIGFyZ3VtZW50cyBzaW5jZSB0aGV5J2xsIG5ldmVyIGNvbXBsZXRlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlSGFuZ2luZ0lucHV0QWJvcnRTaWduYWwoXG4gIHdvcmtVbml0U3RvcmU6IFByZXJlbmRlclN0b3JlTW9kZXJuXG4pOiBBYm9ydFNpZ25hbCB7XG4gIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKClcblxuICBpZiAod29ya1VuaXRTdG9yZS5jYWNoZVNpZ25hbCkge1xuICAgIC8vIElmIHdlIGhhdmUgYSBjYWNoZVNpZ25hbCBpdCBtZWFucyB3ZSdyZSBpbiBhIHByb3NwZWN0aXZlIHJlbmRlci4gSWYgdGhlIGlucHV0XG4gICAgLy8gd2UncmUgd2FpdGluZyBvbiBpcyBjb21pbmcgZnJvbSBhbm90aGVyIGNhY2hlLCB3ZSBkbyB3YW50IHRvIHdhaXQgZm9yIGl0IHNvIHRoYXRcbiAgICAvLyB3ZSBjYW4gcmVzb2x2ZSB0aGlzIGNhY2hlIGVudHJ5IHRvby5cbiAgICB3b3JrVW5pdFN0b3JlLmNhY2hlU2lnbmFsLmlucHV0UmVhZHkoKS50aGVuKCgpID0+IHtcbiAgICAgIGNvbnRyb2xsZXIuYWJvcnQoKVxuICAgIH0pXG4gIH0gZWxzZSB7XG4gICAgLy8gT3RoZXJ3aXNlIHdlJ3JlIGluIHRoZSBmaW5hbCByZW5kZXIgYW5kIHdlIHNob3VsZCBhbHJlYWR5IGhhdmUgYWxsIG91ciBjYWNoZXNcbiAgICAvLyBmaWxsZWQuIFdlIG1pZ2h0IHN0aWxsIGJlIHdhaXRpbmcgb24gc29tZSBtaWNyb3Rhc2tzIHNvIHdlIHdhaXQgb25lIHRpY2sgYmVmb3JlXG4gICAgLy8gZ2l2aW5nIHVwLiBXaGVuIHdlIGdpdmUgdXAsIHdlIHN0aWxsIHdhbnQgdG8gcmVuZGVyIHRoZSBjb250ZW50IG9mIHRoaXMgY2FjaGVcbiAgICAvLyBhcyBkZWVwbHkgYXMgd2UgY2FuIHNvIHRoYXQgd2UgY2FuIHN1c3BlbmQgYXMgZGVlcGx5IGFzIHBvc3NpYmxlIGluIHRoZSB0cmVlXG4gICAgLy8gb3Igbm90IGF0IGFsbCBpZiB3ZSBkb24ndCBlbmQgdXAgd2FpdGluZyBmb3IgdGhlIGlucHV0LlxuICAgIHNjaGVkdWxlT25OZXh0VGljaygoKSA9PiBjb250cm9sbGVyLmFib3J0KCkpXG4gIH1cblxuICByZXR1cm4gY29udHJvbGxlci5zaWduYWxcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGFubm90YXRlRHluYW1pY0FjY2VzcyhcbiAgZXhwcmVzc2lvbjogc3RyaW5nLFxuICBwcmVyZW5kZXJTdG9yZTogUHJlcmVuZGVyU3RvcmVNb2Rlcm5cbikge1xuICBjb25zdCBkeW5hbWljVHJhY2tpbmcgPSBwcmVyZW5kZXJTdG9yZS5keW5hbWljVHJhY2tpbmdcbiAgaWYgKGR5bmFtaWNUcmFja2luZykge1xuICAgIGR5bmFtaWNUcmFja2luZy5keW5hbWljQWNjZXNzZXMucHVzaCh7XG4gICAgICBzdGFjazogZHluYW1pY1RyYWNraW5nLmlzRGVidWdEeW5hbWljQWNjZXNzZXNcbiAgICAgICAgPyBuZXcgRXJyb3IoKS5zdGFja1xuICAgICAgICA6IHVuZGVmaW5lZCxcbiAgICAgIGV4cHJlc3Npb24sXG4gICAgfSlcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlRHluYW1pY1JvdXRlUGFyYW1zKGV4cHJlc3Npb246IHN0cmluZykge1xuICBjb25zdCB3b3JrU3RvcmUgPSB3b3JrQXN5bmNTdG9yYWdlLmdldFN0b3JlKClcblxuICBpZiAoXG4gICAgd29ya1N0b3JlICYmXG4gICAgd29ya1N0b3JlLmlzU3RhdGljR2VuZXJhdGlvbiAmJlxuICAgIHdvcmtTdG9yZS5mYWxsYmFja1JvdXRlUGFyYW1zICYmXG4gICAgd29ya1N0b3JlLmZhbGxiYWNrUm91dGVQYXJhbXMuc2l6ZSA+IDBcbiAgKSB7XG4gICAgLy8gVGhlcmUgYXJlIGZhbGxiYWNrIHJvdXRlIHBhcmFtcywgd2Ugc2hvdWxkIHRyYWNrIHRoZXNlIGFzIGR5bmFtaWNcbiAgICAvLyBhY2Nlc3Nlcy5cbiAgICBjb25zdCB3b3JrVW5pdFN0b3JlID0gd29ya1VuaXRBc3luY1N0b3JhZ2UuZ2V0U3RvcmUoKVxuICAgIGlmICh3b3JrVW5pdFN0b3JlKSB7XG4gICAgICAvLyBXZSdyZSBwcmVyZW5kZXJpbmcgd2l0aCBkeW5hbWljSU8gb3IgUFBSIG9yIGJvdGhcbiAgICAgIGlmICh3b3JrVW5pdFN0b3JlLnR5cGUgPT09ICdwcmVyZW5kZXItY2xpZW50Jykge1xuICAgICAgICAvLyBXZSBhcmUgaW4gYSBwcmVyZW5kZXIgd2l0aCBkeW5hbWljSU8gc2VtYW50aWNzXG4gICAgICAgIC8vIFdlIGFyZSBnb2luZyB0byBoYW5nIGhlcmUgYW5kIG5ldmVyIHJlc29sdmUuIFRoaXMgd2lsbCBjYXVzZSB0aGUgY3VycmVudGx5XG4gICAgICAgIC8vIHJlbmRlcmluZyBjb21wb25lbnQgdG8gZWZmZWN0aXZlbHkgYmUgYSBkeW5hbWljIGhvbGVcbiAgICAgICAgUmVhY3QudXNlKG1ha2VIYW5naW5nUHJvbWlzZSh3b3JrVW5pdFN0b3JlLnJlbmRlclNpZ25hbCwgZXhwcmVzc2lvbikpXG4gICAgICB9IGVsc2UgaWYgKHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ3ByZXJlbmRlci1wcHInKSB7XG4gICAgICAgIC8vIFdlJ3JlIHByZXJlbmRlcmluZyB3aXRoIFBQUlxuICAgICAgICBwb3N0cG9uZVdpdGhUcmFja2luZyhcbiAgICAgICAgICB3b3JrU3RvcmUucm91dGUsXG4gICAgICAgICAgZXhwcmVzc2lvbixcbiAgICAgICAgICB3b3JrVW5pdFN0b3JlLmR5bmFtaWNUcmFja2luZ1xuICAgICAgICApXG4gICAgICB9IGVsc2UgaWYgKHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ3ByZXJlbmRlci1sZWdhY3knKSB7XG4gICAgICAgIHRocm93VG9JbnRlcnJ1cHRTdGF0aWNHZW5lcmF0aW9uKGV4cHJlc3Npb24sIHdvcmtTdG9yZSwgd29ya1VuaXRTdG9yZSlcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuY29uc3QgaGFzU3VzcGVuc2VSZWdleCA9IC9cXG5cXHMrYXQgU3VzcGVuc2UgXFwoPGFub255bW91cz5cXCkvXG5jb25zdCBoYXNTdXNwZW5zZUFmdGVyQm9keU9ySHRtbFJlZ2V4ID1cbiAgL1xcblxccythdCAoPzpib2R5fGh0bWwpIFxcKDxhbm9ueW1vdXM+XFwpW1xcc1xcU10qP1xcblxccythdCBTdXNwZW5zZSBcXCg8YW5vbnltb3VzPlxcKS9cbmNvbnN0IGhhc01ldGFkYXRhUmVnZXggPSBuZXcgUmVnRXhwKFxuICBgXFxcXG5cXFxccythdCAke01FVEFEQVRBX0JPVU5EQVJZX05BTUV9W1xcXFxuXFxcXHNdYFxuKVxuY29uc3QgaGFzVmlld3BvcnRSZWdleCA9IG5ldyBSZWdFeHAoXG4gIGBcXFxcblxcXFxzK2F0ICR7VklFV1BPUlRfQk9VTkRBUllfTkFNRX1bXFxcXG5cXFxcc11gXG4pXG5jb25zdCBoYXNPdXRsZXRSZWdleCA9IG5ldyBSZWdFeHAoYFxcXFxuXFxcXHMrYXQgJHtPVVRMRVRfQk9VTkRBUllfTkFNRX1bXFxcXG5cXFxcc11gKVxuXG5leHBvcnQgZnVuY3Rpb24gdHJhY2tBbGxvd2VkRHluYW1pY0FjY2VzcyhcbiAgd29ya1N0b3JlOiBXb3JrU3RvcmUsXG4gIGNvbXBvbmVudFN0YWNrOiBzdHJpbmcsXG4gIGR5bmFtaWNWYWxpZGF0aW9uOiBEeW5hbWljVmFsaWRhdGlvblN0YXRlLFxuICBjbGllbnREeW5hbWljOiBEeW5hbWljVHJhY2tpbmdTdGF0ZVxuKSB7XG4gIGlmIChoYXNPdXRsZXRSZWdleC50ZXN0KGNvbXBvbmVudFN0YWNrKSkge1xuICAgIC8vIFdlIGRvbid0IG5lZWQgdG8gdHJhY2sgdGhhdCB0aGlzIGlzIGR5bmFtaWMuIEl0IGlzIG9ubHkgc28gd2hlbiBzb21ldGhpbmcgZWxzZSBpcyBhbHNvIGR5bmFtaWMuXG4gICAgcmV0dXJuXG4gIH0gZWxzZSBpZiAoaGFzTWV0YWRhdGFSZWdleC50ZXN0KGNvbXBvbmVudFN0YWNrKSkge1xuICAgIGR5bmFtaWNWYWxpZGF0aW9uLmhhc0R5bmFtaWNNZXRhZGF0YSA9IHRydWVcbiAgICByZXR1cm5cbiAgfSBlbHNlIGlmIChoYXNWaWV3cG9ydFJlZ2V4LnRlc3QoY29tcG9uZW50U3RhY2spKSB7XG4gICAgZHluYW1pY1ZhbGlkYXRpb24uaGFzRHluYW1pY1ZpZXdwb3J0ID0gdHJ1ZVxuICAgIHJldHVyblxuICB9IGVsc2UgaWYgKGhhc1N1c3BlbnNlQWZ0ZXJCb2R5T3JIdG1sUmVnZXgudGVzdChjb21wb25lbnRTdGFjaykpIHtcbiAgICAvLyBUaGlzIHByZXJlbmRlciBoYXMgYSBTdXNwZW5zZSBib3VuZGFyeSBhYm92ZSB0aGUgYm9keSB3aGljaFxuICAgIC8vIGVmZmVjdGl2ZWx5IG9wdHMgdGhlIHBhZ2UgaW50byBhbGxvd2luZyAxMDAlIGR5bmFtaWMgcmVuZGVyaW5nXG4gICAgZHluYW1pY1ZhbGlkYXRpb24uaGFzQWxsb3dlZER5bmFtaWMgPSB0cnVlXG4gICAgZHluYW1pY1ZhbGlkYXRpb24uaGFzU3VzcGVuc2VBYm92ZUJvZHkgPSB0cnVlXG4gICAgcmV0dXJuXG4gIH0gZWxzZSBpZiAoaGFzU3VzcGVuc2VSZWdleC50ZXN0KGNvbXBvbmVudFN0YWNrKSkge1xuICAgIC8vIHRoaXMgZXJyb3IgaGFkIGEgU3VzcGVuc2UgYm91bmRhcnkgYWJvdmUgaXQgc28gd2UgZG9uJ3QgbmVlZCB0byByZXBvcnQgaXQgYXMgYSBzb3VyY2VcbiAgICAvLyBvZiBkaXNhbGxvd2VkXG4gICAgZHluYW1pY1ZhbGlkYXRpb24uaGFzQWxsb3dlZER5bmFtaWMgPSB0cnVlXG4gICAgcmV0dXJuXG4gIH0gZWxzZSBpZiAoY2xpZW50RHluYW1pYy5zeW5jRHluYW1pY0Vycm9yV2l0aFN0YWNrKSB7XG4gICAgLy8gVGhpcyB0YXNrIHdhcyB0aGUgdGFzayB0aGF0IGNhbGxlZCB0aGUgc3luYyBlcnJvci5cbiAgICBkeW5hbWljVmFsaWRhdGlvbi5keW5hbWljRXJyb3JzLnB1c2goXG4gICAgICBjbGllbnREeW5hbWljLnN5bmNEeW5hbWljRXJyb3JXaXRoU3RhY2tcbiAgICApXG4gICAgcmV0dXJuXG4gIH0gZWxzZSB7XG4gICAgY29uc3QgbWVzc2FnZSA9IGBSb3V0ZSBcIiR7d29ya1N0b3JlLnJvdXRlfVwiOiBBIGNvbXBvbmVudCBhY2Nlc3NlZCBkYXRhLCBoZWFkZXJzLCBwYXJhbXMsIHNlYXJjaFBhcmFtcywgb3IgYSBzaG9ydC1saXZlZCBjYWNoZSB3aXRob3V0IGEgU3VzcGVuc2UgYm91bmRhcnkgbm9yIGEgXCJ1c2UgY2FjaGVcIiBhYm92ZSBpdC4gU2VlIG1vcmUgaW5mbzogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbmV4dC1wcmVyZW5kZXItbWlzc2luZy1zdXNwZW5zZWBcbiAgICBjb25zdCBlcnJvciA9IGNyZWF0ZUVycm9yV2l0aENvbXBvbmVudE9yT3duZXJTdGFjayhtZXNzYWdlLCBjb21wb25lbnRTdGFjaylcbiAgICBkeW5hbWljVmFsaWRhdGlvbi5keW5hbWljRXJyb3JzLnB1c2goZXJyb3IpXG4gICAgcmV0dXJuXG4gIH1cbn1cblxuLyoqXG4gKiBJbiBkZXYgbW9kZSwgd2UgcHJlZmVyIHVzaW5nIHRoZSBvd25lciBzdGFjaywgb3RoZXJ3aXNlIHRoZSBwcm92aWRlZFxuICogY29tcG9uZW50IHN0YWNrIGlzIHVzZWQuXG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZUVycm9yV2l0aENvbXBvbmVudE9yT3duZXJTdGFjayhcbiAgbWVzc2FnZTogc3RyaW5nLFxuICBjb21wb25lbnRTdGFjazogc3RyaW5nXG4pIHtcbiAgY29uc3Qgb3duZXJTdGFjayA9XG4gICAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJyAmJiBSZWFjdC5jYXB0dXJlT3duZXJTdGFja1xuICAgICAgPyBSZWFjdC5jYXB0dXJlT3duZXJTdGFjaygpXG4gICAgICA6IG51bGxcblxuICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcihtZXNzYWdlKVxuICBlcnJvci5zdGFjayA9IGVycm9yLm5hbWUgKyAnOiAnICsgbWVzc2FnZSArIChvd25lclN0YWNrID8/IGNvbXBvbmVudFN0YWNrKVxuICByZXR1cm4gZXJyb3Jcbn1cblxuZXhwb3J0IGVudW0gUHJlbHVkZVN0YXRlIHtcbiAgRnVsbCA9IDAsXG4gIEVtcHR5ID0gMSxcbiAgRXJyb3JlZCA9IDIsXG59XG5cbmZ1bmN0aW9uIGxvZ0Rpc2FsbG93ZWREeW5hbWljRXJyb3Iod29ya1N0b3JlOiBXb3JrU3RvcmUsIGVycm9yOiBFcnJvcik6IHZvaWQge1xuICBjb25zb2xlLmVycm9yKGVycm9yKVxuXG4gIGlmICghd29ya1N0b3JlLmRldikge1xuICAgIGlmICh3b3JrU3RvcmUuaGFzUmVhZGFibGVFcnJvclN0YWNrcykge1xuICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgYFRvIGdldCBhIG1vcmUgZGV0YWlsZWQgc3RhY2sgdHJhY2UgYW5kIHBpbnBvaW50IHRoZSBpc3N1ZSwgc3RhcnQgdGhlIGFwcCBpbiBkZXZlbG9wbWVudCBtb2RlIGJ5IHJ1bm5pbmcgXFxgbmV4dCBkZXZcXGAsIHRoZW4gb3BlbiBcIiR7d29ya1N0b3JlLnJvdXRlfVwiIGluIHlvdXIgYnJvd3NlciB0byBpbnZlc3RpZ2F0ZSB0aGUgZXJyb3IuYFxuICAgICAgKVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBUbyBnZXQgYSBtb3JlIGRldGFpbGVkIHN0YWNrIHRyYWNlIGFuZCBwaW5wb2ludCB0aGUgaXNzdWUsIHRyeSBvbmUgb2YgdGhlIGZvbGxvd2luZzpcbiAgLSBTdGFydCB0aGUgYXBwIGluIGRldmVsb3BtZW50IG1vZGUgYnkgcnVubmluZyBcXGBuZXh0IGRldlxcYCwgdGhlbiBvcGVuIFwiJHt3b3JrU3RvcmUucm91dGV9XCIgaW4geW91ciBicm93c2VyIHRvIGludmVzdGlnYXRlIHRoZSBlcnJvci5cbiAgLSBSZXJ1biB0aGUgcHJvZHVjdGlvbiBidWlsZCB3aXRoIFxcYG5leHQgYnVpbGQgLS1kZWJ1Zy1wcmVyZW5kZXJcXGAgdG8gZ2VuZXJhdGUgYmV0dGVyIHN0YWNrIHRyYWNlcy5gKVxuICAgIH1cbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdGhyb3dJZkRpc2FsbG93ZWREeW5hbWljKFxuICB3b3JrU3RvcmU6IFdvcmtTdG9yZSxcbiAgcHJlbHVkZTogUHJlbHVkZVN0YXRlLFxuICBkeW5hbWljVmFsaWRhdGlvbjogRHluYW1pY1ZhbGlkYXRpb25TdGF0ZSxcbiAgc2VydmVyRHluYW1pYzogRHluYW1pY1RyYWNraW5nU3RhdGVcbik6IHZvaWQge1xuICBpZiAod29ya1N0b3JlLmludmFsaWREeW5hbWljVXNhZ2VFcnJvcikge1xuICAgIGxvZ0Rpc2FsbG93ZWREeW5hbWljRXJyb3Iod29ya1N0b3JlLCB3b3JrU3RvcmUuaW52YWxpZER5bmFtaWNVc2FnZUVycm9yKVxuICAgIHRocm93IG5ldyBTdGF0aWNHZW5CYWlsb3V0RXJyb3IoKVxuICB9XG5cbiAgaWYgKHByZWx1ZGUgIT09IFByZWx1ZGVTdGF0ZS5GdWxsKSB7XG4gICAgaWYgKGR5bmFtaWNWYWxpZGF0aW9uLmhhc1N1c3BlbnNlQWJvdmVCb2R5KSB7XG4gICAgICAvLyBUaGlzIHJvdXRlIGhhcyBvcHRlZCBpbnRvIGFsbG93aW5nIGZ1bGx5IGR5bmFtaWMgcmVuZGVyaW5nXG4gICAgICAvLyBieSBpbmNsdWRpbmcgYSBTdXNwZW5zZSBib3VuZGFyeSBhYm92ZSB0aGUgYm9keS4gSW4gdGhpcyBjYXNlXG4gICAgICAvLyBhIGxhY2sgb2YgYSBzaGVsbCBpcyBub3QgY29uc2lkZXJlZCBkaXNhbGxvd2VkIHNvIHdlIHNpbXBseSByZXR1cm5cbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChzZXJ2ZXJEeW5hbWljLnN5bmNEeW5hbWljRXJyb3JXaXRoU3RhY2spIHtcbiAgICAgIC8vIFRoZXJlIGlzIG5vIHNoZWxsIGFuZCB0aGUgc2VydmVyIGRpZCBzb21ldGhpbmcgc3luYyBkeW5hbWljIGxpa2VseVxuICAgICAgLy8gbGVhZGluZyB0byBhbiBlYXJseSB0ZXJtaW5hdGlvbiBvZiB0aGUgcHJlcmVuZGVyIGJlZm9yZSB0aGUgc2hlbGxcbiAgICAgIC8vIGNvdWxkIGJlIGNvbXBsZXRlZC4gV2UgdGVybWluYXRlIHRoZSBidWlsZC92YWxpZGF0aW5nIHJlbmRlci5cbiAgICAgIGxvZ0Rpc2FsbG93ZWREeW5hbWljRXJyb3IoXG4gICAgICAgIHdvcmtTdG9yZSxcbiAgICAgICAgc2VydmVyRHluYW1pYy5zeW5jRHluYW1pY0Vycm9yV2l0aFN0YWNrXG4gICAgICApXG4gICAgICB0aHJvdyBuZXcgU3RhdGljR2VuQmFpbG91dEVycm9yKClcbiAgICB9XG5cbiAgICAvLyBXZSBkaWRuJ3QgaGF2ZSBhbnkgc3luYyBiYWlsb3V0cyBidXQgdGhlcmUgbWF5IGJlIHVzZXIgY29kZSB3aGljaFxuICAgIC8vIGJsb2NrZWQgdGhlIHJvb3QuIFdlIHdvdWxkIGhhdmUgY2FwdHVyZWQgdGhlc2UgZHVyaW5nIHRoZSBwcmVyZW5kZXJcbiAgICAvLyBhbmQgY2FuIGxvZyB0aGVtIGhlcmUgYW5kIHRoZW4gdGVybWluYXRlIHRoZSBidWlsZC92YWxpZGF0aW5nIHJlbmRlclxuICAgIGNvbnN0IGR5bmFtaWNFcnJvcnMgPSBkeW5hbWljVmFsaWRhdGlvbi5keW5hbWljRXJyb3JzXG4gICAgaWYgKGR5bmFtaWNFcnJvcnMubGVuZ3RoID4gMCkge1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkeW5hbWljRXJyb3JzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGxvZ0Rpc2FsbG93ZWREeW5hbWljRXJyb3Iod29ya1N0b3JlLCBkeW5hbWljRXJyb3JzW2ldKVxuICAgICAgfVxuXG4gICAgICB0aHJvdyBuZXcgU3RhdGljR2VuQmFpbG91dEVycm9yKClcbiAgICB9XG5cbiAgICAvLyBJZiB3ZSBnb3QgdGhpcyBmYXIgdGhlbiB0aGUgb25seSBvdGhlciB0aGluZyB0aGF0IGNvdWxkIGJlIGJsb2NraW5nXG4gICAgLy8gdGhlIHJvb3QgaXMgZHluYW1pYyBWaWV3cG9ydC4gSWYgdGhpcyBpcyBkeW5hbWljIHRoZW5cbiAgICAvLyB5b3UgbmVlZCB0byBvcHQgaW50byB0aGF0IGJ5IGFkZGluZyBhIFN1c3BlbnNlIGJvdW5kYXJ5IGFib3ZlIHRoZSBib2R5XG4gICAgLy8gdG8gaW5kaWNhdGUgeW91ciBhcmUgb2sgd2l0aCBmdWxseSBkeW5hbWljIHJlbmRlcmluZy5cbiAgICBpZiAoZHluYW1pY1ZhbGlkYXRpb24uaGFzRHluYW1pY1ZpZXdwb3J0KSB7XG4gICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICBgUm91dGUgXCIke3dvcmtTdG9yZS5yb3V0ZX1cIiBoYXMgYSBcXGBnZW5lcmF0ZVZpZXdwb3J0XFxgIHRoYXQgZGVwZW5kcyBvbiBSZXF1ZXN0IGRhdGEgKFxcYGNvb2tpZXMoKVxcYCwgZXRjLi4uKSBvciB1bmNhY2hlZCBleHRlcm5hbCBkYXRhIChcXGBmZXRjaCguLi4pXFxgLCBldGMuLi4pIHdpdGhvdXQgZXhwbGljaXRseSBhbGxvd2luZyBmdWxseSBkeW5hbWljIHJlbmRlcmluZy4gU2VlIG1vcmUgaW5mbyBoZXJlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9uZXh0LXByZXJlbmRlci1keW5hbWljLXZpZXdwb3J0YFxuICAgICAgKVxuICAgICAgdGhyb3cgbmV3IFN0YXRpY0dlbkJhaWxvdXRFcnJvcigpXG4gICAgfVxuXG4gICAgaWYgKHByZWx1ZGUgPT09IFByZWx1ZGVTdGF0ZS5FbXB0eSkge1xuICAgICAgLy8gSWYgd2UgZXZlciBnZXQgdGhpcyBmYXIgdGhlbiB3ZSBtZXNzZWQgdXAgdGhlIHRyYWNraW5nIG9mIGludmFsaWQgZHluYW1pYy5cbiAgICAgIC8vIFdlIHN0aWxsIGFkaGVyZSB0byB0aGUgY29uc3RyYWludCB0aGF0IHlvdSBtdXN0IHByb2R1Y2UgYSBzaGVsbCBidXQgaW52aXRlIHRoZVxuICAgICAgLy8gdXNlciB0byByZXBvcnQgdGhpcyBhcyBhIGJ1ZyBpbiBOZXh0LmpzLlxuICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgYFJvdXRlIFwiJHt3b3JrU3RvcmUucm91dGV9XCIgZGlkIG5vdCBwcm9kdWNlIGEgc3RhdGljIHNoZWxsIGFuZCBOZXh0LmpzIHdhcyB1bmFibGUgdG8gZGV0ZXJtaW5lIGEgcmVhc29uLiBUaGlzIGlzIGEgYnVnIGluIE5leHQuanMuYFxuICAgICAgKVxuICAgICAgdGhyb3cgbmV3IFN0YXRpY0dlbkJhaWxvdXRFcnJvcigpXG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGlmIChcbiAgICAgIGR5bmFtaWNWYWxpZGF0aW9uLmhhc0FsbG93ZWREeW5hbWljID09PSBmYWxzZSAmJlxuICAgICAgZHluYW1pY1ZhbGlkYXRpb24uaGFzRHluYW1pY01ldGFkYXRhXG4gICAgKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICBgUm91dGUgXCIke3dvcmtTdG9yZS5yb3V0ZX1cIiBoYXMgYSBcXGBnZW5lcmF0ZU1ldGFkYXRhXFxgIHRoYXQgZGVwZW5kcyBvbiBSZXF1ZXN0IGRhdGEgKFxcYGNvb2tpZXMoKVxcYCwgZXRjLi4uKSBvciB1bmNhY2hlZCBleHRlcm5hbCBkYXRhIChcXGBmZXRjaCguLi4pXFxgLCBldGMuLi4pIHdoZW4gdGhlIHJlc3Qgb2YgdGhlIHJvdXRlIGRvZXMgbm90LiBTZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL25leHQtcHJlcmVuZGVyLWR5bmFtaWMtbWV0YWRhdGFgXG4gICAgICApXG4gICAgICB0aHJvdyBuZXcgU3RhdGljR2VuQmFpbG91dEVycm9yKClcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJQb3N0cG9uZSIsIlByZWx1ZGVTdGF0ZSIsImFib3J0QW5kVGhyb3dPblN5bmNocm9ub3VzUmVxdWVzdERhdGFBY2Nlc3MiLCJhYm9ydE9uU3luY2hyb25vdXNQbGF0Zm9ybUlPQWNjZXNzIiwiYWNjZXNzZWREeW5hbWljRGF0YSIsImFubm90YXRlRHluYW1pY0FjY2VzcyIsImNvbnN1bWVEeW5hbWljQWNjZXNzIiwiY3JlYXRlRHluYW1pY1RyYWNraW5nU3RhdGUiLCJjcmVhdGVEeW5hbWljVmFsaWRhdGlvblN0YXRlIiwiY3JlYXRlSGFuZ2luZ0lucHV0QWJvcnRTaWduYWwiLCJjcmVhdGVQb3N0cG9uZWRBYm9ydFNpZ25hbCIsImZvcm1hdER5bmFtaWNBUElBY2Nlc3NlcyIsImdldEZpcnN0RHluYW1pY1JlYXNvbiIsImlzRHluYW1pY1Bvc3Rwb25lIiwiaXNQcmVyZW5kZXJJbnRlcnJ1cHRlZEVycm9yIiwibWFya0N1cnJlbnRTY29wZUFzRHluYW1pYyIsInBvc3Rwb25lV2l0aFRyYWNraW5nIiwidGhyb3dJZkRpc2FsbG93ZWREeW5hbWljIiwidGhyb3dUb0ludGVycnVwdFN0YXRpY0dlbmVyYXRpb24iLCJ0cmFja0FsbG93ZWREeW5hbWljQWNjZXNzIiwidHJhY2tEeW5hbWljRGF0YUluRHluYW1pY1JlbmRlciIsInRyYWNrRmFsbGJhY2tQYXJhbUFjY2Vzc2VkIiwidHJhY2tTeW5jaHJvbm91c1BsYXRmb3JtSU9BY2Nlc3NJbkRldiIsInRyYWNrU3luY2hyb25vdXNSZXF1ZXN0RGF0YUFjY2Vzc0luRGV2IiwidXNlRHluYW1pY1JvdXRlUGFyYW1zIiwiaGFzUG9zdHBvbmUiLCJSZWFjdCIsInVuc3RhYmxlX3Bvc3Rwb25lIiwiaXNEZWJ1Z0R5bmFtaWNBY2Nlc3NlcyIsImR5bmFtaWNBY2Nlc3NlcyIsInN5bmNEeW5hbWljRXJyb3JXaXRoU3RhY2siLCJoYXNTdXNwZW5zZUFib3ZlQm9keSIsImhhc0R5bmFtaWNNZXRhZGF0YSIsImhhc0R5bmFtaWNWaWV3cG9ydCIsImhhc0FsbG93ZWREeW5hbWljIiwiZHluYW1pY0Vycm9ycyIsInRyYWNraW5nU3RhdGUiLCJleHByZXNzaW9uIiwic3RvcmUiLCJ3b3JrVW5pdFN0b3JlIiwidHlwZSIsImZvcmNlRHluYW1pYyIsImZvcmNlU3RhdGljIiwiZHluYW1pY1Nob3VsZEVycm9yIiwiU3RhdGljR2VuQmFpbG91dEVycm9yIiwicm91dGUiLCJkeW5hbWljVHJhY2tpbmciLCJyZXZhbGlkYXRlIiwiZXJyIiwiRHluYW1pY1NlcnZlckVycm9yIiwiZHluYW1pY1VzYWdlRGVzY3JpcHRpb24iLCJkeW5hbWljVXNhZ2VTdGFjayIsInN0YWNrIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwidXNlZER5bmFtaWMiLCJwcmVyZW5kZXJTdG9yZSIsIndvcmtVbml0QXN5bmNTdG9yYWdlIiwiZ2V0U3RvcmUiLCJfc3RvcmUiLCJhYm9ydE9uU3luY2hyb25vdXNEeW5hbWljRGF0YUFjY2VzcyIsInJlYXNvbiIsImVycm9yIiwiY3JlYXRlUHJlcmVuZGVySW50ZXJydXB0ZWRFcnJvciIsImNvbnRyb2xsZXIiLCJhYm9ydCIsInB1c2giLCJFcnJvciIsInVuZGVmaW5lZCIsImVycm9yV2l0aFN0YWNrIiwicmVxdWVzdFN0b3JlIiwicHJlcmVuZGVyUGhhc2UiLCJwcmVyZW5kZXJTaWduYWwiLCJzaWduYWwiLCJhYm9ydGVkIiwiYXNzZXJ0UG9zdHBvbmUiLCJjcmVhdGVQb3N0cG9uZVJlYXNvbiIsIm1lc3NhZ2UiLCJpc0R5bmFtaWNQb3N0cG9uZVJlYXNvbiIsImluY2x1ZGVzIiwiTkVYVF9QUkVSRU5ERVJfSU5URVJSVVBURUQiLCJkaWdlc3QiLCJsZW5ndGgiLCJzZXJ2ZXJEeW5hbWljIiwiY2xpZW50RHluYW1pYyIsImZpbHRlciIsImFjY2VzcyIsIm1hcCIsInNwbGl0Iiwic2xpY2UiLCJsaW5lIiwiam9pbiIsIkFib3J0Q29udHJvbGxlciIsIngiLCJjYWNoZVNpZ25hbCIsImlucHV0UmVhZHkiLCJ0aGVuIiwic2NoZWR1bGVPbk5leHRUaWNrIiwid29ya1N0b3JlIiwid29ya0FzeW5jU3RvcmFnZSIsImlzU3RhdGljR2VuZXJhdGlvbiIsImZhbGxiYWNrUm91dGVQYXJhbXMiLCJzaXplIiwidXNlIiwibWFrZUhhbmdpbmdQcm9taXNlIiwicmVuZGVyU2lnbmFsIiwiaGFzU3VzcGVuc2VSZWdleCIsImhhc1N1c3BlbnNlQWZ0ZXJCb2R5T3JIdG1sUmVnZXgiLCJoYXNNZXRhZGF0YVJlZ2V4IiwiUmVnRXhwIiwiTUVUQURBVEFfQk9VTkRBUllfTkFNRSIsImhhc1ZpZXdwb3J0UmVnZXgiLCJWSUVXUE9SVF9CT1VOREFSWV9OQU1FIiwiaGFzT3V0bGV0UmVnZXgiLCJPVVRMRVRfQk9VTkRBUllfTkFNRSIsImNvbXBvbmVudFN0YWNrIiwiZHluYW1pY1ZhbGlkYXRpb24iLCJ0ZXN0IiwiY3JlYXRlRXJyb3JXaXRoQ29tcG9uZW50T3JPd25lclN0YWNrIiwib3duZXJTdGFjayIsImNhcHR1cmVPd25lclN0YWNrIiwibmFtZSIsImxvZ0Rpc2FsbG93ZWREeW5hbWljRXJyb3IiLCJjb25zb2xlIiwiZGV2IiwiaGFzUmVhZGFibGVFcnJvclN0YWNrcyIsInByZWx1ZGUiLCJpbnZhbGlkRHluYW1pY1VzYWdlRXJyb3IiLCJpIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createDedupedByCallsiteServerErrorLoggerDev\", ({\n    enumerable: true,\n    get: function() {\n        return createDedupedByCallsiteServerErrorLoggerDev;\n    }\n}));\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst errorRef = {\n    current: null\n};\n// React.cache is currently only available in canary/experimental React channels.\nconst cache = typeof _react.cache === 'function' ? _react.cache : (fn)=>fn;\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn =  false ? 0 : console.warn;\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n(key)=>{\n    try {\n        logErrorOrWarn(errorRef.current);\n    } finally{\n        errorRef.current = null;\n    }\n});\nfunction createDedupedByCallsiteServerErrorLoggerDev(getMessage) {\n    return function logDedupedError(...args) {\n        const message = getMessage(...args);\n        if (true) {\n            var _stack;\n            const callStackFrames = (_stack = new Error().stack) == null ? void 0 : _stack.split('\\n');\n            if (callStackFrames === undefined || callStackFrames.length < 4) {\n                logErrorOrWarn(message);\n            } else {\n                // Error:\n                //   logDedupedError\n                //   asyncApiBeingAccessedSynchronously\n                //   <userland callsite>\n                // TODO: This breaks if sourcemaps with ignore lists are enabled.\n                const key = callStackFrames[4];\n                errorRef.current = message;\n                flushCurrentErrorIfNew(key);\n            }\n        } else {}\n    };\n}\n\n//# sourceMappingURL=create-deduped-by-callsite-server-error-logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/server/dynamic-rendering-utils.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isHangingPromiseRejectionError: function() {\n        return isHangingPromiseRejectionError;\n    },\n    makeHangingPromise: function() {\n        return makeHangingPromise;\n    }\n});\nfunction isHangingPromiseRejectionError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err)) {\n        return false;\n    }\n    return err.digest === HANGING_PROMISE_REJECTION;\n}\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION';\nclass HangingPromiseRejectionError extends Error {\n    constructor(expression){\n        super(`During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`), this.expression = expression, this.digest = HANGING_PROMISE_REJECTION;\n    }\n}\nconst abortListenersBySignal = new WeakMap();\nfunction makeHangingPromise(signal, expression) {\n    if (signal.aborted) {\n        return Promise.reject(new HangingPromiseRejectionError(expression));\n    } else {\n        const hangingPromise = new Promise((_, reject)=>{\n            const boundRejection = reject.bind(null, new HangingPromiseRejectionError(expression));\n            let currentListeners = abortListenersBySignal.get(signal);\n            if (currentListeners) {\n                currentListeners.push(boundRejection);\n            } else {\n                const listeners = [\n                    boundRejection\n                ];\n                abortListenersBySignal.set(signal, listeners);\n                signal.addEventListener('abort', ()=>{\n                    for(let i = 0; i < listeners.length; i++){\n                        listeners[i]();\n                    }\n                }, {\n                    once: true\n                });\n            }\n        });\n        // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n        // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n        // your own promise out of it you'll need to ensure you handle the error when it rejects.\n        hangingPromise.catch(ignoreReject);\n        return hangingPromise;\n    }\n}\nfunction ignoreReject() {}\n\n//# sourceMappingURL=dynamic-rendering-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2R5bmFtaWMtcmVuZGVyaW5nLXV0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsTUFBTSxDQUdMO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxZQUFZLHNHQUFzRyxZQUFZO0FBQ3BLO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsc0JBQXNCO0FBQ3pEO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcbmV3XFxkb2N1bWVudC1yZXBvc2l0b3J5XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcZHluYW1pYy1yZW5kZXJpbmctdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG4wICYmIChtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBpc0hhbmdpbmdQcm9taXNlUmVqZWN0aW9uRXJyb3I6IG51bGwsXG4gICAgbWFrZUhhbmdpbmdQcm9taXNlOiBudWxsXG59KTtcbmZ1bmN0aW9uIF9leHBvcnQodGFyZ2V0LCBhbGwpIHtcbiAgICBmb3IodmFyIG5hbWUgaW4gYWxsKU9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIG5hbWUsIHtcbiAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgZ2V0OiBhbGxbbmFtZV1cbiAgICB9KTtcbn1cbl9leHBvcnQoZXhwb3J0cywge1xuICAgIGlzSGFuZ2luZ1Byb21pc2VSZWplY3Rpb25FcnJvcjogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBpc0hhbmdpbmdQcm9taXNlUmVqZWN0aW9uRXJyb3I7XG4gICAgfSxcbiAgICBtYWtlSGFuZ2luZ1Byb21pc2U6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gbWFrZUhhbmdpbmdQcm9taXNlO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gaXNIYW5naW5nUHJvbWlzZVJlamVjdGlvbkVycm9yKGVycikge1xuICAgIGlmICh0eXBlb2YgZXJyICE9PSAnb2JqZWN0JyB8fCBlcnIgPT09IG51bGwgfHwgISgnZGlnZXN0JyBpbiBlcnIpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIGVyci5kaWdlc3QgPT09IEhBTkdJTkdfUFJPTUlTRV9SRUpFQ1RJT047XG59XG5jb25zdCBIQU5HSU5HX1BST01JU0VfUkVKRUNUSU9OID0gJ0hBTkdJTkdfUFJPTUlTRV9SRUpFQ1RJT04nO1xuY2xhc3MgSGFuZ2luZ1Byb21pc2VSZWplY3Rpb25FcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihleHByZXNzaW9uKXtcbiAgICAgICAgc3VwZXIoYER1cmluZyBwcmVyZW5kZXJpbmcsICR7ZXhwcmVzc2lvbn0gcmVqZWN0cyB3aGVuIHRoZSBwcmVyZW5kZXIgaXMgY29tcGxldGUuIFR5cGljYWxseSB0aGVzZSBlcnJvcnMgYXJlIGhhbmRsZWQgYnkgUmVhY3QgYnV0IGlmIHlvdSBtb3ZlICR7ZXhwcmVzc2lvbn0gdG8gYSBkaWZmZXJlbnQgY29udGV4dCBieSB1c2luZyBcXGBzZXRUaW1lb3V0XFxgLCBcXGBhZnRlclxcYCwgb3Igc2ltaWxhciBmdW5jdGlvbnMgeW91IG1heSBvYnNlcnZlIHRoaXMgZXJyb3IgYW5kIHlvdSBzaG91bGQgaGFuZGxlIGl0IGluIHRoYXQgY29udGV4dC5gKSwgdGhpcy5leHByZXNzaW9uID0gZXhwcmVzc2lvbiwgdGhpcy5kaWdlc3QgPSBIQU5HSU5HX1BST01JU0VfUkVKRUNUSU9OO1xuICAgIH1cbn1cbmNvbnN0IGFib3J0TGlzdGVuZXJzQnlTaWduYWwgPSBuZXcgV2Vha01hcCgpO1xuZnVuY3Rpb24gbWFrZUhhbmdpbmdQcm9taXNlKHNpZ25hbCwgZXhwcmVzc2lvbikge1xuICAgIGlmIChzaWduYWwuYWJvcnRlZCkge1xuICAgICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QobmV3IEhhbmdpbmdQcm9taXNlUmVqZWN0aW9uRXJyb3IoZXhwcmVzc2lvbikpO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGhhbmdpbmdQcm9taXNlID0gbmV3IFByb21pc2UoKF8sIHJlamVjdCk9PntcbiAgICAgICAgICAgIGNvbnN0IGJvdW5kUmVqZWN0aW9uID0gcmVqZWN0LmJpbmQobnVsbCwgbmV3IEhhbmdpbmdQcm9taXNlUmVqZWN0aW9uRXJyb3IoZXhwcmVzc2lvbikpO1xuICAgICAgICAgICAgbGV0IGN1cnJlbnRMaXN0ZW5lcnMgPSBhYm9ydExpc3RlbmVyc0J5U2lnbmFsLmdldChzaWduYWwpO1xuICAgICAgICAgICAgaWYgKGN1cnJlbnRMaXN0ZW5lcnMpIHtcbiAgICAgICAgICAgICAgICBjdXJyZW50TGlzdGVuZXJzLnB1c2goYm91bmRSZWplY3Rpb24pO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb25zdCBsaXN0ZW5lcnMgPSBbXG4gICAgICAgICAgICAgICAgICAgIGJvdW5kUmVqZWN0aW9uXG4gICAgICAgICAgICAgICAgXTtcbiAgICAgICAgICAgICAgICBhYm9ydExpc3RlbmVyc0J5U2lnbmFsLnNldChzaWduYWwsIGxpc3RlbmVycyk7XG4gICAgICAgICAgICAgICAgc2lnbmFsLmFkZEV2ZW50TGlzdGVuZXIoJ2Fib3J0JywgKCk9PntcbiAgICAgICAgICAgICAgICAgICAgZm9yKGxldCBpID0gMDsgaSA8IGxpc3RlbmVycy5sZW5ndGg7IGkrKyl7XG4gICAgICAgICAgICAgICAgICAgICAgICBsaXN0ZW5lcnNbaV0oKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIHtcbiAgICAgICAgICAgICAgICAgICAgb25jZTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgLy8gV2UgYXJlIGZpbmUgaWYgbm8gb25lIGFjdHVhbGx5IGF3YWl0cyB0aGlzIHByb21pc2UuIFdlIHNob3VsZG4ndCBjb25zaWRlciB0aGlzIGFuIHVuaGFuZGxlZCByZWplY3Rpb24gc29cbiAgICAgICAgLy8gd2UgYXR0YWNoIGEgbm9vcCBjYXRjaCBoYW5kbGVyIGhlcmUgdG8gc3VwcHJlc3MgdGhpcyB3YXJuaW5nLiBJZiB5b3UgYWN0dWFsbHkgYXdhaXQgc29tZXdoZXJlIG9yIGNvbnN0cnVjdFxuICAgICAgICAvLyB5b3VyIG93biBwcm9taXNlIG91dCBvZiBpdCB5b3UnbGwgbmVlZCB0byBlbnN1cmUgeW91IGhhbmRsZSB0aGUgZXJyb3Igd2hlbiBpdCByZWplY3RzLlxuICAgICAgICBoYW5naW5nUHJvbWlzZS5jYXRjaChpZ25vcmVSZWplY3QpO1xuICAgICAgICByZXR1cm4gaGFuZ2luZ1Byb21pc2U7XG4gICAgfVxufVxuZnVuY3Rpb24gaWdub3JlUmVqZWN0KCkge31cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZHluYW1pYy1yZW5kZXJpbmctdXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/cookies.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/server/request/cookies.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"cookies\", ({\n    enumerable: true,\n    get: function() {\n        return cookies;\n    }\n}));\nconst _requestcookies = __webpack_require__(/*! ../web/spec-extension/adapters/request-cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js\");\nconst _cookies = __webpack_require__(/*! ../web/spec-extension/cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nfunction cookies() {\n    const callingExpression = 'cookies';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(\"Route \".concat(workStore.route, ' used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after')), \"__NEXT_ERROR_CODE\", {\n                value: \"E88\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // cookies object without tracking\n            const underlyingCookies = createEmptyCookies();\n            return makeUntrackedExoticCookies(underlyingCookies);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(\"Route \".concat(workStore.route, ' used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')), \"__NEXT_ERROR_CODE\", {\n                    value: \"E398\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(\"Route \".concat(workStore.route, ' used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache')), \"__NEXT_ERROR_CODE\", {\n                    value: \"E157\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(\"Route \".concat(workStore.route, ' with `dynamic = \"error\"` couldn\\'t be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering')), \"__NEXT_ERROR_CODE\", {\n                value: \"E549\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            switch(workUnitStore.type){\n                case 'prerender':\n                    return makeHangingCookies(workUnitStore);\n                case 'prerender-client':\n                    const exportName = '`cookies`';\n                    throw Object.defineProperty(new _invarianterror.InvariantError(\"\".concat(exportName, \" must not be used within a client component. Next.js should be preventing \").concat(exportName, \" from being included in client components statically, but did not in this case.\")), \"__NEXT_ERROR_CODE\", {\n                        value: \"E693\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 'prerender-ppr':\n                    // PPR Prerender (no dynamicIO)\n                    // We are prerendering with PPR. We need track dynamic access here eagerly\n                    // to keep continuity with how cookies has worked in PPR without dynamicIO.\n                    (0, _dynamicrendering.postponeWithTracking)(workStore.route, callingExpression, workUnitStore.dynamicTracking);\n                    break;\n                case 'prerender-legacy':\n                    // Legacy Prerender\n                    // We track dynamic access here so we don't need to wrap the cookies in\n                    // individual property access tracking.\n                    (0, _dynamicrendering.throwToInterruptStaticGeneration)(callingExpression, workStore, workUnitStore);\n                    break;\n                default:\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using cookies inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    // cookies is being called in a dynamic context\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    let underlyingCookies;\n    if ((0, _requestcookies.areCookiesMutableInCurrentPhase)(requestStore)) {\n        // We can't conditionally return different types here based on the context.\n        // To avoid confusion, we always return the readonly type here.\n        underlyingCookies = requestStore.userspaceMutableCookies;\n    } else {\n        underlyingCookies = requestStore.cookies;\n    }\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        if (false) {}\n        return makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticCookies(underlyingCookies);\n    }\n}\nfunction createEmptyCookies() {\n    return _requestcookies.RequestCookiesAdapter.seal(new _cookies.RequestCookies(new Headers({})));\n}\nconst CachedCookies = new WeakMap();\nfunction makeHangingCookies(prerenderStore) {\n    const cachedPromise = CachedCookies.get(prerenderStore);\n    if (cachedPromise) {\n        return cachedPromise;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`cookies()`');\n    CachedCookies.set(prerenderStore, promise);\n    return promise;\n}\nfunction makeUntrackedExoticCookies(underlyingCookies) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = Promise.resolve(underlyingCookies);\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].bind(underlyingCookies) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.bind(underlyingCookies)\n        },\n        size: {\n            get () {\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: underlyingCookies.get.bind(underlyingCookies)\n        },\n        getAll: {\n            value: underlyingCookies.getAll.bind(underlyingCookies)\n        },\n        has: {\n            value: underlyingCookies.has.bind(underlyingCookies)\n        },\n        set: {\n            value: underlyingCookies.set.bind(underlyingCookies)\n        },\n        delete: {\n            value: underlyingCookies.delete.bind(underlyingCookies)\n        },\n        clear: {\n            value: typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.bind(underlyingCookies) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise)\n        },\n        toString: {\n            value: underlyingCookies.toString.bind(underlyingCookies)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingCookies)));\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...cookies()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].apply(underlyingCookies, arguments) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesIterator.call(underlyingCookies);\n            },\n            writable: false\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                syncIODev(route, expression);\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = \"`cookies().get(\".concat(describeNameArg(arguments[0]), \")`\");\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.get.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = \"`cookies().getAll(\".concat(describeNameArg(arguments[0]), \")`\");\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.getAll.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        has: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = \"`cookies().has(\".concat(describeNameArg(arguments[0]), \")`\");\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.has.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = \"`cookies().set(\".concat(describeNameArg(arg), \", ...)`\");\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.set.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = \"`cookies().delete(\".concat(describeNameArg(arguments[0]), \")`\");\n                } else {\n                    expression = \"`cookies().delete(\".concat(describeNameArg(arguments[0]), \", ...)`\");\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.delete.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                syncIODev(route, expression);\n                // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n                return typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.apply(underlyingCookies, arguments) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesClear.call(underlyingCookies, promise);\n            },\n            writable: false\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()` or implicit casting';\n                syncIODev(route, expression);\n                return underlyingCookies.toString.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        }\n    });\n    return promise;\n}\n// Similar to `makeUntrackedExoticCookiesWithDevWarnings`, but just logging the\n// sync access without actually defining the cookies properties on the promise.\nfunction makeUntrackedCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingCookies)));\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case Symbol.iterator:\n                    {\n                        warnForSyncAccess(route, '`...cookies()` or similar iteration');\n                        break;\n                    }\n                case 'size':\n                case 'get':\n                case 'getAll':\n                case 'has':\n                case 'set':\n                case 'delete':\n                case 'clear':\n                case 'toString':\n                    {\n                        warnForSyncAccess(route, \"`cookies().\".concat(prop, \"`\"));\n                        break;\n                    }\n                default:\n                    {\n                    // We only warn for well-defined properties of the cookies object.\n                    }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        }\n    });\n    CachedCookies.set(underlyingCookies, proxiedPromise);\n    return proxiedPromise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'object' && arg !== null && typeof arg.name === 'string' ? \"'\".concat(arg.name, \"'\") : typeof arg === 'string' ? \"'\".concat(arg, \"'\") : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createCookiesAccessError);\nfunction createCookiesAccessError(route, expression) {\n    const prefix = route ? 'Route \"'.concat(route, '\" ') : 'This route ';\n    return Object.defineProperty(new Error(\"\".concat(prefix, \"used \").concat(expression, \". \") + \"`cookies()` should be awaited before using its value. \" + \"Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis\"), \"__NEXT_ERROR_CODE\", {\n        value: \"E223\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction polyfilledResponseCookiesIterator() {\n    return this.getAll().map((c)=>[\n            c.name,\n            c\n        ]).values();\n}\nfunction polyfilledResponseCookiesClear(returnable) {\n    for (const cookie of this.getAll()){\n        this.delete(cookie.name);\n    }\n    return returnable;\n} //# sourceMappingURL=cookies.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/cookies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/draft-mode.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/server/request/draft-mode.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"draftMode\", ({\n    enumerable: true,\n    get: function() {\n        return draftMode;\n    }\n}));\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nfunction draftMode() {\n    const callingExpression = 'draftMode';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (!workStore || !workUnitStore) {\n        (0, _workunitasyncstorageexternal.throwForMissingRequestStore)(callingExpression);\n    }\n    switch(workUnitStore.type){\n        case 'request':\n            return createOrGetCachedDraftMode(workUnitStore.draftMode, workStore);\n        case 'cache':\n        case 'unstable-cache':\n            // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n            // the outmost work unit store is a request store, and if draft mode is\n            // enabled.\n            const draftModeProvider = (0, _workunitasyncstorageexternal.getDraftModeProviderForCacheScope)(workStore, workUnitStore);\n            if (draftModeProvider) {\n                return createOrGetCachedDraftMode(draftModeProvider, workStore);\n            }\n        // Otherwise, we fall through to providing an empty draft mode.\n        // eslint-disable-next-line no-fallthrough\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // Return empty draft mode\n            return createOrGetCachedDraftMode(null, workStore);\n        default:\n            const _exhaustiveCheck = workUnitStore;\n            return _exhaustiveCheck;\n    }\n}\nfunction createOrGetCachedDraftMode(draftModeProvider, workStore) {\n    const cacheKey = draftModeProvider !== null && draftModeProvider !== void 0 ? draftModeProvider : NullDraftMode;\n    const cachedDraftMode = CachedDraftModes.get(cacheKey);\n    if (cachedDraftMode) {\n        return cachedDraftMode;\n    }\n    let promise;\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        const route = workStore == null ? void 0 : workStore.route;\n        if (false) {}\n        promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route);\n    } else {\n        if (false) {}\n        promise = createExoticDraftMode(draftModeProvider);\n    }\n    CachedDraftModes.set(cacheKey, promise);\n    return promise;\n}\nconst NullDraftMode = {};\nconst CachedDraftModes = new WeakMap();\nfunction createExoticDraftMode(underlyingProvider) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            return instance.isEnabled;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    promise.enable = instance.enable.bind(instance);\n    promise.disable = instance.disable.bind(instance);\n    return promise;\n}\nfunction createExoticDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            const expression = '`draftMode().isEnabled`';\n            syncIODev(route, expression);\n            return instance.isEnabled;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(promise, 'enable', {\n        value: function get() {\n            const expression = '`draftMode().enable()`';\n            syncIODev(route, expression);\n            return instance.enable.apply(instance, arguments);\n        }\n    });\n    Object.defineProperty(promise, 'disable', {\n        value: function get() {\n            const expression = '`draftMode().disable()`';\n            syncIODev(route, expression);\n            return instance.disable.apply(instance, arguments);\n        }\n    });\n    return promise;\n}\n// Similar to `createExoticDraftModeWithDevWarnings`, but just logging the sync\n// access without actually defining the draftMode properties on the promise.\nfunction createDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'isEnabled':\n                    warnForSyncAccess(route, \"`draftMode().\".concat(prop, \"`\"));\n                    break;\n                case 'enable':\n                case 'disable':\n                    {\n                        warnForSyncAccess(route, \"`draftMode().\".concat(prop, \"()`\"));\n                        break;\n                    }\n                default:\n                    {\n                    // We only warn for well-defined properties of the draftMode object.\n                    }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        }\n    });\n    return proxiedPromise;\n}\nclass DraftMode {\n    get isEnabled() {\n        if (this._provider !== null) {\n            return this._provider.isEnabled;\n        }\n        return false;\n    }\n    enable() {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        trackDynamicDraftMode('draftMode().enable()');\n        if (this._provider !== null) {\n            this._provider.enable();\n        }\n    }\n    disable() {\n        trackDynamicDraftMode('draftMode().disable()');\n        if (this._provider !== null) {\n            this._provider.disable();\n        }\n    }\n    constructor(provider){\n        this._provider = provider;\n    }\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createDraftModeAccessError);\nfunction createDraftModeAccessError(route, expression) {\n    const prefix = route ? 'Route \"'.concat(route, '\" ') : 'This route ';\n    return Object.defineProperty(new Error(\"\".concat(prefix, \"used \").concat(expression, \". \") + \"`draftMode()` should be awaited before using its value. \" + \"Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis\"), \"__NEXT_ERROR_CODE\", {\n        value: \"E377\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction trackDynamicDraftMode(expression) {\n    const store = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (store) {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(\"Route \".concat(store.route, ' used \"').concat(expression, '\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')), \"__NEXT_ERROR_CODE\", {\n                    value: \"E246\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(\"Route \".concat(store.route, ' used \"').concat(expression, '\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache')), \"__NEXT_ERROR_CODE\", {\n                    value: \"E259\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.phase === 'after') {\n                throw Object.defineProperty(new Error(\"Route \".concat(store.route, ' used \"').concat(expression, '\" inside `after`. The enabled status of draftMode can be read inside `after` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after')), \"__NEXT_ERROR_CODE\", {\n                    value: \"E348\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (store.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(\"Route \".concat(store.route, ' with `dynamic = \"error\"` couldn\\'t be rendered statically because it used `').concat(expression, \"`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\")), \"__NEXT_ERROR_CODE\", {\n                value: \"E553\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            switch(workUnitStore.type){\n                case 'prerender':\n                    // dynamicIO Prerender\n                    const error = Object.defineProperty(new Error(\"Route \".concat(store.route, \" used \").concat(expression, \" without first calling `await connection()`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers\")), \"__NEXT_ERROR_CODE\", {\n                        value: \"E126\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(store.route, expression, error, workUnitStore);\n                    break;\n                case 'prerender-client':\n                    const exportName = '`draftMode`';\n                    throw Object.defineProperty(new _invarianterror.InvariantError(\"\".concat(exportName, \" must not be used within a client component. Next.js should be preventing \").concat(exportName, \" from being included in client components statically, but did not in this case.\")), \"__NEXT_ERROR_CODE\", {\n                        value: \"E693\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 'prerender-ppr':\n                    // PPR Prerender\n                    (0, _dynamicrendering.postponeWithTracking)(store.route, expression, workUnitStore.dynamicTracking);\n                    break;\n                case 'prerender-legacy':\n                    // legacy Prerender\n                    workUnitStore.revalidate = 0;\n                    const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(\"Route \".concat(store.route, \" couldn't be rendered statically because it used `\").concat(expression, \"`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\")), \"__NEXT_ERROR_CODE\", {\n                        value: \"E558\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    store.dynamicUsageDescription = expression;\n                    store.dynamicUsageStack = err.stack;\n                    throw err;\n                case 'request':\n                    if (true) {\n                        workUnitStore.usedDynamic = true;\n                    }\n                    break;\n                default:\n            }\n        }\n    }\n} //# sourceMappingURL=draft-mode.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/draft-mode.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/headers.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/server/request/headers.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"headers\", ({\n    enumerable: true,\n    get: function() {\n        return headers;\n    }\n}));\nconst _headers = __webpack_require__(/*! ../web/spec-extension/adapters/headers */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/headers.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nfunction headers() {\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(\"Route \".concat(workStore.route, ' used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after')), \"__NEXT_ERROR_CODE\", {\n                value: \"E367\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // headers object without tracking\n            const underlyingHeaders = _headers.HeadersAdapter.seal(new Headers({}));\n            return makeUntrackedExoticHeaders(underlyingHeaders);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(\"Route \".concat(workStore.route, ' used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')), \"__NEXT_ERROR_CODE\", {\n                    value: \"E304\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(\"Route \".concat(workStore.route, ' used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache')), \"__NEXT_ERROR_CODE\", {\n                    value: \"E127\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(\"Route \".concat(workStore.route, ' with `dynamic = \"error\"` couldn\\'t be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering')), \"__NEXT_ERROR_CODE\", {\n                value: \"E525\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            switch(workUnitStore.type){\n                case 'prerender':\n                    return makeHangingHeaders(workUnitStore);\n                case 'prerender-client':\n                    const exportName = '`headers`';\n                    throw Object.defineProperty(new _invarianterror.InvariantError(\"\".concat(exportName, \" must not be used within a client component. Next.js should be preventing \").concat(exportName, \" from being included in client components statically, but did not in this case.\")), \"__NEXT_ERROR_CODE\", {\n                        value: \"E693\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 'prerender-ppr':\n                    // PPR Prerender (no dynamicIO)\n                    // We are prerendering with PPR. We need track dynamic access here eagerly\n                    // to keep continuity with how headers has worked in PPR without dynamicIO.\n                    // TODO consider switching the semantic to throw on property access instead\n                    (0, _dynamicrendering.postponeWithTracking)(workStore.route, 'headers', workUnitStore.dynamicTracking);\n                    break;\n                case 'prerender-legacy':\n                    // Legacy Prerender\n                    // We are in a legacy static generation mode while prerendering\n                    // We track dynamic access here so we don't need to wrap the headers in\n                    // individual property access tracking.\n                    (0, _dynamicrendering.throwToInterruptStaticGeneration)('headers', workStore, workUnitStore);\n                    break;\n                default:\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using headers inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)('headers');\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        if (false) {}\n        return makeUntrackedExoticHeadersWithDevWarnings(requestStore.headers, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticHeaders(requestStore.headers);\n    }\n}\nconst CachedHeaders = new WeakMap();\nfunction makeHangingHeaders(prerenderStore) {\n    const cachedHeaders = CachedHeaders.get(prerenderStore);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`headers()`');\n    CachedHeaders.set(prerenderStore, promise);\n    return promise;\n}\nfunction makeUntrackedExoticHeaders(underlyingHeaders) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = Promise.resolve(underlyingHeaders);\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: underlyingHeaders.append.bind(underlyingHeaders)\n        },\n        delete: {\n            value: underlyingHeaders.delete.bind(underlyingHeaders)\n        },\n        get: {\n            value: underlyingHeaders.get.bind(underlyingHeaders)\n        },\n        has: {\n            value: underlyingHeaders.has.bind(underlyingHeaders)\n        },\n        set: {\n            value: underlyingHeaders.set.bind(underlyingHeaders)\n        },\n        getSetCookie: {\n            value: underlyingHeaders.getSetCookie.bind(underlyingHeaders)\n        },\n        forEach: {\n            value: underlyingHeaders.forEach.bind(underlyingHeaders)\n        },\n        keys: {\n            value: underlyingHeaders.keys.bind(underlyingHeaders)\n        },\n        values: {\n            value: underlyingHeaders.values.bind(underlyingHeaders)\n        },\n        entries: {\n            value: underlyingHeaders.entries.bind(underlyingHeaders)\n        },\n        [Symbol.iterator]: {\n            value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingHeaders)));\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = \"`headers().append(\".concat(describeNameArg(arguments[0]), \", ...)`\");\n                syncIODev(route, expression);\n                return underlyingHeaders.append.apply(underlyingHeaders, arguments);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = \"`headers().delete(\".concat(describeNameArg(arguments[0]), \")`\");\n                syncIODev(route, expression);\n                return underlyingHeaders.delete.apply(underlyingHeaders, arguments);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = \"`headers().get(\".concat(describeNameArg(arguments[0]), \")`\");\n                syncIODev(route, expression);\n                return underlyingHeaders.get.apply(underlyingHeaders, arguments);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = \"`headers().has(\".concat(describeNameArg(arguments[0]), \")`\");\n                syncIODev(route, expression);\n                return underlyingHeaders.has.apply(underlyingHeaders, arguments);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = \"`headers().set(\".concat(describeNameArg(arguments[0]), \", ...)`\");\n                syncIODev(route, expression);\n                return underlyingHeaders.set.apply(underlyingHeaders, arguments);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.getSetCookie.apply(underlyingHeaders, arguments);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                syncIODev(route, expression);\n                return underlyingHeaders.forEach.apply(underlyingHeaders, arguments);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.keys.apply(underlyingHeaders, arguments);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.values.apply(underlyingHeaders, arguments);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.entries.apply(underlyingHeaders, arguments);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...headers()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingHeaders[Symbol.iterator].apply(underlyingHeaders, arguments);\n            }\n        }\n    });\n    return promise;\n}\n// Similar to `makeUntrackedExoticHeadersWithDevWarnings`, but just logging the\n// sync access without actually defining the headers properties on the promise.\nfunction makeUntrackedHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingHeaders)));\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case Symbol.iterator:\n                    {\n                        warnForSyncAccess(route, '`...headers()` or similar iteration');\n                        break;\n                    }\n                case 'append':\n                case 'delete':\n                case 'get':\n                case 'has':\n                case 'set':\n                case 'getSetCookie':\n                case 'forEach':\n                case 'keys':\n                case 'values':\n                case 'entries':\n                    {\n                        warnForSyncAccess(route, \"`headers().\".concat(prop, \"`\"));\n                        break;\n                    }\n                default:\n                    {\n                    // We only warn for well-defined properties of the headers object.\n                    }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        }\n    });\n    CachedHeaders.set(underlyingHeaders, proxiedPromise);\n    return proxiedPromise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'string' ? \"'\".concat(arg, \"'\") : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createHeadersAccessError);\nfunction createHeadersAccessError(route, expression) {\n    const prefix = route ? 'Route \"'.concat(route, '\" ') : 'This route ';\n    return Object.defineProperty(new Error(\"\".concat(prefix, \"used \").concat(expression, \". \") + \"`headers()` should be awaited before using its value. \" + \"Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis\"), \"__NEXT_ERROR_CODE\", {\n        value: \"E277\",\n        enumerable: false,\n        configurable: true\n    });\n} //# sourceMappingURL=headers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3JlcXVlc3QvaGVhZGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQXdEZ0JBOzs7ZUFBQUE7OztxQ0FyRFQ7c0RBQzBCOzBEQUNPOzhDQVVqQztxREFDK0I7bURBQ0g7c0VBQ3lCO3VDQUMxQjttQ0FDYzs0Q0FDakI7cUNBQ0E7QUFrQ3hCLFNBQVNBO0lBQ2QsTUFBTUMsWUFBWUMsMEJBQUFBLGdCQUFnQixDQUFDQyxRQUFRO0lBQzNDLE1BQU1DLGdCQUFnQkMsOEJBQUFBLG9CQUFvQixDQUFDRixRQUFRO0lBRW5ELElBQUlGLFdBQVc7UUFDYixJQUNFRyxpQkFDQUEsY0FBY0UsS0FBSyxLQUFLLFdBQ3hCLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLCtCQUFBQSxLQUNEO1lBQ0EsTUFBTSxxQkFFTCxDQUZLLElBQUlDLE1BQ1IsTUFBTyxVQUFFUCxVQUFVUSxLQUFLLEVBQUMseU9BQXlPLENBQUMsS0FEL1A7dUJBQUE7NEJBQUE7OEJBQUE7WUFFTjtRQUNGO1FBRUEsSUFBSVIsVUFBVVMsV0FBVyxFQUFFO1lBQ3pCLHFGQUFxRjtZQUNyRixrQ0FBa0M7WUFDbEMsTUFBTUMsb0JBQW9CQyxTQUFBQSxjQUFjLENBQUNDLElBQUksQ0FBQyxJQUFJQyxRQUFRLENBQUM7WUFDM0QsT0FBT0MsMkJBQTJCSjtRQUNwQztRQUVBLElBQUlQLGVBQWU7WUFDakIsSUFBSUEsY0FBY1ksSUFBSSxLQUFLLFNBQVM7Z0JBQ2xDLE1BQU0scUJBRUwsQ0FGSyxJQUFJUixNQUNSLE1BQU8sVUFBRVAsVUFBVVEsS0FBSyxFQUFDLDBVQUEwVSxDQUFDLEtBRGhXOzJCQUFBO2dDQUFBO2tDQUFBO2dCQUVOO1lBQ0YsT0FBTyxJQUFJTCxjQUFjWSxJQUFJLEtBQUssa0JBQWtCO2dCQUNsRCxNQUFNLHFCQUVMLENBRkssSUFBSVIsTUFDUixNQUFPLFVBQUVQLFVBQVVRLEtBQUssRUFBQyxtWEFBbVgsQ0FBQyxLQUR6WTsyQkFBQTtnQ0FBQTtrQ0FBQTtnQkFFTjtZQUNGO1FBQ0Y7UUFDQSxJQUFJUixVQUFVZ0Isa0JBQWtCLEVBQUU7WUFDaEMsTUFBTSxxQkFFTCxDQUZLLElBQUlDLHlCQUFBQSxxQkFBcUIsQ0FDN0IsTUFBTyxVQUFFakIsVUFBVVEsS0FBSyxFQUFDLGlOQUFpTixDQUFDLEVBRHZPO3VCQUFBOzRCQUFBOzhCQUFBO1lBRU47UUFDRjtRQUVBLElBQUlMLGVBQWU7WUFDakIsT0FBUUEsY0FBY1ksSUFBSTtnQkFDeEIsS0FBSztvQkFDSCxPQUFPRyxtQkFBbUJmO2dCQUM1QixLQUFLO29CQUNILE1BQU1nQixhQUFhO29CQUNuQixNQUFNLHFCQUVMLENBRkssSUFBSUMsZ0JBQUFBLGNBQWMsQ0FDdEIsVUFBR0QsWUFBVywwRUFBMEUsV0FBRUEsWUFBVywrRUFBK0UsQ0FBQyxLQURqTDsrQkFBQTtvQ0FBQTtzQ0FBQTtvQkFFTjtnQkFDRixLQUFLO29CQUNILCtCQUErQjtvQkFDL0IsMEVBQTBFO29CQUMxRSwyRUFBMkU7b0JBQzNFLDJFQUEyRTtvQkFDM0VFLENBQUFBLEdBQUFBLGtCQUFBQSxvQkFBQUEsRUFDRXJCLFVBQVVRLEtBQUssRUFDZixXQUNBTCxjQUFjbUIsZUFBZTtvQkFFL0I7Z0JBQ0YsS0FBSztvQkFDSCxtQkFBbUI7b0JBQ25CLCtEQUErRDtvQkFDL0QsdUVBQXVFO29CQUN2RSx1Q0FBdUM7b0JBQ3ZDQyxDQUFBQSxHQUFBQSxrQkFBQUEsZ0NBQUFBLEVBQWlDLFdBQVd2QixXQUFXRztvQkFDdkQ7Z0JBQ0Y7WUFFRjtRQUNGO1FBQ0EsaUZBQWlGO1FBQ2pGLHlGQUF5RjtRQUN6RnFCLENBQUFBLEdBQUFBLGtCQUFBQSwrQkFBQUEsRUFBZ0N4QixXQUFXRztJQUM3QztJQUVBLE1BQU1zQixlQUFlQyxDQUFBQSxHQUFBQSw4QkFBQUEsdUJBQUFBLEVBQXdCO0lBQzdDLElBQUlDLEtBQW9CLElBQXNCLEVBQUMzQixhQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxVQUFXOEIsaUJBQUFBLEdBQW1CO1FBQzNFLElBQUlILEtBQTZCLEVBQUUsRUFLbEM7UUFFRCxPQUFPTSwwQ0FDTFIsYUFBYTFCLE9BQU8sRUFDcEJDLGFBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFVBQVdRLEtBQUs7SUFFcEIsT0FBTztRQUNMLE9BQU9NLDJCQUEyQlcsYUFBYTFCLE9BQU87SUFDeEQ7QUFDRjtBQUdBLE1BQU1tQyxnQkFBZ0IsSUFBSUM7QUFFMUIsU0FBU2pCLG1CQUNQa0IsY0FBb0M7SUFFcEMsTUFBTUMsZ0JBQWdCSCxjQUFjSSxHQUFHLENBQUNGO0lBQ3hDLElBQUlDLGVBQWU7UUFDakIsT0FBT0E7SUFDVDtJQUVBLE1BQU1FLFVBQVVDLENBQUFBLEdBQUFBLHVCQUFBQSxrQkFBQUEsRUFDZEosZUFBZUssWUFBWSxFQUMzQjtJQUVGUCxjQUFjUSxHQUFHLENBQUNOLGdCQUFnQkc7SUFFbEMsT0FBT0E7QUFDVDtBQUVBLFNBQVN6QiwyQkFDUEosaUJBQWtDO0lBRWxDLE1BQU0yQixnQkFBZ0JILGNBQWNJLEdBQUcsQ0FBQzVCO0lBQ3hDLElBQUkyQixlQUFlO1FBQ2pCLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNRSxVQUFVSSxRQUFRQyxPQUFPLENBQUNsQztJQUNoQ3dCLGNBQWNRLEdBQUcsQ0FBQ2hDLG1CQUFtQjZCO0lBRXJDTSxPQUFPQyxnQkFBZ0IsQ0FBQ1AsU0FBUztRQUMvQlEsUUFBUTtZQUNOQyxPQUFPdEMsa0JBQWtCcUMsTUFBTSxDQUFDRSxJQUFJLENBQUN2QztRQUN2QztRQUNBd0MsUUFBUTtZQUNORixPQUFPdEMsa0JBQWtCd0MsTUFBTSxDQUFDRCxJQUFJLENBQUN2QztRQUN2QztRQUNBNEIsS0FBSztZQUNIVSxPQUFPdEMsa0JBQWtCNEIsR0FBRyxDQUFDVyxJQUFJLENBQUN2QztRQUNwQztRQUNBeUMsS0FBSztZQUNISCxPQUFPdEMsa0JBQWtCeUMsR0FBRyxDQUFDRixJQUFJLENBQUN2QztRQUNwQztRQUNBZ0MsS0FBSztZQUNITSxPQUFPdEMsa0JBQWtCZ0MsR0FBRyxDQUFDTyxJQUFJLENBQUN2QztRQUNwQztRQUNBMEMsY0FBYztZQUNaSixPQUFPdEMsa0JBQWtCMEMsWUFBWSxDQUFDSCxJQUFJLENBQUN2QztRQUM3QztRQUNBMkMsU0FBUztZQUNQTCxPQUFPdEMsa0JBQWtCMkMsT0FBTyxDQUFDSixJQUFJLENBQUN2QztRQUN4QztRQUNBNEMsTUFBTTtZQUNKTixPQUFPdEMsa0JBQWtCNEMsSUFBSSxDQUFDTCxJQUFJLENBQUN2QztRQUNyQztRQUNBNkMsUUFBUTtZQUNOUCxPQUFPdEMsa0JBQWtCNkMsTUFBTSxDQUFDTixJQUFJLENBQUN2QztRQUN2QztRQUNBOEMsU0FBUztZQUNQUixPQUFPdEMsa0JBQWtCOEMsT0FBTyxDQUFDUCxJQUFJLENBQUN2QztRQUN4QztRQUNBLENBQUMrQyxPQUFPQyxRQUFRLENBQUMsRUFBRTtZQUNqQlYsT0FBT3RDLGlCQUFpQixDQUFDK0MsT0FBT0MsUUFBUSxDQUFDLENBQUNULElBQUksQ0FBQ3ZDO1FBQ2pEO0lBQ0Y7SUFFQSxPQUFPNkI7QUFDVDtBQUVBLFNBQVNOLDBDQUNQdkIsaUJBQWtDLEVBQ2xDRixLQUFjO0lBRWQsTUFBTTZCLGdCQUFnQkgsY0FBY0ksR0FBRyxDQUFDNUI7SUFDeEMsSUFBSTJCLGVBQWU7UUFDakIsT0FBT0E7SUFDVDtJQUVBLE1BQU1FLFVBQVUsSUFBSUksUUFBeUIsQ0FBQ0MsVUFDNUNlLENBQUFBLEdBQUFBLFdBQUFBLGlCQUFBQSxFQUFrQixJQUFNZixRQUFRbEM7SUFHbEN3QixjQUFjUSxHQUFHLENBQUNoQyxtQkFBbUI2QjtJQUVyQ00sT0FBT0MsZ0JBQWdCLENBQUNQLFNBQVM7UUFDL0JRLFFBQVE7WUFDTkMsT0FBTyxTQUFTRDtnQkFDZCxNQUFNYSxhQUFhLG1CQUFvQixFQUFnQyxPQUE5QkMsQ0FBc0MsQ0FBQyxjQUF2QkMsU0FBUyxDQUFDLEVBQUU7Z0JBQ3JFQyxVQUFVdkQsT0FBT29EO2dCQUNqQixPQUFPbEQsa0JBQWtCcUMsTUFBTSxDQUFDaUIsS0FBSyxDQUNuQ3RELG1CQUNBb0Q7WUFFSjtRQUNGO1FBQ0FaLFFBQVE7WUFDTkYsT0FBTyxTQUFTaUI7Z0JBQ2QsTUFBTUwsYUFBYSxtQkFBb0IsRUFBZ0MsR0FBRyxDQUFDLEdBQWxDQyxnQkFBZ0JDLFNBQVMsQ0FBQyxFQUFFO2dCQUNyRUMsVUFBVXZELE9BQU9vRDtnQkFDakIsT0FBT2xELGtCQUFrQndDLE1BQU0sQ0FBQ2MsS0FBSyxDQUNuQ3RELG1CQUNBb0Q7WUFFSjtRQUNGO1FBQ0F4QixLQUFLO1lBQ0hVLE9BQU8sU0FBU1Y7Z0JBQ2QsTUFBTXNCLGFBQWEsZ0JBQWlCLFNBQUVDLGdCQUFnQkMsU0FBUyxDQUFDLEVBQUUsR0FBRSxHQUFHLENBQUM7Z0JBQ3hFQyxVQUFVdkQsT0FBT29EO2dCQUNqQixPQUFPbEQsa0JBQWtCNEIsR0FBRyxDQUFDMEIsS0FBSyxDQUFDdEQsbUJBQW1Cb0Q7WUFDeEQ7UUFDRjtRQUNBWCxLQUFLO1lBQ0hILE9BQU8sU0FBU0c7Z0JBQ2QsTUFBTVMsYUFBYSxnQkFBaUIsU0FBRUMsZ0JBQWdCQyxTQUFTLENBQUMsRUFBRSxHQUFFLEdBQUcsQ0FBQztnQkFDeEVDLFVBQVV2RCxPQUFPb0Q7Z0JBQ2pCLE9BQU9sRCxrQkFBa0J5QyxHQUFHLENBQUNhLEtBQUssQ0FBQ3RELG1CQUFtQm9EO1lBQ3hEO1FBQ0Y7UUFDQXBCLEtBQUs7WUFDSE0sT0FBTyxTQUFTTjtnQkFDZCxNQUFNa0IsYUFBYyxnQkFBZ0IsU0FBRUMsZ0JBQWdCQyxTQUFTLENBQUMsRUFBRSxHQUFFLFFBQVEsQ0FBQztnQkFDN0VDLFVBQVV2RCxPQUFPb0Q7Z0JBQ2pCLE9BQU9sRCxrQkFBa0JnQyxHQUFHLENBQUNzQixLQUFLLENBQUN0RCxtQkFBbUJvRDtZQUN4RDtRQUNGO1FBQ0FWLGNBQWM7WUFDWkosT0FBTyxTQUFTSTtnQkFDZCxNQUFNUSxhQUFhO2dCQUNuQkcsVUFBVXZELE9BQU9vRDtnQkFDakIsT0FBT2xELGtCQUFrQjBDLFlBQVksQ0FBQ1ksS0FBSyxDQUN6Q3RELG1CQUNBb0Q7WUFFSjtRQUNGO1FBQ0FULFNBQVM7WUFDUEwsT0FBTyxTQUFTSztnQkFDZCxNQUFNTyxhQUFhO2dCQUNuQkcsVUFBVXZELE9BQU9vRDtnQkFDakIsT0FBT2xELGtCQUFrQjJDLE9BQU8sQ0FBQ1csS0FBSyxDQUNwQ3RELG1CQUNBb0Q7WUFFSjtRQUNGO1FBQ0FSLE1BQU07WUFDSk4sT0FBTyxTQUFTTTtnQkFDZCxNQUFNTSxhQUFhO2dCQUNuQkcsVUFBVXZELE9BQU9vRDtnQkFDakIsT0FBT2xELGtCQUFrQjRDLElBQUksQ0FBQ1UsS0FBSyxDQUFDdEQsbUJBQW1Cb0Q7WUFDekQ7UUFDRjtRQUNBUCxRQUFRO1lBQ05QLE9BQU8sU0FBU087Z0JBQ2QsTUFBTUssYUFBYTtnQkFDbkJHLFVBQVV2RCxPQUFPb0Q7Z0JBQ2pCLE9BQU9sRCxrQkFBa0I2QyxNQUFNLENBQUNTLEtBQUssQ0FDbkN0RCxtQkFDQW9EO1lBRUo7UUFDRjtRQUNBTixTQUFTO1lBQ1BSLE9BQU8sU0FBU1E7Z0JBQ2QsTUFBTUksYUFBYTtnQkFDbkJHLFVBQVV2RCxPQUFPb0Q7Z0JBQ2pCLE9BQU9sRCxrQkFBa0I4QyxPQUFPLENBQUNRLEtBQUssQ0FDcEN0RCxtQkFDQW9EO1lBRUo7UUFDRjtRQUNBLENBQUNMLE9BQU9DLFFBQVEsQ0FBQyxFQUFFO1lBQ2pCVixPQUFPO2dCQUNMLE1BQU1ZLGFBQWE7Z0JBQ25CRyxVQUFVdkQsT0FBT29EO2dCQUNqQixPQUFPbEQsaUJBQWlCLENBQUMrQyxPQUFPQyxRQUFRLENBQUMsQ0FBQ00sS0FBSyxDQUM3Q3RELG1CQUNBb0Q7WUFFSjtRQUNGO0lBQ0Y7SUFFQSxPQUFPdkI7QUFDVDtBQUVBLCtFQUErRTtBQUMvRSwrRUFBK0U7QUFDL0UsU0FBU1Asb0NBQ1B0QixpQkFBa0MsRUFDbENGLEtBQWM7SUFFZCxNQUFNNkIsZ0JBQWdCSCxjQUFjSSxHQUFHLENBQUM1QjtJQUN4QyxJQUFJMkIsZUFBZTtRQUNqQixPQUFPQTtJQUNUO0lBRUEsTUFBTUUsVUFBVSxJQUFJSSxRQUF5QixDQUFDQyxVQUM1Q2UsQ0FBQUEsR0FBQUEsV0FBQUEsaUJBQUFBLEVBQWtCLElBQU1mLFFBQVFsQztJQUdsQyxNQUFNd0QsaUJBQWlCLElBQUlDLE1BQU01QixTQUFTO1FBQ3hDRCxLQUFJOEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLFFBQVE7WUFDeEIsT0FBUUQ7Z0JBQ04sS0FBS1osT0FBT0MsUUFBUTtvQkFBRTt3QkFDcEJhLGtCQUFrQi9ELE9BQU87d0JBQ3pCO29CQUNGO2dCQUNBLEtBQUs7Z0JBQ0wsS0FBSztnQkFDTCxLQUFLO2dCQUNMLEtBQUs7Z0JBQ0wsS0FBSztnQkFDTCxLQUFLO2dCQUNMLEtBQUs7Z0JBQ0wsS0FBSztnQkFDTCxLQUFLO2dCQUNMLEtBQUs7b0JBQVc7d0JBQ2QrRCxrQkFBa0IvRCxPQUFPLFlBQWEsU0FBRTZELE1BQUssRUFBRSxDQUFDO3dCQUNoRDtvQkFDRjtnQkFDQTtvQkFBUztvQkFDUCxrRUFBa0U7b0JBQ3BFO1lBQ0Y7WUFFQSxPQUFPRyxTQUFBQSxjQUFjLENBQUNsQyxHQUFHLENBQUM4QixRQUFRQyxNQUFNQztRQUMxQztJQUNGO0lBRUFwQyxjQUFjUSxHQUFHLENBQUNoQyxtQkFBbUJ3RDtJQUVyQyxPQUFPQTtBQUNUO0FBRUEsU0FBU0wsZ0JBQWdCWSxHQUFZO0lBQ25DLE9BQU8sT0FBT0EsUUFBUSxXQUFXLENBQUUsVUFBRUEsS0FBSSxDQUFDLENBQUMsS0FBRztBQUNoRDtBQUVBLFNBQVNWLFVBQVV2RCxLQUF5QixFQUFFb0QsVUFBa0I7SUFDOUQsTUFBTXpELGdCQUFnQkMsOEJBQUFBLG9CQUFvQixDQUFDRixRQUFRO0lBQ25ELElBQ0VDLGlCQUNBQSxjQUFjWSxJQUFJLEtBQUssYUFDdkJaLGNBQWN1RSxjQUFjLEtBQUssTUFDakM7UUFDQSx3RUFBd0U7UUFDeEUsZ0VBQWdFO1FBQ2hFLE1BQU1qRCxlQUFldEI7UUFDckJ3RSxDQUFBQSxHQUFBQSxrQkFBQUEsc0NBQUFBLEVBQXVDbEQ7SUFDekM7SUFDQSxnQ0FBZ0M7SUFDaEM4QyxrQkFBa0IvRCxPQUFPb0Q7QUFDM0I7QUFFQSxNQUFNVyxvQkFBb0JLLENBQUFBLEdBQUFBLDBDQUFBQSwyQ0FBQUEsRUFDeEJDO0FBR0YsU0FBU0EseUJBQ1ByRSxLQUF5QixFQUN6Qm9ELFVBQWtCO0lBRWxCLE1BQU1rQixTQUFTdEUsUUFBUSxPQUFRLFVBQUVBLE9BQU0sRUFBRSxDQUFDLEtBQUc7SUFDN0MsT0FBTyxxQkFJTixDQUpNLElBQUlELE1BQ1QsVUFBR3VFLFFBQU8sS0FBSyxXQUFFbEIsWUFBVyxFQUFFLENBQUMsR0FDN0IsRUFBQyx3REFBd0QsQ0FBQyxFQUN6RCxDQUFELDZEQUErRCxDQUFDLElBSDdEO2VBQUE7b0JBQUE7c0JBQUE7SUFJUDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXHNyY1xcc2VydmVyXFxyZXF1ZXN0XFxoZWFkZXJzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIEhlYWRlcnNBZGFwdGVyLFxuICB0eXBlIFJlYWRvbmx5SGVhZGVycyxcbn0gZnJvbSAnLi4vd2ViL3NwZWMtZXh0ZW5zaW9uL2FkYXB0ZXJzL2hlYWRlcnMnXG5pbXBvcnQgeyB3b3JrQXN5bmNTdG9yYWdlIH0gZnJvbSAnLi4vYXBwLXJlbmRlci93b3JrLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwnXG5pbXBvcnQgeyBnZXRFeHBlY3RlZFJlcXVlc3RTdG9yZSB9IGZyb20gJy4uL2FwcC1yZW5kZXIvd29yay11bml0LWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwnXG5pbXBvcnQge1xuICB3b3JrVW5pdEFzeW5jU3RvcmFnZSxcbiAgdHlwZSBQcmVyZW5kZXJTdG9yZU1vZGVybixcbn0gZnJvbSAnLi4vYXBwLXJlbmRlci93b3JrLXVuaXQtYXN5bmMtc3RvcmFnZS5leHRlcm5hbCdcbmltcG9ydCB7XG4gIHBvc3Rwb25lV2l0aFRyYWNraW5nLFxuICB0aHJvd1RvSW50ZXJydXB0U3RhdGljR2VuZXJhdGlvbixcbiAgdHJhY2tEeW5hbWljRGF0YUluRHluYW1pY1JlbmRlcixcbiAgdHJhY2tTeW5jaHJvbm91c1JlcXVlc3REYXRhQWNjZXNzSW5EZXYsXG59IGZyb20gJy4uL2FwcC1yZW5kZXIvZHluYW1pYy1yZW5kZXJpbmcnXG5pbXBvcnQgeyBTdGF0aWNHZW5CYWlsb3V0RXJyb3IgfSBmcm9tICcuLi8uLi9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1iYWlsb3V0J1xuaW1wb3J0IHsgbWFrZUhhbmdpbmdQcm9taXNlIH0gZnJvbSAnLi4vZHluYW1pYy1yZW5kZXJpbmctdXRpbHMnXG5pbXBvcnQgeyBjcmVhdGVEZWR1cGVkQnlDYWxsc2l0ZVNlcnZlckVycm9yTG9nZ2VyRGV2IH0gZnJvbSAnLi4vY3JlYXRlLWRlZHVwZWQtYnktY2FsbHNpdGUtc2VydmVyLWVycm9yLWxvZ2dlcidcbmltcG9ydCB7IHNjaGVkdWxlSW1tZWRpYXRlIH0gZnJvbSAnLi4vLi4vbGliL3NjaGVkdWxlcidcbmltcG9ydCB7IGlzUmVxdWVzdEFQSUNhbGxhYmxlSW5zaWRlQWZ0ZXIgfSBmcm9tICcuL3V0aWxzJ1xuaW1wb3J0IHsgSW52YXJpYW50RXJyb3IgfSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL2ludmFyaWFudC1lcnJvcidcbmltcG9ydCB7IFJlZmxlY3RBZGFwdGVyIH0gZnJvbSAnLi4vd2ViL3NwZWMtZXh0ZW5zaW9uL2FkYXB0ZXJzL3JlZmxlY3QnXG5cbi8qKlxuICogSW4gdGhpcyB2ZXJzaW9uIG9mIE5leHQuanMgYGhlYWRlcnMoKWAgcmV0dXJucyBhIFByb21pc2UgaG93ZXZlciB5b3UgY2FuIHN0aWxsIHJlZmVyZW5jZSB0aGUgcHJvcGVydGllcyBvZiB0aGUgdW5kZXJseWluZyBIZWFkZXJzIGluc3RhbmNlXG4gKiBzeW5jaHJvbm91c2x5IHRvIGZhY2lsaXRhdGUgbWlncmF0aW9uLiBUaGUgYFVuc2FmZVVud3JhcHBlZEhlYWRlcnNgIHR5cGUgaXMgYWRkZWQgdG8geW91ciBjb2RlIGJ5IGEgY29kZW1vZCB0aGF0IGF0dGVtcHRzIHRvIGF1dG9tYXRpY2FsbHlcbiAqIHVwZGF0ZXMgY2FsbHNpdGVzIHRvIHJlZmxlY3QgdGhlIG5ldyBQcm9taXNlIHJldHVybiB0eXBlLiBUaGVyZSBhcmUgc29tZSBjYXNlcyB3aGVyZSBgaGVhZGVycygpYCBjYW5ub3QgYmUgYXV0b21hdGljYWxseSBjb252ZXJ0ZWQsIG5hbWVseVxuICogd2hlbiBpdCBpcyB1c2VkIGluc2lkZSBhIHN5bmNocm9ub3VzIGZ1bmN0aW9uIGFuZCB3ZSBjYW4ndCBiZSBzdXJlIHRoZSBmdW5jdGlvbiBjYW4gYmUgbWFkZSBhc3luYyBhdXRvbWF0aWNhbGx5LiBJbiB0aGVzZSBjYXNlcyB3ZSBhZGQgYW5cbiAqIGV4cGxpY2l0IHR5cGUgY2FzZSB0byBgVW5zYWZlVW53cmFwcGVkSGVhZGVyc2AgdG8gZW5hYmxlIHR5cGVzY3JpcHQgdG8gYWxsb3cgZm9yIHRoZSBzeW5jaHJvbm91cyB1c2FnZSBvbmx5IHdoZXJlIGl0IGlzIGFjdHVhbGx5IG5lY2Vzc2FyeS5cbiAqXG4gKiBZb3Ugc2hvdWxkIHNob3VsZCB1cGRhdGUgdGhlc2UgY2FsbHNpdGVzIHRvIGVpdGhlciBiZSBhc3luYyBmdW5jdGlvbnMgd2hlcmUgdGhlIGBoZWFkZXJzKClgIHZhbHVlIGNhbiBiZSBhd2FpdGVkIG9yIHlvdSBzaG91bGQgY2FsbCBgaGVhZGVycygpYFxuICogZnJvbSBvdXRzaWRlIGFuZCBhd2FpdCB0aGUgcmV0dXJuIHZhbHVlIGJlZm9yZSBwYXNzaW5nIGl0IGludG8gdGhpcyBmdW5jdGlvbi5cbiAqXG4gKiBZb3UgY2FuIGZpbmQgaW5zdGFuY2VzIHRoYXQgcmVxdWlyZSBtYW51YWwgbWlncmF0aW9uIGJ5IHNlYXJjaGluZyBmb3IgYFVuc2FmZVVud3JhcHBlZEhlYWRlcnNgIGluIHlvdXIgY29kZWJhc2Ugb3IgYnkgc2VhcmNoIGZvciBhIGNvbW1lbnQgdGhhdFxuICogc3RhcnRzIHdpdGggYEBuZXh0LWNvZGVtb2QtZXJyb3JgLlxuICpcbiAqIEluIGEgZnV0dXJlIHZlcnNpb24gb2YgTmV4dC5qcyBgaGVhZGVycygpYCB3aWxsIG9ubHkgcmV0dXJuIGEgUHJvbWlzZSBhbmQgeW91IHdpbGwgbm90IGJlIGFibGUgdG8gYWNjZXNzIHRoZSB1bmRlcmx5aW5nIEhlYWRlcnMgaW5zdGFuY2VcbiAqIHdpdGhvdXQgYXdhaXRpbmcgdGhlIHJldHVybiB2YWx1ZSBmaXJzdC4gV2hlbiB0aGlzIGNoYW5nZSBoYXBwZW5zIHRoZSB0eXBlIGBVbnNhZmVVbndyYXBwZWRIZWFkZXJzYCB3aWxsIGJlIHVwZGF0ZWQgdG8gcmVmbGVjdCB0aGF0IGlzIGl0IG5vIGxvbmdlclxuICogdXNhYmxlLlxuICpcbiAqIFRoaXMgdHlwZSBpcyBtYXJrZWQgZGVwcmVjYXRlZCB0byBoZWxwIGlkZW50aWZ5IGl0IGFzIHRhcmdldCBmb3IgcmVmYWN0b3JpbmcgYXdheS5cbiAqXG4gKiBAZGVwcmVjYXRlZFxuICovXG5leHBvcnQgdHlwZSBVbnNhZmVVbndyYXBwZWRIZWFkZXJzID0gUmVhZG9ubHlIZWFkZXJzXG5cbi8qKlxuICogVGhpcyBmdW5jdGlvbiBhbGxvd3MgeW91IHRvIHJlYWQgdGhlIEhUVFAgaW5jb21pbmcgcmVxdWVzdCBoZWFkZXJzIGluXG4gKiBbU2VydmVyIENvbXBvbmVudHNdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL3JlbmRlcmluZy9zZXJ2ZXItY29tcG9uZW50cyksXG4gKiBbU2VydmVyIEFjdGlvbnNdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL2RhdGEtZmV0Y2hpbmcvc2VydmVyLWFjdGlvbnMtYW5kLW11dGF0aW9ucyksXG4gKiBbUm91dGUgSGFuZGxlcnNdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL3JvdXRpbmcvcm91dGUtaGFuZGxlcnMpIGFuZFxuICogW01pZGRsZXdhcmVdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL3JvdXRpbmcvbWlkZGxld2FyZSkuXG4gKlxuICogUmVhZCBtb3JlOiBbTmV4dC5qcyBEb2NzOiBgaGVhZGVyc2BdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9hcGktcmVmZXJlbmNlL2Z1bmN0aW9ucy9oZWFkZXJzKVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGVhZGVycygpOiBQcm9taXNlPFJlYWRvbmx5SGVhZGVycz4ge1xuICBjb25zdCB3b3JrU3RvcmUgPSB3b3JrQXN5bmNTdG9yYWdlLmdldFN0b3JlKClcbiAgY29uc3Qgd29ya1VuaXRTdG9yZSA9IHdvcmtVbml0QXN5bmNTdG9yYWdlLmdldFN0b3JlKClcblxuICBpZiAod29ya1N0b3JlKSB7XG4gICAgaWYgKFxuICAgICAgd29ya1VuaXRTdG9yZSAmJlxuICAgICAgd29ya1VuaXRTdG9yZS5waGFzZSA9PT0gJ2FmdGVyJyAmJlxuICAgICAgIWlzUmVxdWVzdEFQSUNhbGxhYmxlSW5zaWRlQWZ0ZXIoKVxuICAgICkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICBgUm91dGUgJHt3b3JrU3RvcmUucm91dGV9IHVzZWQgXCJoZWFkZXJzXCIgaW5zaWRlIFwiYWZ0ZXIoLi4uKVwiLiBUaGlzIGlzIG5vdCBzdXBwb3J0ZWQuIElmIHlvdSBuZWVkIHRoaXMgZGF0YSBpbnNpZGUgYW4gXCJhZnRlclwiIGNhbGxiYWNrLCB1c2UgXCJoZWFkZXJzXCIgb3V0c2lkZSBvZiB0aGUgY2FsbGJhY2suIFNlZSBtb3JlIGluZm8gaGVyZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvY2FuYXJ5L2FwcC9hcGktcmVmZXJlbmNlL2Z1bmN0aW9ucy9hZnRlcmBcbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAod29ya1N0b3JlLmZvcmNlU3RhdGljKSB7XG4gICAgICAvLyBXaGVuIHVzaW5nIGZvcmNlU3RhdGljIHdlIG92ZXJyaWRlIGFsbCBvdGhlciBsb2dpYyBhbmQgYWx3YXlzIGp1c3QgcmV0dXJuIGFuIGVtcHR5XG4gICAgICAvLyBoZWFkZXJzIG9iamVjdCB3aXRob3V0IHRyYWNraW5nXG4gICAgICBjb25zdCB1bmRlcmx5aW5nSGVhZGVycyA9IEhlYWRlcnNBZGFwdGVyLnNlYWwobmV3IEhlYWRlcnMoe30pKVxuICAgICAgcmV0dXJuIG1ha2VVbnRyYWNrZWRFeG90aWNIZWFkZXJzKHVuZGVybHlpbmdIZWFkZXJzKVxuICAgIH1cblxuICAgIGlmICh3b3JrVW5pdFN0b3JlKSB7XG4gICAgICBpZiAod29ya1VuaXRTdG9yZS50eXBlID09PSAnY2FjaGUnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICBgUm91dGUgJHt3b3JrU3RvcmUucm91dGV9IHVzZWQgXCJoZWFkZXJzXCIgaW5zaWRlIFwidXNlIGNhY2hlXCIuIEFjY2Vzc2luZyBEeW5hbWljIGRhdGEgc291cmNlcyBpbnNpZGUgYSBjYWNoZSBzY29wZSBpcyBub3Qgc3VwcG9ydGVkLiBJZiB5b3UgbmVlZCB0aGlzIGRhdGEgaW5zaWRlIGEgY2FjaGVkIGZ1bmN0aW9uIHVzZSBcImhlYWRlcnNcIiBvdXRzaWRlIG9mIHRoZSBjYWNoZWQgZnVuY3Rpb24gYW5kIHBhc3MgdGhlIHJlcXVpcmVkIGR5bmFtaWMgZGF0YSBpbiBhcyBhbiBhcmd1bWVudC4gU2VlIG1vcmUgaW5mbyBoZXJlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9uZXh0LXJlcXVlc3QtaW4tdXNlLWNhY2hlYFxuICAgICAgICApXG4gICAgICB9IGVsc2UgaWYgKHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ3Vuc3RhYmxlLWNhY2hlJykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgYFJvdXRlICR7d29ya1N0b3JlLnJvdXRlfSB1c2VkIFwiaGVhZGVyc1wiIGluc2lkZSBhIGZ1bmN0aW9uIGNhY2hlZCB3aXRoIFwidW5zdGFibGVfY2FjaGUoLi4uKVwiLiBBY2Nlc3NpbmcgRHluYW1pYyBkYXRhIHNvdXJjZXMgaW5zaWRlIGEgY2FjaGUgc2NvcGUgaXMgbm90IHN1cHBvcnRlZC4gSWYgeW91IG5lZWQgdGhpcyBkYXRhIGluc2lkZSBhIGNhY2hlZCBmdW5jdGlvbiB1c2UgXCJoZWFkZXJzXCIgb3V0c2lkZSBvZiB0aGUgY2FjaGVkIGZ1bmN0aW9uIGFuZCBwYXNzIHRoZSByZXF1aXJlZCBkeW5hbWljIGRhdGEgaW4gYXMgYW4gYXJndW1lbnQuIFNlZSBtb3JlIGluZm8gaGVyZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3Vuc3RhYmxlX2NhY2hlYFxuICAgICAgICApXG4gICAgICB9XG4gICAgfVxuICAgIGlmICh3b3JrU3RvcmUuZHluYW1pY1Nob3VsZEVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgU3RhdGljR2VuQmFpbG91dEVycm9yKFxuICAgICAgICBgUm91dGUgJHt3b3JrU3RvcmUucm91dGV9IHdpdGggXFxgZHluYW1pYyA9IFwiZXJyb3JcIlxcYCBjb3VsZG4ndCBiZSByZW5kZXJlZCBzdGF0aWNhbGx5IGJlY2F1c2UgaXQgdXNlZCBcXGBoZWFkZXJzXFxgLiBTZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL3JlbmRlcmluZy9zdGF0aWMtYW5kLWR5bmFtaWMjZHluYW1pYy1yZW5kZXJpbmdgXG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKHdvcmtVbml0U3RvcmUpIHtcbiAgICAgIHN3aXRjaCAod29ya1VuaXRTdG9yZS50eXBlKSB7XG4gICAgICAgIGNhc2UgJ3ByZXJlbmRlcic6XG4gICAgICAgICAgcmV0dXJuIG1ha2VIYW5naW5nSGVhZGVycyh3b3JrVW5pdFN0b3JlKVxuICAgICAgICBjYXNlICdwcmVyZW5kZXItY2xpZW50JzpcbiAgICAgICAgICBjb25zdCBleHBvcnROYW1lID0gJ2BoZWFkZXJzYCdcbiAgICAgICAgICB0aHJvdyBuZXcgSW52YXJpYW50RXJyb3IoXG4gICAgICAgICAgICBgJHtleHBvcnROYW1lfSBtdXN0IG5vdCBiZSB1c2VkIHdpdGhpbiBhIGNsaWVudCBjb21wb25lbnQuIE5leHQuanMgc2hvdWxkIGJlIHByZXZlbnRpbmcgJHtleHBvcnROYW1lfSBmcm9tIGJlaW5nIGluY2x1ZGVkIGluIGNsaWVudCBjb21wb25lbnRzIHN0YXRpY2FsbHksIGJ1dCBkaWQgbm90IGluIHRoaXMgY2FzZS5gXG4gICAgICAgICAgKVxuICAgICAgICBjYXNlICdwcmVyZW5kZXItcHByJzpcbiAgICAgICAgICAvLyBQUFIgUHJlcmVuZGVyIChubyBkeW5hbWljSU8pXG4gICAgICAgICAgLy8gV2UgYXJlIHByZXJlbmRlcmluZyB3aXRoIFBQUi4gV2UgbmVlZCB0cmFjayBkeW5hbWljIGFjY2VzcyBoZXJlIGVhZ2VybHlcbiAgICAgICAgICAvLyB0byBrZWVwIGNvbnRpbnVpdHkgd2l0aCBob3cgaGVhZGVycyBoYXMgd29ya2VkIGluIFBQUiB3aXRob3V0IGR5bmFtaWNJTy5cbiAgICAgICAgICAvLyBUT0RPIGNvbnNpZGVyIHN3aXRjaGluZyB0aGUgc2VtYW50aWMgdG8gdGhyb3cgb24gcHJvcGVydHkgYWNjZXNzIGluc3RlYWRcbiAgICAgICAgICBwb3N0cG9uZVdpdGhUcmFja2luZyhcbiAgICAgICAgICAgIHdvcmtTdG9yZS5yb3V0ZSxcbiAgICAgICAgICAgICdoZWFkZXJzJyxcbiAgICAgICAgICAgIHdvcmtVbml0U3RvcmUuZHluYW1pY1RyYWNraW5nXG4gICAgICAgICAgKVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIGNhc2UgJ3ByZXJlbmRlci1sZWdhY3knOlxuICAgICAgICAgIC8vIExlZ2FjeSBQcmVyZW5kZXJcbiAgICAgICAgICAvLyBXZSBhcmUgaW4gYSBsZWdhY3kgc3RhdGljIGdlbmVyYXRpb24gbW9kZSB3aGlsZSBwcmVyZW5kZXJpbmdcbiAgICAgICAgICAvLyBXZSB0cmFjayBkeW5hbWljIGFjY2VzcyBoZXJlIHNvIHdlIGRvbid0IG5lZWQgdG8gd3JhcCB0aGUgaGVhZGVycyBpblxuICAgICAgICAgIC8vIGluZGl2aWR1YWwgcHJvcGVydHkgYWNjZXNzIHRyYWNraW5nLlxuICAgICAgICAgIHRocm93VG9JbnRlcnJ1cHRTdGF0aWNHZW5lcmF0aW9uKCdoZWFkZXJzJywgd29ya1N0b3JlLCB3b3JrVW5pdFN0b3JlKVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgIC8vIGZhbGx0aHJvdWdoXG4gICAgICB9XG4gICAgfVxuICAgIC8vIFdlIGZhbGwgdGhyb3VnaCB0byB0aGUgZHluYW1pYyBjb250ZXh0IGJlbG93IGJ1dCB3ZSBzdGlsbCB0cmFjayBkeW5hbWljIGFjY2Vzc1xuICAgIC8vIGJlY2F1c2UgaW4gZGV2IHdlIGNhbiBzdGlsbCBlcnJvciBmb3IgdGhpbmdzIGxpa2UgdXNpbmcgaGVhZGVycyBpbnNpZGUgYSBjYWNoZSBjb250ZXh0XG4gICAgdHJhY2tEeW5hbWljRGF0YUluRHluYW1pY1JlbmRlcih3b3JrU3RvcmUsIHdvcmtVbml0U3RvcmUpXG4gIH1cblxuICBjb25zdCByZXF1ZXN0U3RvcmUgPSBnZXRFeHBlY3RlZFJlcXVlc3RTdG9yZSgnaGVhZGVycycpXG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiAhd29ya1N0b3JlPy5pc1ByZWZldGNoUmVxdWVzdCkge1xuICAgIGlmIChwcm9jZXNzLmVudi5fX05FWFRfRFlOQU1JQ19JTykge1xuICAgICAgcmV0dXJuIG1ha2VVbnRyYWNrZWRIZWFkZXJzV2l0aERldldhcm5pbmdzKFxuICAgICAgICByZXF1ZXN0U3RvcmUuaGVhZGVycyxcbiAgICAgICAgd29ya1N0b3JlPy5yb3V0ZVxuICAgICAgKVxuICAgIH1cblxuICAgIHJldHVybiBtYWtlVW50cmFja2VkRXhvdGljSGVhZGVyc1dpdGhEZXZXYXJuaW5ncyhcbiAgICAgIHJlcXVlc3RTdG9yZS5oZWFkZXJzLFxuICAgICAgd29ya1N0b3JlPy5yb3V0ZVxuICAgIClcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gbWFrZVVudHJhY2tlZEV4b3RpY0hlYWRlcnMocmVxdWVzdFN0b3JlLmhlYWRlcnMpXG4gIH1cbn1cblxuaW50ZXJmYWNlIENhY2hlTGlmZXRpbWUge31cbmNvbnN0IENhY2hlZEhlYWRlcnMgPSBuZXcgV2Vha01hcDxDYWNoZUxpZmV0aW1lLCBQcm9taXNlPFJlYWRvbmx5SGVhZGVycz4+KClcblxuZnVuY3Rpb24gbWFrZUhhbmdpbmdIZWFkZXJzKFxuICBwcmVyZW5kZXJTdG9yZTogUHJlcmVuZGVyU3RvcmVNb2Rlcm5cbik6IFByb21pc2U8UmVhZG9ubHlIZWFkZXJzPiB7XG4gIGNvbnN0IGNhY2hlZEhlYWRlcnMgPSBDYWNoZWRIZWFkZXJzLmdldChwcmVyZW5kZXJTdG9yZSlcbiAgaWYgKGNhY2hlZEhlYWRlcnMpIHtcbiAgICByZXR1cm4gY2FjaGVkSGVhZGVyc1xuICB9XG5cbiAgY29uc3QgcHJvbWlzZSA9IG1ha2VIYW5naW5nUHJvbWlzZTxSZWFkb25seUhlYWRlcnM+KFxuICAgIHByZXJlbmRlclN0b3JlLnJlbmRlclNpZ25hbCxcbiAgICAnYGhlYWRlcnMoKWAnXG4gIClcbiAgQ2FjaGVkSGVhZGVycy5zZXQocHJlcmVuZGVyU3RvcmUsIHByb21pc2UpXG5cbiAgcmV0dXJuIHByb21pc2Vcbn1cblxuZnVuY3Rpb24gbWFrZVVudHJhY2tlZEV4b3RpY0hlYWRlcnMoXG4gIHVuZGVybHlpbmdIZWFkZXJzOiBSZWFkb25seUhlYWRlcnNcbik6IFByb21pc2U8UmVhZG9ubHlIZWFkZXJzPiB7XG4gIGNvbnN0IGNhY2hlZEhlYWRlcnMgPSBDYWNoZWRIZWFkZXJzLmdldCh1bmRlcmx5aW5nSGVhZGVycylcbiAgaWYgKGNhY2hlZEhlYWRlcnMpIHtcbiAgICByZXR1cm4gY2FjaGVkSGVhZGVyc1xuICB9XG5cbiAgY29uc3QgcHJvbWlzZSA9IFByb21pc2UucmVzb2x2ZSh1bmRlcmx5aW5nSGVhZGVycylcbiAgQ2FjaGVkSGVhZGVycy5zZXQodW5kZXJseWluZ0hlYWRlcnMsIHByb21pc2UpXG5cbiAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXMocHJvbWlzZSwge1xuICAgIGFwcGVuZDoge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLmFwcGVuZC5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIGRlbGV0ZToge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLmRlbGV0ZS5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIGdldDoge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLmdldC5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIGhhczoge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLmhhcy5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIHNldDoge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLnNldC5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIGdldFNldENvb2tpZToge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLmdldFNldENvb2tpZS5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIGZvckVhY2g6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5mb3JFYWNoLmJpbmQodW5kZXJseWluZ0hlYWRlcnMpLFxuICAgIH0sXG4gICAga2V5czoge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLmtleXMuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICB2YWx1ZXM6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy52YWx1ZXMuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBlbnRyaWVzOiB7XG4gICAgICB2YWx1ZTogdW5kZXJseWluZ0hlYWRlcnMuZW50cmllcy5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIFtTeW1ib2wuaXRlcmF0b3JdOiB7XG4gICAgICB2YWx1ZTogdW5kZXJseWluZ0hlYWRlcnNbU3ltYm9sLml0ZXJhdG9yXS5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICB9IHNhdGlzZmllcyBIZWFkZXJzRXh0ZW5zaW9ucylcblxuICByZXR1cm4gcHJvbWlzZVxufVxuXG5mdW5jdGlvbiBtYWtlVW50cmFja2VkRXhvdGljSGVhZGVyc1dpdGhEZXZXYXJuaW5ncyhcbiAgdW5kZXJseWluZ0hlYWRlcnM6IFJlYWRvbmx5SGVhZGVycyxcbiAgcm91dGU/OiBzdHJpbmdcbik6IFByb21pc2U8UmVhZG9ubHlIZWFkZXJzPiB7XG4gIGNvbnN0IGNhY2hlZEhlYWRlcnMgPSBDYWNoZWRIZWFkZXJzLmdldCh1bmRlcmx5aW5nSGVhZGVycylcbiAgaWYgKGNhY2hlZEhlYWRlcnMpIHtcbiAgICByZXR1cm4gY2FjaGVkSGVhZGVyc1xuICB9XG5cbiAgY29uc3QgcHJvbWlzZSA9IG5ldyBQcm9taXNlPFJlYWRvbmx5SGVhZGVycz4oKHJlc29sdmUpID0+XG4gICAgc2NoZWR1bGVJbW1lZGlhdGUoKCkgPT4gcmVzb2x2ZSh1bmRlcmx5aW5nSGVhZGVycykpXG4gIClcblxuICBDYWNoZWRIZWFkZXJzLnNldCh1bmRlcmx5aW5nSGVhZGVycywgcHJvbWlzZSlcblxuICBPYmplY3QuZGVmaW5lUHJvcGVydGllcyhwcm9taXNlLCB7XG4gICAgYXBwZW5kOiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gYXBwZW5kKCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gYFxcYGhlYWRlcnMoKS5hcHBlbmQoJHtkZXNjcmliZU5hbWVBcmcoYXJndW1lbnRzWzBdKX0sIC4uLilcXGBgXG4gICAgICAgIHN5bmNJT0Rldihyb3V0ZSwgZXhwcmVzc2lvbilcbiAgICAgICAgcmV0dXJuIHVuZGVybHlpbmdIZWFkZXJzLmFwcGVuZC5hcHBseShcbiAgICAgICAgICB1bmRlcmx5aW5nSGVhZGVycyxcbiAgICAgICAgICBhcmd1bWVudHMgYXMgYW55XG4gICAgICAgIClcbiAgICAgIH0sXG4gICAgfSxcbiAgICBkZWxldGU6IHtcbiAgICAgIHZhbHVlOiBmdW5jdGlvbiBfZGVsZXRlKCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gYFxcYGhlYWRlcnMoKS5kZWxldGUoJHtkZXNjcmliZU5hbWVBcmcoYXJndW1lbnRzWzBdKX0pXFxgYFxuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5kZWxldGUuYXBwbHkoXG4gICAgICAgICAgdW5kZXJseWluZ0hlYWRlcnMsXG4gICAgICAgICAgYXJndW1lbnRzIGFzIGFueVxuICAgICAgICApXG4gICAgICB9LFxuICAgIH0sXG4gICAgZ2V0OiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gZ2V0KCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gYFxcYGhlYWRlcnMoKS5nZXQoJHtkZXNjcmliZU5hbWVBcmcoYXJndW1lbnRzWzBdKX0pXFxgYFxuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5nZXQuYXBwbHkodW5kZXJseWluZ0hlYWRlcnMsIGFyZ3VtZW50cyBhcyBhbnkpXG4gICAgICB9LFxuICAgIH0sXG4gICAgaGFzOiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gaGFzKCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gYFxcYGhlYWRlcnMoKS5oYXMoJHtkZXNjcmliZU5hbWVBcmcoYXJndW1lbnRzWzBdKX0pXFxgYFxuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5oYXMuYXBwbHkodW5kZXJseWluZ0hlYWRlcnMsIGFyZ3VtZW50cyBhcyBhbnkpXG4gICAgICB9LFxuICAgIH0sXG4gICAgc2V0OiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gc2V0KCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gYFxcYGhlYWRlcnMoKS5zZXQoJHtkZXNjcmliZU5hbWVBcmcoYXJndW1lbnRzWzBdKX0sIC4uLilcXGBgXG4gICAgICAgIHN5bmNJT0Rldihyb3V0ZSwgZXhwcmVzc2lvbilcbiAgICAgICAgcmV0dXJuIHVuZGVybHlpbmdIZWFkZXJzLnNldC5hcHBseSh1bmRlcmx5aW5nSGVhZGVycywgYXJndW1lbnRzIGFzIGFueSlcbiAgICAgIH0sXG4gICAgfSxcbiAgICBnZXRTZXRDb29raWU6IHtcbiAgICAgIHZhbHVlOiBmdW5jdGlvbiBnZXRTZXRDb29raWUoKSB7XG4gICAgICAgIGNvbnN0IGV4cHJlc3Npb24gPSAnYGhlYWRlcnMoKS5nZXRTZXRDb29raWUoKWAnXG4gICAgICAgIHN5bmNJT0Rldihyb3V0ZSwgZXhwcmVzc2lvbilcbiAgICAgICAgcmV0dXJuIHVuZGVybHlpbmdIZWFkZXJzLmdldFNldENvb2tpZS5hcHBseShcbiAgICAgICAgICB1bmRlcmx5aW5nSGVhZGVycyxcbiAgICAgICAgICBhcmd1bWVudHMgYXMgYW55XG4gICAgICAgIClcbiAgICAgIH0sXG4gICAgfSxcbiAgICBmb3JFYWNoOiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gZm9yRWFjaCgpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9ICdgaGVhZGVycygpLmZvckVhY2goLi4uKWAnXG4gICAgICAgIHN5bmNJT0Rldihyb3V0ZSwgZXhwcmVzc2lvbilcbiAgICAgICAgcmV0dXJuIHVuZGVybHlpbmdIZWFkZXJzLmZvckVhY2guYXBwbHkoXG4gICAgICAgICAgdW5kZXJseWluZ0hlYWRlcnMsXG4gICAgICAgICAgYXJndW1lbnRzIGFzIGFueVxuICAgICAgICApXG4gICAgICB9LFxuICAgIH0sXG4gICAga2V5czoge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uIGtleXMoKSB7XG4gICAgICAgIGNvbnN0IGV4cHJlc3Npb24gPSAnYGhlYWRlcnMoKS5rZXlzKClgJ1xuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5rZXlzLmFwcGx5KHVuZGVybHlpbmdIZWFkZXJzLCBhcmd1bWVudHMgYXMgYW55KVxuICAgICAgfSxcbiAgICB9LFxuICAgIHZhbHVlczoge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uIHZhbHVlcygpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9ICdgaGVhZGVycygpLnZhbHVlcygpYCdcbiAgICAgICAgc3luY0lPRGV2KHJvdXRlLCBleHByZXNzaW9uKVxuICAgICAgICByZXR1cm4gdW5kZXJseWluZ0hlYWRlcnMudmFsdWVzLmFwcGx5KFxuICAgICAgICAgIHVuZGVybHlpbmdIZWFkZXJzLFxuICAgICAgICAgIGFyZ3VtZW50cyBhcyBhbnlcbiAgICAgICAgKVxuICAgICAgfSxcbiAgICB9LFxuICAgIGVudHJpZXM6IHtcbiAgICAgIHZhbHVlOiBmdW5jdGlvbiBlbnRyaWVzKCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gJ2BoZWFkZXJzKCkuZW50cmllcygpYCdcbiAgICAgICAgc3luY0lPRGV2KHJvdXRlLCBleHByZXNzaW9uKVxuICAgICAgICByZXR1cm4gdW5kZXJseWluZ0hlYWRlcnMuZW50cmllcy5hcHBseShcbiAgICAgICAgICB1bmRlcmx5aW5nSGVhZGVycyxcbiAgICAgICAgICBhcmd1bWVudHMgYXMgYW55XG4gICAgICAgIClcbiAgICAgIH0sXG4gICAgfSxcbiAgICBbU3ltYm9sLml0ZXJhdG9yXToge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9ICdgLi4uaGVhZGVycygpYCBvciBzaW1pbGFyIGl0ZXJhdGlvbidcbiAgICAgICAgc3luY0lPRGV2KHJvdXRlLCBleHByZXNzaW9uKVxuICAgICAgICByZXR1cm4gdW5kZXJseWluZ0hlYWRlcnNbU3ltYm9sLml0ZXJhdG9yXS5hcHBseShcbiAgICAgICAgICB1bmRlcmx5aW5nSGVhZGVycyxcbiAgICAgICAgICBhcmd1bWVudHMgYXMgYW55XG4gICAgICAgIClcbiAgICAgIH0sXG4gICAgfSxcbiAgfSBzYXRpc2ZpZXMgSGVhZGVyc0V4dGVuc2lvbnMpXG5cbiAgcmV0dXJuIHByb21pc2Vcbn1cblxuLy8gU2ltaWxhciB0byBgbWFrZVVudHJhY2tlZEV4b3RpY0hlYWRlcnNXaXRoRGV2V2FybmluZ3NgLCBidXQganVzdCBsb2dnaW5nIHRoZVxuLy8gc3luYyBhY2Nlc3Mgd2l0aG91dCBhY3R1YWxseSBkZWZpbmluZyB0aGUgaGVhZGVycyBwcm9wZXJ0aWVzIG9uIHRoZSBwcm9taXNlLlxuZnVuY3Rpb24gbWFrZVVudHJhY2tlZEhlYWRlcnNXaXRoRGV2V2FybmluZ3MoXG4gIHVuZGVybHlpbmdIZWFkZXJzOiBSZWFkb25seUhlYWRlcnMsXG4gIHJvdXRlPzogc3RyaW5nXG4pOiBQcm9taXNlPFJlYWRvbmx5SGVhZGVycz4ge1xuICBjb25zdCBjYWNoZWRIZWFkZXJzID0gQ2FjaGVkSGVhZGVycy5nZXQodW5kZXJseWluZ0hlYWRlcnMpXG4gIGlmIChjYWNoZWRIZWFkZXJzKSB7XG4gICAgcmV0dXJuIGNhY2hlZEhlYWRlcnNcbiAgfVxuXG4gIGNvbnN0IHByb21pc2UgPSBuZXcgUHJvbWlzZTxSZWFkb25seUhlYWRlcnM+KChyZXNvbHZlKSA9PlxuICAgIHNjaGVkdWxlSW1tZWRpYXRlKCgpID0+IHJlc29sdmUodW5kZXJseWluZ0hlYWRlcnMpKVxuICApXG5cbiAgY29uc3QgcHJveGllZFByb21pc2UgPSBuZXcgUHJveHkocHJvbWlzZSwge1xuICAgIGdldCh0YXJnZXQsIHByb3AsIHJlY2VpdmVyKSB7XG4gICAgICBzd2l0Y2ggKHByb3ApIHtcbiAgICAgICAgY2FzZSBTeW1ib2wuaXRlcmF0b3I6IHtcbiAgICAgICAgICB3YXJuRm9yU3luY0FjY2Vzcyhyb3V0ZSwgJ2AuLi5oZWFkZXJzKClgIG9yIHNpbWlsYXIgaXRlcmF0aW9uJylcbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ2FwcGVuZCc6XG4gICAgICAgIGNhc2UgJ2RlbGV0ZSc6XG4gICAgICAgIGNhc2UgJ2dldCc6XG4gICAgICAgIGNhc2UgJ2hhcyc6XG4gICAgICAgIGNhc2UgJ3NldCc6XG4gICAgICAgIGNhc2UgJ2dldFNldENvb2tpZSc6XG4gICAgICAgIGNhc2UgJ2ZvckVhY2gnOlxuICAgICAgICBjYXNlICdrZXlzJzpcbiAgICAgICAgY2FzZSAndmFsdWVzJzpcbiAgICAgICAgY2FzZSAnZW50cmllcyc6IHtcbiAgICAgICAgICB3YXJuRm9yU3luY0FjY2Vzcyhyb3V0ZSwgYFxcYGhlYWRlcnMoKS4ke3Byb3B9XFxgYClcbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG4gICAgICAgIGRlZmF1bHQ6IHtcbiAgICAgICAgICAvLyBXZSBvbmx5IHdhcm4gZm9yIHdlbGwtZGVmaW5lZCBwcm9wZXJ0aWVzIG9mIHRoZSBoZWFkZXJzIG9iamVjdC5cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gUmVmbGVjdEFkYXB0ZXIuZ2V0KHRhcmdldCwgcHJvcCwgcmVjZWl2ZXIpXG4gICAgfSxcbiAgfSlcblxuICBDYWNoZWRIZWFkZXJzLnNldCh1bmRlcmx5aW5nSGVhZGVycywgcHJveGllZFByb21pc2UpXG5cbiAgcmV0dXJuIHByb3hpZWRQcm9taXNlXG59XG5cbmZ1bmN0aW9uIGRlc2NyaWJlTmFtZUFyZyhhcmc6IHVua25vd24pIHtcbiAgcmV0dXJuIHR5cGVvZiBhcmcgPT09ICdzdHJpbmcnID8gYCcke2FyZ30nYCA6ICcuLi4nXG59XG5cbmZ1bmN0aW9uIHN5bmNJT0Rldihyb3V0ZTogc3RyaW5nIHwgdW5kZWZpbmVkLCBleHByZXNzaW9uOiBzdHJpbmcpIHtcbiAgY29uc3Qgd29ya1VuaXRTdG9yZSA9IHdvcmtVbml0QXN5bmNTdG9yYWdlLmdldFN0b3JlKClcbiAgaWYgKFxuICAgIHdvcmtVbml0U3RvcmUgJiZcbiAgICB3b3JrVW5pdFN0b3JlLnR5cGUgPT09ICdyZXF1ZXN0JyAmJlxuICAgIHdvcmtVbml0U3RvcmUucHJlcmVuZGVyUGhhc2UgPT09IHRydWVcbiAgKSB7XG4gICAgLy8gV2hlbiB3ZSdyZSByZW5kZXJpbmcgZHluYW1pY2FsbHkgaW4gZGV2IHdlIG5lZWQgdG8gYWR2YW5jZSBvdXQgb2YgdGhlXG4gICAgLy8gUHJlcmVuZGVyIGVudmlyb25tZW50IHdoZW4gd2UgcmVhZCBSZXF1ZXN0IGRhdGEgc3luY2hyb25vdXNseVxuICAgIGNvbnN0IHJlcXVlc3RTdG9yZSA9IHdvcmtVbml0U3RvcmVcbiAgICB0cmFja1N5bmNocm9ub3VzUmVxdWVzdERhdGFBY2Nlc3NJbkRldihyZXF1ZXN0U3RvcmUpXG4gIH1cbiAgLy8gSW4gYWxsIGNhc2VzIHdlIHdhcm4gbm9ybWFsbHlcbiAgd2FybkZvclN5bmNBY2Nlc3Mocm91dGUsIGV4cHJlc3Npb24pXG59XG5cbmNvbnN0IHdhcm5Gb3JTeW5jQWNjZXNzID0gY3JlYXRlRGVkdXBlZEJ5Q2FsbHNpdGVTZXJ2ZXJFcnJvckxvZ2dlckRldihcbiAgY3JlYXRlSGVhZGVyc0FjY2Vzc0Vycm9yXG4pXG5cbmZ1bmN0aW9uIGNyZWF0ZUhlYWRlcnNBY2Nlc3NFcnJvcihcbiAgcm91dGU6IHN0cmluZyB8IHVuZGVmaW5lZCxcbiAgZXhwcmVzc2lvbjogc3RyaW5nXG4pIHtcbiAgY29uc3QgcHJlZml4ID0gcm91dGUgPyBgUm91dGUgXCIke3JvdXRlfVwiIGAgOiAnVGhpcyByb3V0ZSAnXG4gIHJldHVybiBuZXcgRXJyb3IoXG4gICAgYCR7cHJlZml4fXVzZWQgJHtleHByZXNzaW9ufS4gYCArXG4gICAgICBgXFxgaGVhZGVycygpXFxgIHNob3VsZCBiZSBhd2FpdGVkIGJlZm9yZSB1c2luZyBpdHMgdmFsdWUuIGAgK1xuICAgICAgYExlYXJuIG1vcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL3N5bmMtZHluYW1pYy1hcGlzYFxuICApXG59XG5cbnR5cGUgSGVhZGVyc0V4dGVuc2lvbnMgPSB7XG4gIFtLIGluIGtleW9mIFJlYWRvbmx5SGVhZGVyc106IHVua25vd25cbn1cbiJdLCJuYW1lcyI6WyJoZWFkZXJzIiwid29ya1N0b3JlIiwid29ya0FzeW5jU3RvcmFnZSIsImdldFN0b3JlIiwid29ya1VuaXRTdG9yZSIsIndvcmtVbml0QXN5bmNTdG9yYWdlIiwicGhhc2UiLCJpc1JlcXVlc3RBUElDYWxsYWJsZUluc2lkZUFmdGVyIiwiRXJyb3IiLCJyb3V0ZSIsImZvcmNlU3RhdGljIiwidW5kZXJseWluZ0hlYWRlcnMiLCJIZWFkZXJzQWRhcHRlciIsInNlYWwiLCJIZWFkZXJzIiwibWFrZVVudHJhY2tlZEV4b3RpY0hlYWRlcnMiLCJ0eXBlIiwiZHluYW1pY1Nob3VsZEVycm9yIiwiU3RhdGljR2VuQmFpbG91dEVycm9yIiwibWFrZUhhbmdpbmdIZWFkZXJzIiwiZXhwb3J0TmFtZSIsIkludmFyaWFudEVycm9yIiwicG9zdHBvbmVXaXRoVHJhY2tpbmciLCJkeW5hbWljVHJhY2tpbmciLCJ0aHJvd1RvSW50ZXJydXB0U3RhdGljR2VuZXJhdGlvbiIsInRyYWNrRHluYW1pY0RhdGFJbkR5bmFtaWNSZW5kZXIiLCJyZXF1ZXN0U3RvcmUiLCJnZXRFeHBlY3RlZFJlcXVlc3RTdG9yZSIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImlzUHJlZmV0Y2hSZXF1ZXN0IiwiX19ORVhUX0RZTkFNSUNfSU8iLCJtYWtlVW50cmFja2VkSGVhZGVyc1dpdGhEZXZXYXJuaW5ncyIsIm1ha2VVbnRyYWNrZWRFeG90aWNIZWFkZXJzV2l0aERldldhcm5pbmdzIiwiQ2FjaGVkSGVhZGVycyIsIldlYWtNYXAiLCJwcmVyZW5kZXJTdG9yZSIsImNhY2hlZEhlYWRlcnMiLCJnZXQiLCJwcm9taXNlIiwibWFrZUhhbmdpbmdQcm9taXNlIiwicmVuZGVyU2lnbmFsIiwic2V0IiwiUHJvbWlzZSIsInJlc29sdmUiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0aWVzIiwiYXBwZW5kIiwidmFsdWUiLCJiaW5kIiwiZGVsZXRlIiwiaGFzIiwiZ2V0U2V0Q29va2llIiwiZm9yRWFjaCIsImtleXMiLCJ2YWx1ZXMiLCJlbnRyaWVzIiwiU3ltYm9sIiwiaXRlcmF0b3IiLCJzY2hlZHVsZUltbWVkaWF0ZSIsImV4cHJlc3Npb24iLCJkZXNjcmliZU5hbWVBcmciLCJhcmd1bWVudHMiLCJzeW5jSU9EZXYiLCJhcHBseSIsIl9kZWxldGUiLCJwcm94aWVkUHJvbWlzZSIsIlByb3h5IiwidGFyZ2V0IiwicHJvcCIsInJlY2VpdmVyIiwid2FybkZvclN5bmNBY2Nlc3MiLCJSZWZsZWN0QWRhcHRlciIsImFyZyIsInByZXJlbmRlclBoYXNlIiwidHJhY2tTeW5jaHJvbm91c1JlcXVlc3REYXRhQWNjZXNzSW5EZXYiLCJjcmVhdGVEZWR1cGVkQnlDYWxsc2l0ZVNlcnZlckVycm9yTG9nZ2VyRGV2IiwiY3JlYXRlSGVhZGVyc0FjY2Vzc0Vycm9yIiwicHJlZml4Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/utils.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/server/request/utils.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isRequestAPICallableInsideAfter: function() {\n        return isRequestAPICallableInsideAfter;\n    },\n    throwForSearchParamsAccessInUseCache: function() {\n        return throwForSearchParamsAccessInUseCache;\n    },\n    throwWithStaticGenerationBailoutError: function() {\n        return throwWithStaticGenerationBailoutError;\n    },\n    throwWithStaticGenerationBailoutErrorWithDynamicError: function() {\n        return throwWithStaticGenerationBailoutErrorWithDynamicError;\n    }\n});\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _aftertaskasyncstorageexternal = __webpack_require__(/*! ../app-render/after-task-async-storage.external */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js\");\nfunction throwWithStaticGenerationBailoutError(route, expression) {\n    throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(\"Route \".concat(route, \" couldn't be rendered statically because it used \").concat(expression, \". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\")), \"__NEXT_ERROR_CODE\", {\n        value: \"E576\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction throwWithStaticGenerationBailoutErrorWithDynamicError(route, expression) {\n    throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(\"Route \".concat(route, ' with `dynamic = \"error\"` couldn\\'t be rendered statically because it used ').concat(expression, \". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering\")), \"__NEXT_ERROR_CODE\", {\n        value: \"E543\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction throwForSearchParamsAccessInUseCache(workStore, constructorOpt) {\n    var _workStore;\n    const error = Object.defineProperty(new Error(\"Route \".concat(workStore.route, ' used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')), \"__NEXT_ERROR_CODE\", {\n        value: \"E634\",\n        enumerable: false,\n        configurable: true\n    });\n    Error.captureStackTrace(error, constructorOpt);\n    var _invalidDynamicUsageError;\n    (_invalidDynamicUsageError = (_workStore = workStore).invalidDynamicUsageError) !== null && _invalidDynamicUsageError !== void 0 ? _invalidDynamicUsageError : _workStore.invalidDynamicUsageError = error;\n    throw error;\n}\nfunction isRequestAPICallableInsideAfter() {\n    const afterTaskStore = _aftertaskasyncstorageexternal.afterTaskAsyncStorage.getStore();\n    return (afterTaskStore == null ? void 0 : afterTaskStore.rootTaskSpawnPhase) === 'action';\n} //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/headers.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/headers.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    HeadersAdapter: function() {\n        return HeadersAdapter;\n    },\n    ReadonlyHeadersError: function() {\n        return ReadonlyHeadersError;\n    }\n});\nconst _reflect = __webpack_require__(/*! ./reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nclass ReadonlyHeadersError extends Error {\n    constructor(){\n        super('Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers');\n    }\n    static callable() {\n        throw new ReadonlyHeadersError();\n    }\n}\nclass HeadersAdapter extends Headers {\n    constructor(headers){\n        // We've already overridden the methods that would be called, so we're just\n        // calling the super constructor to ensure that the instanceof check works.\n        super();\n        this.headers = new Proxy(headers, {\n            get (target, prop, receiver) {\n                // Because this is just an object, we expect that all \"get\" operations\n                // are for properties. If it's a \"get\" for a symbol, we'll just return\n                // the symbol.\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return undefined.\n                if (typeof original === 'undefined') return;\n                // If the original casing exists, return the value.\n                return _reflect.ReflectAdapter.get(target, original, receiver);\n            },\n            set (target, prop, value, receiver) {\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, use the prop as the key.\n                return _reflect.ReflectAdapter.set(target, original ?? prop, value, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.has(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return false.\n                if (typeof original === 'undefined') return false;\n                // If the original casing exists, return true.\n                return _reflect.ReflectAdapter.has(target, original);\n            },\n            deleteProperty (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.deleteProperty(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return true.\n                if (typeof original === 'undefined') return true;\n                // If the original casing exists, delete the property.\n                return _reflect.ReflectAdapter.deleteProperty(target, original);\n            }\n        });\n    }\n    /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */ static seal(headers) {\n        return new Proxy(headers, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'append':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyHeadersError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n    /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */ merge(value) {\n        if (Array.isArray(value)) return value.join(', ');\n        return value;\n    }\n    /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */ static from(headers) {\n        if (headers instanceof Headers) return headers;\n        return new HeadersAdapter(headers);\n    }\n    append(name, value) {\n        const existing = this.headers[name];\n        if (typeof existing === 'string') {\n            this.headers[name] = [\n                existing,\n                value\n            ];\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            this.headers[name] = value;\n        }\n    }\n    delete(name) {\n        delete this.headers[name];\n    }\n    get(name) {\n        const value = this.headers[name];\n        if (typeof value !== 'undefined') return this.merge(value);\n        return null;\n    }\n    has(name) {\n        return typeof this.headers[name] !== 'undefined';\n    }\n    set(name, value) {\n        this.headers[name] = value;\n    }\n    forEach(callbackfn, thisArg) {\n        for (const [name, value] of this.entries()){\n            callbackfn.call(thisArg, value, name, this);\n        }\n    }\n    *entries() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(name);\n            yield [\n                name,\n                value\n            ];\n        }\n    }\n    *keys() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            yield name;\n        }\n    }\n    *values() {\n        for (const key of Object.keys(this.headers)){\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(key);\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n}\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ReflectAdapter\", ({\n    enumerable: true,\n    get: function() {\n        return ReflectAdapter;\n    }\n}));\nclass ReflectAdapter {\n    static get(target, prop, receiver) {\n        const value = Reflect.get(target, prop, receiver);\n        if (typeof value === 'function') {\n            return value.bind(target);\n        }\n        return value;\n    }\n    static set(target, prop, value, receiver) {\n        return Reflect.set(target, prop, value, receiver);\n    }\n    static has(target, prop) {\n        return Reflect.has(target, prop);\n    }\n    static deleteProperty(target, prop) {\n        return Reflect.deleteProperty(target, prop);\n    }\n}\n\n//# sourceMappingURL=reflect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9yZWZsZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0RBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxuZXdcXGRvY3VtZW50LXJlcG9zaXRvcnlcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2VydmVyXFx3ZWJcXHNwZWMtZXh0ZW5zaW9uXFxhZGFwdGVyc1xccmVmbGVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlJlZmxlY3RBZGFwdGVyXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0QWRhcHRlcjtcbiAgICB9XG59KTtcbmNsYXNzIFJlZmxlY3RBZGFwdGVyIHtcbiAgICBzdGF0aWMgZ2V0KHRhcmdldCwgcHJvcCwgcmVjZWl2ZXIpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBSZWZsZWN0LmdldCh0YXJnZXQsIHByb3AsIHJlY2VpdmVyKTtcbiAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlLmJpbmQodGFyZ2V0KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIHN0YXRpYyBzZXQodGFyZ2V0LCBwcm9wLCB2YWx1ZSwgcmVjZWl2ZXIpIHtcbiAgICAgICAgcmV0dXJuIFJlZmxlY3Quc2V0KHRhcmdldCwgcHJvcCwgdmFsdWUsIHJlY2VpdmVyKTtcbiAgICB9XG4gICAgc3RhdGljIGhhcyh0YXJnZXQsIHByb3ApIHtcbiAgICAgICAgcmV0dXJuIFJlZmxlY3QuaGFzKHRhcmdldCwgcHJvcCk7XG4gICAgfVxuICAgIHN0YXRpYyBkZWxldGVQcm9wZXJ0eSh0YXJnZXQsIHByb3ApIHtcbiAgICAgICAgcmV0dXJuIFJlZmxlY3QuZGVsZXRlUHJvcGVydHkodGFyZ2V0LCBwcm9wKTtcbiAgICB9XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZmxlY3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MutableRequestCookiesAdapter: function() {\n        return MutableRequestCookiesAdapter;\n    },\n    ReadonlyRequestCookiesError: function() {\n        return ReadonlyRequestCookiesError;\n    },\n    RequestCookiesAdapter: function() {\n        return RequestCookiesAdapter;\n    },\n    appendMutableCookies: function() {\n        return appendMutableCookies;\n    },\n    areCookiesMutableInCurrentPhase: function() {\n        return areCookiesMutableInCurrentPhase;\n    },\n    getModifiedCookieValues: function() {\n        return getModifiedCookieValues;\n    },\n    responseCookiesToRequestCookies: function() {\n        return responseCookiesToRequestCookies;\n    },\n    wrapWithMutableAccessCheck: function() {\n        return wrapWithMutableAccessCheck;\n    }\n});\nconst _cookies = __webpack_require__(/*! ../cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js\");\nconst _reflect = __webpack_require__(/*! ./reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../../../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../../../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nclass ReadonlyRequestCookiesError extends Error {\n    constructor(){\n        super('Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options');\n    }\n    static callable() {\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nclass RequestCookiesAdapter {\n    static seal(cookies) {\n        return new Proxy(cookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'clear':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyRequestCookiesError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n}\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies');\nfunction getModifiedCookieValues(cookies) {\n    const modified = cookies[SYMBOL_MODIFY_COOKIE_VALUES];\n    if (!modified || !Array.isArray(modified) || modified.length === 0) {\n        return [];\n    }\n    return modified;\n}\nfunction appendMutableCookies(headers, mutableCookies) {\n    const modifiedCookieValues = getModifiedCookieValues(mutableCookies);\n    if (modifiedCookieValues.length === 0) {\n        return false;\n    }\n    // Return a new response that extends the response with\n    // the modified cookies as fallbacks. `res` cookies\n    // will still take precedence.\n    const resCookies = new _cookies.ResponseCookies(headers);\n    const returnedCookies = resCookies.getAll();\n    // Set the modified cookies as fallbacks.\n    for (const cookie of modifiedCookieValues){\n        resCookies.set(cookie);\n    }\n    // Set the original cookies as the final values.\n    for (const cookie of returnedCookies){\n        resCookies.set(cookie);\n    }\n    return true;\n}\nclass MutableRequestCookiesAdapter {\n    static wrap(cookies, onUpdateCookies) {\n        const responseCookies = new _cookies.ResponseCookies(new Headers());\n        for (const cookie of cookies.getAll()){\n            responseCookies.set(cookie);\n        }\n        let modifiedValues = [];\n        const modifiedCookies = new Set();\n        const updateResponseCookies = ()=>{\n            // TODO-APP: change method of getting workStore\n            const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n            if (workStore) {\n                workStore.pathWasRevalidated = true;\n            }\n            const allCookies = responseCookies.getAll();\n            modifiedValues = allCookies.filter((c)=>modifiedCookies.has(c.name));\n            if (onUpdateCookies) {\n                const serializedCookies = [];\n                for (const cookie of modifiedValues){\n                    const tempCookies = new _cookies.ResponseCookies(new Headers());\n                    tempCookies.set(cookie);\n                    serializedCookies.push(tempCookies.toString());\n                }\n                onUpdateCookies(serializedCookies);\n            }\n        };\n        const wrappedCookies = new Proxy(responseCookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    // A special symbol to get the modified cookie values\n                    case SYMBOL_MODIFY_COOKIE_VALUES:\n                        return modifiedValues;\n                    // TODO: Throw error if trying to set a cookie after the response\n                    // headers have been set.\n                    case 'delete':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.delete(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    case 'set':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.set(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n        return wrappedCookies;\n    }\n}\nfunction wrapWithMutableAccessCheck(responseCookies) {\n    const wrappedCookies = new Proxy(responseCookies, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'delete':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().delete');\n                        target.delete(...args);\n                        return wrappedCookies;\n                    };\n                case 'set':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().set');\n                        target.set(...args);\n                        return wrappedCookies;\n                    };\n                default:\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n        }\n    });\n    return wrappedCookies;\n}\nfunction areCookiesMutableInCurrentPhase(requestStore) {\n    return requestStore.phase === 'action';\n}\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */ function ensureCookiesAreStillMutable(callingExpression) {\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    if (!areCookiesMutableInCurrentPhase(requestStore)) {\n        // TODO: maybe we can give a more precise error message based on callingExpression?\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nfunction responseCookiesToRequestCookies(responseCookies) {\n    const requestCookies = new _cookies.RequestCookies(new Headers());\n    for (const cookie of responseCookies.getAll()){\n        requestCookies.set(cookie);\n    }\n    return requestCookies;\n}\n\n//# sourceMappingURL=request-cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/cookies.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    RequestCookies: function() {\n        return _cookies.RequestCookies;\n    },\n    ResponseCookies: function() {\n        return _cookies.ResponseCookies;\n    },\n    stringifyCookie: function() {\n        return _cookies.stringifyCookie;\n    }\n});\nconst _cookies = __webpack_require__(/*! next/dist/compiled/@edge-runtime/cookies */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js\");\n\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9jb29raWVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsTUFBTSxDQUlMO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsaUJBQWlCLG1CQUFPLENBQUMsc0lBQTBDOztBQUVuRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcbmV3XFxkb2N1bWVudC1yZXBvc2l0b3J5XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcd2ViXFxzcGVjLWV4dGVuc2lvblxcY29va2llcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbjAgJiYgKG1vZHVsZS5leHBvcnRzID0ge1xuICAgIFJlcXVlc3RDb29raWVzOiBudWxsLFxuICAgIFJlc3BvbnNlQ29va2llczogbnVsbCxcbiAgICBzdHJpbmdpZnlDb29raWU6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgUmVxdWVzdENvb2tpZXM6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX2Nvb2tpZXMuUmVxdWVzdENvb2tpZXM7XG4gICAgfSxcbiAgICBSZXNwb25zZUNvb2tpZXM6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX2Nvb2tpZXMuUmVzcG9uc2VDb29raWVzO1xuICAgIH0sXG4gICAgc3RyaW5naWZ5Q29va2llOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9jb29raWVzLnN0cmluZ2lmeUNvb2tpZTtcbiAgICB9XG59KTtcbmNvbnN0IF9jb29raWVzID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9AZWRnZS1ydW50aW1lL2Nvb2tpZXNcIik7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvb2tpZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/invariant-error.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"InvariantError\", ({\n    enumerable: true,\n    get: function() {\n        return InvariantError;\n    }\n}));\nclass InvariantError extends Error {\n    constructor(message, options){\n        super(\"Invariant: \" + (message.endsWith('.') ? message : message + '.') + \" This is a bug in Next.js.\", options);\n        this.name = 'InvariantError';\n    }\n} //# sourceMappingURL=invariant-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9pbnZhcmlhbnQtZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztrREFBYUE7OztlQUFBQTs7O0FBQU4sTUFBTUEsdUJBQXVCQztJQUNsQ0MsWUFBWUMsT0FBZSxFQUFFQyxPQUFzQixDQUFFO1FBQ25ELEtBQUssQ0FDRixnQkFBYUQsQ0FBQUEsUUFBUUUsUUFBUSxDQUFDLE9BQU9GLFVBQVVBLFVBQVUsSUFBRSxHQUFFLDhCQUM5REM7UUFFRixJQUFJLENBQUNFLElBQUksR0FBRztJQUNkO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcc3JjXFxzaGFyZWRcXGxpYlxcaW52YXJpYW50LWVycm9yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBJbnZhcmlhbnRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogRXJyb3JPcHRpb25zKSB7XG4gICAgc3VwZXIoXG4gICAgICBgSW52YXJpYW50OiAke21lc3NhZ2UuZW5kc1dpdGgoJy4nKSA/IG1lc3NhZ2UgOiBtZXNzYWdlICsgJy4nfSBUaGlzIGlzIGEgYnVnIGluIE5leHQuanMuYCxcbiAgICAgIG9wdGlvbnNcbiAgICApXG4gICAgdGhpcy5uYW1lID0gJ0ludmFyaWFudEVycm9yJ1xuICB9XG59XG4iXSwibmFtZXMiOlsiSW52YXJpYW50RXJyb3IiLCJFcnJvciIsImNvbnN0cnVjdG9yIiwibWVzc2FnZSIsIm9wdGlvbnMiLCJlbmRzV2l0aCIsIm5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/client/components/app-router-headers.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-headers.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_HEADER: function() {\n        return ACTION_HEADER;\n    },\n    FLIGHT_HEADERS: function() {\n        return FLIGHT_HEADERS;\n    },\n    NEXT_ACTION_NOT_FOUND_HEADER: function() {\n        return NEXT_ACTION_NOT_FOUND_HEADER;\n    },\n    NEXT_DID_POSTPONE_HEADER: function() {\n        return NEXT_DID_POSTPONE_HEADER;\n    },\n    NEXT_HMR_REFRESH_HASH_COOKIE: function() {\n        return NEXT_HMR_REFRESH_HASH_COOKIE;\n    },\n    NEXT_HMR_REFRESH_HEADER: function() {\n        return NEXT_HMR_REFRESH_HEADER;\n    },\n    NEXT_IS_PRERENDER_HEADER: function() {\n        return NEXT_IS_PRERENDER_HEADER;\n    },\n    NEXT_REWRITTEN_PATH_HEADER: function() {\n        return NEXT_REWRITTEN_PATH_HEADER;\n    },\n    NEXT_REWRITTEN_QUERY_HEADER: function() {\n        return NEXT_REWRITTEN_QUERY_HEADER;\n    },\n    NEXT_ROUTER_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_SEGMENT_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_STALE_TIME_HEADER: function() {\n        return NEXT_ROUTER_STALE_TIME_HEADER;\n    },\n    NEXT_ROUTER_STATE_TREE_HEADER: function() {\n        return NEXT_ROUTER_STATE_TREE_HEADER;\n    },\n    NEXT_RSC_UNION_QUERY: function() {\n        return NEXT_RSC_UNION_QUERY;\n    },\n    NEXT_URL: function() {\n        return NEXT_URL;\n    },\n    RSC_CONTENT_TYPE_HEADER: function() {\n        return RSC_CONTENT_TYPE_HEADER;\n    },\n    RSC_HEADER: function() {\n        return RSC_HEADER;\n    }\n});\nconst RSC_HEADER = 'RSC';\nconst ACTION_HEADER = 'Next-Action';\nconst NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree';\nconst NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch';\nconst NEXT_ROUTER_SEGMENT_PREFETCH_HEADER = 'Next-Router-Segment-Prefetch';\nconst NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh';\nconst NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__';\nconst NEXT_URL = 'Next-Url';\nconst RSC_CONTENT_TYPE_HEADER = 'text/x-component';\nconst FLIGHT_HEADERS = [\n    RSC_HEADER,\n    NEXT_ROUTER_STATE_TREE_HEADER,\n    NEXT_ROUTER_PREFETCH_HEADER,\n    NEXT_HMR_REFRESH_HEADER,\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n];\nconst NEXT_RSC_UNION_QUERY = '_rsc';\nconst NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time';\nconst NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed';\nconst NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path';\nconst NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query';\nconst NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender';\nconst NEXT_ACTION_NOT_FOUND_HEADER = 'x-nextjs-action-not-found';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-headers.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/client/components/app-router-headers.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage-instance.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDREQUEyRDtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDJCQUEyQixtQkFBTyxDQUFDLHlHQUF1QjtBQUMxRDs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcbmV3XFxkb2N1bWVudC1yZXBvc2l0b3J5XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcd29yay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwid29ya0FzeW5jU3RvcmFnZUluc3RhbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3b3JrQXN5bmNTdG9yYWdlSW5zdGFuY2U7XG4gICAgfVxufSk7XG5jb25zdCBfYXN5bmNsb2NhbHN0b3JhZ2UgPSByZXF1aXJlKFwiLi9hc3luYy1sb2NhbC1zdG9yYWdlXCIpO1xuY29uc3Qgd29ya0FzeW5jU3RvcmFnZUluc3RhbmNlID0gKDAsIF9hc3luY2xvY2Fsc3RvcmFnZS5jcmVhdGVBc3luY0xvY2FsU3RvcmFnZSkoKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29yay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage.external.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _workasyncstorageinstance.workAsyncStorageInstance;\n    }\n}));\nconst _workasyncstorageinstance = __webpack_require__(/*! ./work-async-storage-instance */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\");\n\n//# sourceMappingURL=work-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLG9EQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLGtDQUFrQyxtQkFBTyxDQUFDLHlIQUErQjs7QUFFekUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXG5ld1xcZG9jdW1lbnQtcmVwb3NpdG9yeVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXHdvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndvcmtBc3luY1N0b3JhZ2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF93b3JrYXN5bmNzdG9yYWdlaW5zdGFuY2Uud29ya0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX3dvcmthc3luY3N0b3JhZ2VpbnN0YW5jZSA9IHJlcXVpcmUoXCIuL3dvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZVwiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workUnitAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workUnitAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workUnitAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-unit-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstdW5pdC1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsZ0VBQStEO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsMkJBQTJCLG1CQUFPLENBQUMseUdBQXVCO0FBQzFEOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxuZXdcXGRvY3VtZW50LXJlcG9zaXRvcnlcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2VydmVyXFxhcHAtcmVuZGVyXFx3b3JrLXVuaXQtYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndvcmtVbml0QXN5bmNTdG9yYWdlSW5zdGFuY2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHdvcmtVbml0QXN5bmNTdG9yYWdlSW5zdGFuY2U7XG4gICAgfVxufSk7XG5jb25zdCBfYXN5bmNsb2NhbHN0b3JhZ2UgPSByZXF1aXJlKFwiLi9hc3luYy1sb2NhbC1zdG9yYWdlXCIpO1xuY29uc3Qgd29ya1VuaXRBc3luY1N0b3JhZ2VJbnN0YW5jZSA9ICgwLCBfYXN5bmNsb2NhbHN0b3JhZ2UuY3JlYXRlQXN5bmNMb2NhbFN0b3JhZ2UpKCk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdvcmstdW5pdC1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getDraftModeProviderForCacheScope: function() {\n        return getDraftModeProviderForCacheScope;\n    },\n    getExpectedRequestStore: function() {\n        return getExpectedRequestStore;\n    },\n    getHmrRefreshHash: function() {\n        return getHmrRefreshHash;\n    },\n    getPrerenderResumeDataCache: function() {\n        return getPrerenderResumeDataCache;\n    },\n    getRenderResumeDataCache: function() {\n        return getRenderResumeDataCache;\n    },\n    throwForMissingRequestStore: function() {\n        return throwForMissingRequestStore;\n    },\n    workUnitAsyncStorage: function() {\n        return _workunitasyncstorageinstance.workUnitAsyncStorageInstance;\n    }\n});\nconst _workunitasyncstorageinstance = __webpack_require__(/*! ./work-unit-async-storage-instance */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js\");\nconst _approuterheaders = __webpack_require__(/*! ../../client/components/app-router-headers */ \"(shared)/./node_modules/next/dist/client/components/app-router-headers.js\");\nfunction getExpectedRequestStore(callingExpression) {\n    const workUnitStore = _workunitasyncstorageinstance.workUnitAsyncStorageInstance.getStore();\n    if (!workUnitStore) {\n        throwForMissingRequestStore(callingExpression);\n    }\n    switch(workUnitStore.type){\n        case 'request':\n            return workUnitStore;\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // This should not happen because we should have checked it already.\n            throw Object.defineProperty(new Error(\"`\".concat(callingExpression, \"` cannot be called inside a prerender. This is a bug in Next.js.\")), \"__NEXT_ERROR_CODE\", {\n                value: \"E401\",\n                enumerable: false,\n                configurable: true\n            });\n        case 'cache':\n            throw Object.defineProperty(new Error(\"`\".concat(callingExpression, '` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache')), \"__NEXT_ERROR_CODE\", {\n                value: \"E37\",\n                enumerable: false,\n                configurable: true\n            });\n        case 'unstable-cache':\n            throw Object.defineProperty(new Error(\"`\".concat(callingExpression, \"` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache\")), \"__NEXT_ERROR_CODE\", {\n                value: \"E69\",\n                enumerable: false,\n                configurable: true\n            });\n        default:\n            const _exhaustiveCheck = workUnitStore;\n            return _exhaustiveCheck;\n    }\n}\nfunction throwForMissingRequestStore(callingExpression) {\n    throw Object.defineProperty(new Error(\"`\".concat(callingExpression, \"` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context\")), \"__NEXT_ERROR_CODE\", {\n        value: \"E251\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction getPrerenderResumeDataCache(workUnitStore) {\n    if (workUnitStore.type === 'prerender' || // TODO eliminate fetch caching in client scope and stop exposing this data cache during SSR\n    workUnitStore.type === 'prerender-client' || workUnitStore.type === 'prerender-ppr') {\n        return workUnitStore.prerenderResumeDataCache;\n    }\n    return null;\n}\nfunction getRenderResumeDataCache(workUnitStore) {\n    switch(workUnitStore.type){\n        case 'request':\n            return workUnitStore.renderResumeDataCache;\n        case 'prerender':\n        case 'prerender-client':\n            if (workUnitStore.renderResumeDataCache) {\n                // If we are in a prerender, we might have a render resume data cache\n                // that is used to read from prefilled caches.\n                return workUnitStore.renderResumeDataCache;\n            }\n        // fallthrough\n        case 'prerender-ppr':\n            // Otherwise we return the mutable resume data cache here as an immutable\n            // version of the cache as it can also be used for reading.\n            return workUnitStore.prerenderResumeDataCache;\n        default:\n            return null;\n    }\n}\nfunction getHmrRefreshHash(workStore, workUnitStore) {\n    var _workUnitStore_cookies_get;\n    if (!workStore.dev) {\n        return undefined;\n    }\n    return workUnitStore.type === 'cache' || workUnitStore.type === 'prerender' ? workUnitStore.hmrRefreshHash : workUnitStore.type === 'request' ? (_workUnitStore_cookies_get = workUnitStore.cookies.get(_approuterheaders.NEXT_HMR_REFRESH_HASH_COOKIE)) == null ? void 0 : _workUnitStore_cookies_get.value : undefined;\n}\nfunction getDraftModeProviderForCacheScope(workStore, workUnitStore) {\n    if (workStore.isDraftMode) {\n        switch(workUnitStore.type){\n            case 'cache':\n            case 'unstable-cache':\n            case 'request':\n                return workUnitStore.draftMode;\n            default:\n                return undefined;\n        }\n    }\n    return undefined;\n} //# sourceMappingURL=work-unit-async-storage.external.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\n"));

/***/ })

}]);