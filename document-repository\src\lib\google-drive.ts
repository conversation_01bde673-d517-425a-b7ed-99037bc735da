import { google } from 'googleapis';
import { Readable } from 'stream';

export class GoogleDriveService {
  private drive: any;
  private auth: any;

  constructor() {
    this.auth = new google.auth.OAuth2(
      process.env.GOOGLE_DRIVE_CLIENT_ID,
      process.env.GOOGLE_DRIVE_CLIENT_SECRET,
      process.env.GOOGLE_DRIVE_REDIRECT_URI
    );

    this.auth.setCredentials({
      refresh_token: process.env.GOOGLE_DRIVE_REFRESH_TOKEN,
    });

    this.drive = google.drive({ version: 'v3', auth: this.auth });
  }

  async uploadFile(
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string,
    folderId?: string
  ): Promise<{ id: string; webViewLink: string; webContentLink: string }> {
    try {
      const fileMetadata = {
        name: fileName,
        parents: folderId ? [folderId] : [process.env.GOOGLE_DRIVE_FOLDER_ID],
      };

      const media = {
        mimeType: mimeType,
        body: Readable.from(fileBuffer),
      };

      const response = await this.drive.files.create({
        resource: fileMetadata,
        media: media,
        fields: 'id,webViewLink,webContentLink',
      });

      // Make the file publicly accessible
      await this.drive.permissions.create({
        fileId: response.data.id,
        resource: {
          role: 'reader',
          type: 'anyone',
        },
      });

      return {
        id: response.data.id,
        webViewLink: response.data.webViewLink,
        webContentLink: response.data.webContentLink,
      };
    } catch (error) {
      console.error('Error uploading file to Google Drive:', error);
      throw new Error('Failed to upload file to Google Drive');
    }
  }

  async downloadFile(fileId: string): Promise<Buffer> {
    try {
      const response = await this.drive.files.get({
        fileId: fileId,
        alt: 'media',
      });

      return Buffer.from(response.data);
    } catch (error) {
      console.error('Error downloading file from Google Drive:', error);
      throw new Error('Failed to download file from Google Drive');
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      await this.drive.files.delete({
        fileId: fileId,
      });
    } catch (error) {
      console.error('Error deleting file from Google Drive:', error);
      throw new Error('Failed to delete file from Google Drive');
    }
  }

  async getFileMetadata(fileId: string) {
    try {
      const response = await this.drive.files.get({
        fileId: fileId,
        fields: 'id,name,size,mimeType,createdTime,modifiedTime,webViewLink,webContentLink',
      });

      return response.data;
    } catch (error) {
      console.error('Error getting file metadata from Google Drive:', error);
      throw new Error('Failed to get file metadata from Google Drive');
    }
  }

  async createFolder(name: string, parentFolderId?: string): Promise<string> {
    try {
      const fileMetadata = {
        name: name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: parentFolderId ? [parentFolderId] : [process.env.GOOGLE_DRIVE_FOLDER_ID],
      };

      const response = await this.drive.files.create({
        resource: fileMetadata,
        fields: 'id',
      });

      return response.data.id;
    } catch (error) {
      console.error('Error creating folder in Google Drive:', error);
      throw new Error('Failed to create folder in Google Drive');
    }
  }

  async listFiles(folderId?: string, pageSize: number = 10, pageToken?: string) {
    try {
      const query = folderId 
        ? `'${folderId}' in parents and trashed=false`
        : `'${process.env.GOOGLE_DRIVE_FOLDER_ID}' in parents and trashed=false`;

      const response = await this.drive.files.list({
        q: query,
        pageSize: pageSize,
        pageToken: pageToken,
        fields: 'nextPageToken, files(id,name,size,mimeType,createdTime,modifiedTime,webViewLink)',
        orderBy: 'modifiedTime desc',
      });

      return {
        files: response.data.files,
        nextPageToken: response.data.nextPageToken,
      };
    } catch (error) {
      console.error('Error listing files from Google Drive:', error);
      throw new Error('Failed to list files from Google Drive');
    }
  }

  async updateFilePermissions(fileId: string, isPublic: boolean): Promise<void> {
    try {
      if (isPublic) {
        await this.drive.permissions.create({
          fileId: fileId,
          resource: {
            role: 'reader',
            type: 'anyone',
          },
        });
      } else {
        // Get all permissions
        const permissions = await this.drive.permissions.list({
          fileId: fileId,
        });

        // Remove public permissions
        for (const permission of permissions.data.permissions) {
          if (permission.type === 'anyone') {
            await this.drive.permissions.delete({
              fileId: fileId,
              permissionId: permission.id,
            });
          }
        }
      }
    } catch (error) {
      console.error('Error updating file permissions in Google Drive:', error);
      throw new Error('Failed to update file permissions in Google Drive');
    }
  }

  generateDirectDownloadLink(fileId: string): string {
    return `https://drive.google.com/uc?export=download&id=${fileId}`;
  }

  generatePreviewLink(fileId: string): string {
    return `https://drive.google.com/file/d/${fileId}/preview`;
  }
}

export const googleDriveService = new GoogleDriveService();
